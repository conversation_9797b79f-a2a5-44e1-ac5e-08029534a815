import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { useState } from "react";
import {
  Alert,
  Linking,
  Platform,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Button from "../../components/Button";
import HeaderComponent from "../../components/HeaderComponent";
import { borderRadius, colors, shadows, spacing, typography } from "../../constants/theme";

export default function SettingsScreen() {
  const [darkMode, setDarkMode] = useState(false);
  const [autoSave, setAutoSave] = useState(true);
  const [saveHistory, setSaveHistory] = useState(true);

  const handleClearAllData = () => {
    Alert.alert(
      "Clear All Data",
      "Are you sure you want to clear all data? This will delete all resumes, history, and settings. This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Clear All Data",
          style: "destructive",
          onPress: async () => {
            try {
              await AsyncStorage.clear();
              Alert.alert("Data Cleared", "All data has been cleared successfully.");
            } catch (error) {
              console.error("Error clearing data:", error);
              Alert.alert("Error", "There was an error clearing data. Please try again.");
            }
          },
        },
      ]
    );
  };

  const handleContactSupport = () => {
    Linking.openURL("mailto:<EMAIL>");
  };

  const handleRateApp = () => {
    // This would typically open the app store page
    Alert.alert("Rate App", "This would open the app store page for rating in a real app.");
  };

  const handlePrivacyPolicy = () => {
    // This would typically open a privacy policy page
    Alert.alert("Privacy Policy", "This would open the privacy policy page in a real app.");
  };

  return (
    <View style={styles.container}>
      <HeaderComponent
        title="Settings"
        subtitle="Customize your app experience"
      />
      <ScrollView style={styles.scrollContainer}>
        {/* Appearance Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={styles.headerIcon}>
                <Ionicons name="moon-outline" size={20} color={colors.primary} />
              </View>
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingLabel}>Dark Mode</Text>
                <Text style={styles.settingDescription}>Switch to dark color theme</Text>
              </View>
            </View>
            <View style={styles.switchContainer}>
              <Switch
                value={darkMode}
                onValueChange={setDarkMode}
                trackColor={{ false: colors.border, true: colors.accent }}
                thumbColor={"#fff"}
                ios_backgroundColor={colors.border}
              />
            </View>
          </View>
        </View>

        {/* Editor Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Editor</Text>
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={styles.headerIcon}>
                <Ionicons name="save-outline" size={20} color={colors.primary} />
              </View>
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingLabel}>Auto Save</Text>
                <Text style={styles.settingDescription}>Automatically save changes as you type</Text>
              </View>
            </View>
            <View style={styles.switchContainer}>
              <Switch
                value={autoSave}
                onValueChange={setAutoSave}
                trackColor={{ false: colors.border, true: colors.accent }}
                thumbColor={"#fff"}
                ios_backgroundColor={colors.border}
              />
            </View>
          </View>
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={styles.headerIcon}>
                <Ionicons name="time-outline" size={20} color={colors.primary} />
              </View>
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingLabel}>Save History</Text>
                <Text style={styles.settingDescription}>Keep track of previous versions</Text>
              </View>
            </View>
            <View style={styles.switchContainer}>
              <Switch
                value={saveHistory}
                onValueChange={setSaveHistory}
                trackColor={{ false: colors.border, true: colors.accent }}
                thumbColor={"#fff"}
                ios_backgroundColor={colors.border}
              />
            </View>
          </View>
        </View>

        {/* About Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          <TouchableOpacity style={styles.linkItem} onPress={handleRateApp}>
            <View style={styles.settingInfo}>
              <View style={styles.headerIcon}>
                <Ionicons name="star-outline" size={20} color={colors.primary} />
              </View>
              <Text style={styles.settingLabel}>Rate the App</Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.linkItem} onPress={handleContactSupport}>
            <View style={styles.settingInfo}>
              <View style={styles.headerIcon}>
                <Ionicons name="mail-outline" size={20} color={colors.primary} />
              </View>
              <Text style={styles.settingLabel}>Contact Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.linkItem} onPress={handlePrivacyPolicy}>
            <View style={styles.settingInfo}>
              <View style={styles.headerIcon}>
                <Ionicons name="shield-outline" size={20} color={colors.primary} />
              </View>
              <Text style={styles.settingLabel}>Privacy Policy</Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
          <View style={styles.versionContainer}>
            <Text style={styles.versionText}>Version 1.0.0</Text>
          </View>
        </View>

        {/* Danger Zone */}
        <View style={styles.dangerSection}>
          <Button
            title="Clear All Data"
            onPress={handleClearAllData}
            variant="primary"
            style={styles.dangerButton}
            icon={<Ionicons name="trash-outline" size={18} color="#fff" style={styles.buttonIcon} />}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flex: 1,
    paddingTop: spacing.sm,
  },
  section: {
    backgroundColor: colors.card,
    borderRadius: borderRadius.sm,
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    overflow: "hidden",
    ...shadows.xs,
  },
  sectionTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: "600",
    color: colors.primary,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.md,
    paddingBottom: spacing.sm,
    letterSpacing: 0.3,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  settingLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    marginLeft: spacing.sm,
    fontWeight: "500",
  },
  settingTextContainer: {
    flex: 1,
    justifyContent: "center",
  },
  settingDescription: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    marginTop: 2,
  },
  linkItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  versionContainer: {
    alignItems: "center",
    paddingVertical: spacing.md,
  },
  versionText: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  },
  dangerSection: {
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  dangerButton: {
    width: "100%",
    // backgroundColor: colors.error,
  },
  buttonIcon: {
    marginRight: spacing.xs,
  },
  switchContainer: {
    marginLeft: spacing.sm,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.xs,
  },
  headerIcon: {
    marginRight: spacing.sm,
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
  },
});
