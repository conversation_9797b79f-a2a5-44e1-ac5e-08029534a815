import HeaderComponent from "@/components/HeaderComponent";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Platform,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { borderRadius, colors, shadows, spacing, typography } from "../../constants/theme";
import { ResumeHistory } from "../../types";
import { clearHistory, getHistory, saveResume } from "../../utils/storage";

export default function HistoryScreen() {
  const [history, setHistory] = useState<ResumeHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadHistory = async () => {
    try {
      const data = await getHistory();
      // Sort by timestamp, newest first
      const sortedData = [...data].sort(
        (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
      setHistory(sortedData);
    } catch (error) {
      console.error("Error loading history:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    loadHistory();
  };

  const handleClearHistory = () => {
    Alert.alert(
      "Clear History",
      "Are you sure you want to clear all history? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Clear",
          style: "destructive",
          onPress: async () => {
            try {
              await clearHistory();
              setHistory([]);
            } catch (error) {
              console.error("Error clearing history:", error);
            }
          },
        },
      ]
    );
  };

  const handleRestoreVersion = (historyItem: ResumeHistory) => {
    Alert.alert(
      "Restore Version",
      "Do you want to restore this version? This will create a new resume based on this version.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Restore",
          onPress: async () => {
            try {
              // Create a new resume based on the snapshot
              const newResume = {
                ...historyItem.snapshot,
                id: `resume_${Date.now()}`,
                title: `${historyItem.snapshot.title} (Restored)`,
                updatedAt: new Date().toISOString(),
              };

              await saveResume(newResume);
              Alert.alert(
                "Version Restored",
                "A new resume has been created based on this version.",
                [
                  {
                    text: "View Resume",
                    onPress: () => router.navigate(`/resume/${newResume.id}`),
                  },
                  {
                    text: "OK",
                  },
                ]
              );
            } catch (error) {
              console.error("Error restoring version:", error);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.accent} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <HeaderComponent
        title="Resume History"
        subtitle={`${history.length} ${history.length === 1 ? "version" : "versions"} available`}
        actionButton={
          history.length > 0
            ? {
                icon: "trash-outline",
                label: "Clear All",
                onPress: handleClearHistory,
                color: colors.error,
                backgroundColor: `${colors.error}15`,
              }
            : undefined
        }
      />

      {history.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="time-outline" size={80} color={colors.textLight} />
          <Text style={styles.emptyTitle}>No History Yet</Text>
          <Text style={styles.emptyText}>Your resume edit history will appear here</Text>
        </View>
      ) : (
        <FlatList
          data={history}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.historyItem}
              onPress={() => handleRestoreVersion(item)}
              activeOpacity={0.7}>
              <View style={styles.historyContent}>
                <Text style={styles.historyTitle}>{item.title}</Text>
                <Text style={styles.historyDate}>{formatDate(item.timestamp)}</Text>
                <View style={styles.historyMeta}>
                  <View style={styles.templateBadge}>
                    <Ionicons name="document-outline" size={14} color={colors.accent} />
                    <Text style={styles.templateBadgeText}>{item.templateId}</Text>
                  </View>
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>
                      Version {history.findIndex((h) => h.id === item.id) + 1}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={styles.historyAction}>
                <Ionicons name="refresh-outline" size={18} color={colors.accent} />
                <Text style={styles.actionText}>Restore</Text>
              </View>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.accent]}
              tintColor={colors.accent}
            />
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    paddingTop: 0, // Removed extra padding
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: Platform.OS === "ios" ? spacing.xl : spacing.lg, // Increased to account for status bar
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.background,
  },
  headerTitle: {
    fontSize: typography.fontSize.xxl,
    fontWeight: "700",
    color: colors.text,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  clearButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.error}15`,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.round,
  },
  clearButton: {
    color: colors.error,
    fontSize: typography.fontSize.sm,
    fontWeight: "600",
    marginLeft: spacing.xs,
  },
  listContent: {
    padding: spacing.md,
    paddingBottom: 100, // Add extra padding at the bottom for better scrolling
  },
  historyItem: {
    flexDirection: "row",
    backgroundColor: colors.card,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.xs,
  },
  historyContent: {
    flex: 1,
  },
  historyTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: "600",
    color: colors.text,
    marginBottom: spacing.xs / 2,
  },
  historyDate: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  historyMeta: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
  },
  metaText: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    marginLeft: spacing.xs / 2,
  },
  historyAction: {
    justifyContent: "center",
    alignItems: "center",
    paddingLeft: spacing.md,
    borderLeftWidth: 1,
    borderLeftColor: colors.border,
    marginLeft: spacing.sm,
  },
  actionText: {
    fontSize: typography.fontSize.xs,
    color: colors.accent,
    marginTop: spacing.xs / 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: "600",
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    textAlign: "center",
  },
  badge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    backgroundColor: colors.backgroundDark,
    borderRadius: borderRadius.sm,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  badgeText: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  },
  templateBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.accent}15`,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.sm,
    marginRight: spacing.xs,
  },
  templateBadgeText: {
    fontSize: typography.fontSize.xs,
    color: colors.accent,
    marginLeft: spacing.xs / 2,
  },
});
