import { colors } from "@/constants/theme";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  FlatList,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Button from "../../components/Button";
import EnhancedTemplateCard from "../../components/EnhancedTemplateCard";
import HeaderComponent from "../../components/HeaderComponent";
import { TEMPLATES } from "../../constants/templates";
import { borderRadius, shadows, spacing, typography } from "../../constants/theme";

export default function TemplatesScreen() {
  const params = useLocalSearchParams();
  const resumeId = params.resumeId as string;
  const currentTemplateId = params.currentTemplateId as string;
  const isChangingTemplate = Boolean(resumeId && currentTemplateId);

  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(
    currentTemplateId || null
  );

  // Animation values
  const headerAnimation = useRef(new Animated.Value(0)).current;
  const buttonAnimation = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    // Animate header and button on mount
    Animated.stagger(300, [
      Animated.spring(headerAnimation, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.spring(buttonAnimation, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleSelectTemplate = (templateId: string) => {
    setSelectedTemplate(templateId);
  };

  const [isApplyingTemplate, setIsApplyingTemplate] = useState(false);

  const handleContinue = () => {
    if (!selectedTemplate) {
      Alert.alert("Template Required", "Please select a template to continue.");
      return;
    }

    if (isChangingTemplate) {
      // If changing template for existing resume, go back to the resume editor
      // Add a timestamp to ensure the params are seen as new
      const timestamp = Date.now();
      console.log(
        `Applying template ${selectedTemplate} to resume ${resumeId} with timestamp ${timestamp}`
      );

      // Set loading state
      setIsApplyingTemplate(true);

      // Use push to navigate to the resume editor
      // Short delay to ensure the loading state is visible
      setTimeout(() => {
        router.push({
          pathname: "/resume/[id]",
          params: {
            id: resumeId,
            templateId: selectedTemplate,
            template_update: "true",
            timestamp: timestamp.toString(), // Add timestamp to force param refresh
          },
        });
      }, 300);
    } else {
      // Generate a new resume ID for a new resume
      const newResumeId = `resume_${Date.now()}`;

      // Navigate to the resume editor with the new ID and selected template
      router.push({
        pathname: "/resume/[id]",
        params: {
          id: newResumeId,
          templateId: selectedTemplate,
          isNew: "true",
        },
      });
    }
  };

  const renderHeader = () => (
    <Animated.View
      style={{
        opacity: headerAnimation,
        transform: [
          {
            translateY: headerAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [-20, 0],
            }),
          },
        ],
      }}>
      <HeaderComponent
        title="Choose Your Template"
        subtitle="Select a professional template for your resume. You can change it anytime."
        containerStyle={styles.headerContainer}
        actionButton={{
          icon: "grid-outline",
          label: `${TEMPLATES.length}`,
          onPress: () => {},
          color: "#fff",
          backgroundColor: colors.accent,
        }}
      />
    </Animated.View>
  );

  const renderFooter = () => <View style={styles.footerSpacer} />;

  return (
    <View style={styles.outerContainer}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background} />

      <View style={styles.container}>
        {/* Fixed Header */}
        {renderHeader()}

        <View style={styles.contentContainer}>
          <FlatList
            data={TEMPLATES}
            keyExtractor={(item) => item.id}
            renderItem={({ item, index }) => (
              <EnhancedTemplateCard
                template={item}
                selected={selectedTemplate === item.id}
                onSelect={handleSelectTemplate}
                index={index}
              />
            )}
            contentContainerStyle={styles.listContent}
            ListFooterComponent={renderFooter}
            showsVerticalScrollIndicator={false}
            numColumns={2}
            initialNumToRender={4}
            maxToRenderPerBatch={4}
            windowSize={5}
          />
        </View>
      </View>

      {/* Fixed Button Container */}
      <View style={styles.buttonContainer}>
        <Animated.View
          style={[
            styles.buttonWrapper,
            {
              opacity: buttonAnimation,
              transform: [
                {
                  translateY: buttonAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
            },
          ]}>
          <Button
            title={isChangingTemplate ? "Apply Template" : "Continue with Selected Template"}
            onPress={handleContinue}
            variant="primary"
            size="large"
            disabled={!selectedTemplate}
            style={styles.continueButton}
            icon={
              <Ionicons name="arrow-forward" size={20} color="#fff" style={styles.buttonIcon} />
            }
          />

          {isChangingTemplate && (
            <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </View>

      {/* Loading overlay */}
      {isApplyingTemplate && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingCard}>
            <ActivityIndicator size="large" color={colors.accent} />
            <Text style={styles.loadingText}>
              Applying{" "}
              {selectedTemplate
                ? selectedTemplate.charAt(0).toUpperCase() + selectedTemplate.slice(1)
                : ""}{" "}
              template...
            </Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: colors.background,
    position: "relative", // Ensure relative positioning for absolute children
  },
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  contentContainer: {
    flex: 1,
  },
  buttonContainer: {
    position: "absolute",
    bottom: Platform.OS === "ios" ? 100 : 80, // Position well above the tab bar
    // left: spacing.md,
    // right: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
    // borderWidth: 1,
    borderColor: colors.border,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    // ...shadows.md,
    // elevation: 10,
    // zIndex: 9999,
    // shadowColor: "#000",
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.2,
    // shadowRadius: 8,
  },
  buttonWrapper: {
    width: "100%",
  },
  headerContainer: {
    marginBottom: spacing.sm,
  },
  headerContent: {
    flex: 1,
    paddingRight: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: "700",
    color: colors.text,
    marginBottom: spacing.xs / 2,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    lineHeight: 18,
  },
  templateCountBadge: {
    backgroundColor: colors.accent,
    borderRadius: borderRadius.sm,
    padding: spacing.xs,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 50,
  },
  templateCountText: {
    fontSize: typography.fontSize.md,
    fontWeight: "700",
    color: "#fff",
  },
  templateCountLabel: {
    fontSize: typography.fontSize.xs,
    color: "#fff",
    opacity: 0.9,
  },
  listContent: {
    paddingTop: spacing.sm, // Add padding at the top to separate from header
    paddingBottom: Platform.OS === "ios" ? 160 : 140, // Extra padding at the bottom for the fixed footer
    paddingHorizontal: spacing.sm, // Add horizontal padding for the grid
  },
  footerSpacer: {
    height: Platform.OS === "ios" ? 120 : 100, // Space for the fixed footer
  },
  continueButton: {
    width: "100%",
    borderRadius: borderRadius.md,
    height: 50,
    marginBottom: 4,
  },
  buttonIcon: {
    marginLeft: spacing.xs,
  },
  cancelButton: {
    marginTop: spacing.xs,
    alignItems: "center",
    padding: spacing.xs,
  },
  cancelButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    fontWeight: "500",
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  loadingCard: {
    backgroundColor: colors.card,
    borderRadius: borderRadius.sm,
    padding: spacing.lg,
    alignItems: "center",
    justifyContent: "center",
    width: "80%",
    maxWidth: 280,
    ...shadows.xs,
    borderWidth: 1,
    borderColor: colors.border,
  },
  loadingText: {
    marginTop: spacing.sm,
    fontSize: typography.fontSize.sm,
    color: colors.text,
    textAlign: "center",
  },
});
