import Button from "@/components/Button";
import ResumeCard from "@/components/ResumeCard";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Animated,
  FlatList,
  Platform,
  RefreshControl,
  StyleSheet,
  Text,
  View,
} from "react-native";
import HeaderComponent from "../../components/HeaderComponent";
import { borderRadius, colors, shadows, spacing, typography } from "../../constants/theme";
import { ResumeData } from "../../types";
import { deleteResume, getResumes } from "../../utils/storage";

export default function MyResumesScreen() {
  const [resumes, setResumes] = useState<ResumeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Animation values - separate for empty state and list
  const emptyFadeAnim = useRef(new Animated.Value(0)).current;
  const emptyTranslateY = useRef(new Animated.Value(20)).current;
  const listFadeAnim = useRef(new Animated.Value(0)).current;
  const listTranslateY = useRef(new Animated.Value(20)).current;

  const loadResumes = async () => {
    try {
      const data = await getResumes();
      setResumes(data);

      // Animate content in - use different animations for empty state and list
      if (data.length === 0) {
        // Animate empty state
        Animated.parallel([
          Animated.timing(emptyFadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(emptyTranslateY, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        // Animate list
        Animated.parallel([
          Animated.timing(listFadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(listTranslateY, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ]).start();
      }
    } catch (error) {
      console.error("Error loading resumes:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadResumes();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    loadResumes();
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteResume(id);
      setResumes(resumes.filter((resume) => resume.id !== id));
    } catch (error) {
      console.error("Error deleting resume:", error);
    }
  };

  const handleCreateNew = () => {
    router.push("/templates");
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {resumes.length === 0 ? (
        <Animated.View
          style={[
            styles.emptyContainer,
            {
              opacity: emptyFadeAnim,
              transform: [{ translateY: emptyTranslateY }],
            },
          ]}>
          <View style={styles.emptyIconContainer}>
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              style={styles.emptyIconGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}>
              <Ionicons name="document-text-outline" size={80} color="#ffffff" />
            </LinearGradient>
          </View>
          <Text style={styles.emptyTitle}>No Resumes Yet</Text>
          <Text style={styles.emptyText}>Create your first resume by tapping the button below</Text>
          <Button
            title="Create New Resume"
            onPress={handleCreateNew}
            variant="primary"
            size="large"
            style={styles.emptyButton}
            rounded
            icon={
              <Ionicons
                name="add-circle-outline"
                size={20}
                color="#fff"
                style={styles.buttonIcon}
              />
            }
          />
        </Animated.View>
      ) : (
        <Animated.View
          style={{
            flex: 1,
            opacity: listFadeAnim,
            transform: [{ translateY: listTranslateY }],
          }}>
          <HeaderComponent
            title="My Resumes"
            subtitle={`${resumes.length} ${resumes.length === 1 ? "resume" : "resumes"} available`}
            actionButton={{
              icon: "add-outline",
              label: "New",
              onPress: handleCreateNew,
            }}
          />

          <FlatList
            data={resumes}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <ResumeCard resume={item} onDelete={() => handleDelete(item.id)} />
            )}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }
            showsVerticalScrollIndicator={false}
          />
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: Platform.OS === "ios" ? spacing.xl : spacing.lg, // Increased to account for status bar
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.background,
  },
  headerTitle: {
    fontSize: typography.fontSize.xxl,
    fontWeight: "700", // Use string literal instead of typography.fontWeight.bold
    color: colors.text,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  newButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.primary}15`,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.round,
  },
  newButtonText: {
    color: colors.primary,
    fontSize: typography.fontSize.sm,
    fontWeight: "600",
    marginLeft: spacing.xs,
  },
  listContent: {
    padding: spacing.md,
    paddingBottom: spacing.md, // Normal padding at the bottom
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  },
  emptyIconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.lg,
    ...shadows.lg,
    overflow: "hidden",
  },
  emptyIconGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  emptyTitle: {
    fontSize: typography.fontSize.xxl,
    fontWeight: "700", // Use string literal instead of typography.fontWeight.bold
    color: colors.text,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  emptyButton: {
    width: 240,
    borderRadius: borderRadius.lg,
  },
  buttonIcon: {
    marginRight: spacing.xs,
  },
});
