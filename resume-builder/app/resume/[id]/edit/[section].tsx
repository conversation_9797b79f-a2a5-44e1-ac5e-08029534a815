import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Button from "../../../../components/Button";
import { Education, Experience, PersonalInfo, Project, ResumeData, Skill, Certification } from "../../../../types";
import { getResumeById, saveResume } from "../../../../utils/storage";

export default function SectionEditorScreen() {
  const params = useLocalSearchParams();
  const id = params.id as string;
  const section = params.section as string;

  const [resume, setResume] = useState<ResumeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadResume();
  }, [id]);

  const loadResume = async () => {
    try {
      const data = await getResumeById(id);
      if (data) {
        setResume(data);
      } else {
        Alert.alert("Error", "Resume not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading resume:", error);
      Alert.alert("Error", "Failed to load resume");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!resume) return;

    try {
      setSaving(true);
      const updatedResume = {
        ...resume,
        updatedAt: new Date().toISOString(),
      };
      await saveResume(updatedResume);
      Alert.alert("Success", "Section saved successfully", [
        {
          text: "OK",
          onPress: () => router.push(`/resume/${id}/sections`),
        },
      ]);
    } catch (error) {
      console.error("Error saving resume:", error);
      Alert.alert("Error", "Failed to save resume");
    } finally {
      setSaving(false);
    }
  };

  // Personal Info Section
  const updatePersonalInfo = (field: keyof PersonalInfo, value: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      personalInfo: {
        ...resume.personalInfo,
        [field]: value,
      },
    });
  };

  // Education Section
  const addEducation = () => {
    if (!resume) return;
    const newEducation: Education = {
      id: `edu_${Date.now()}`,
      institution: "",
      degree: "",
      fieldOfStudy: "",
      startDate: "",
      endDate: "",
      location: "",
      description: "",
    };

    setResume({
      ...resume,
      education: [...resume.education, newEducation],
    });
  };

  const updateEducation = (id: string, field: keyof Education, value: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      education: resume.education.map((edu) => (edu.id === id ? { ...edu, [field]: value } : edu)),
    });
  };

  const removeEducation = (id: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      education: resume.education.filter((edu) => edu.id !== id),
    });
  };

  // Experience Section
  const addExperience = () => {
    if (!resume) return;
    const newExperience: Experience = {
      id: `exp_${Date.now()}`,
      company: "",
      position: "",
      startDate: "",
      endDate: "",
      location: "",
      description: "",
      highlights: [],
      isCurrent: false,
    };

    setResume({
      ...resume,
      experience: [...resume.experience, newExperience],
    });
  };

  const updateExperience = (id: string, field: keyof Experience, value: any) => {
    if (!resume) return;
    setResume({
      ...resume,
      experience: resume.experience.map((exp) => (exp.id === id ? { ...exp, [field]: value } : exp)),
    });
  };

  const removeExperience = (id: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      experience: resume.experience.filter((exp) => exp.id !== id),
    });
  };

  // Skills Section
  const addSkill = () => {
    if (!resume) return;
    const newSkill: Skill = {
      id: `skill_${Date.now()}`,
      name: "",
    };

    setResume({
      ...resume,
      skills: [...resume.skills, newSkill],
    });
  };

  const updateSkill = (id: string, field: keyof Skill, value: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      skills: resume.skills.map((skill) => (skill.id === id ? { ...skill, [field]: value } : skill)),
    });
  };

  const removeSkill = (id: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      skills: resume.skills.filter((skill) => skill.id !== id),
    });
  };

  // Projects Section
  const addProject = () => {
    if (!resume) return;
    const newProject: Project = {
      id: `proj_${Date.now()}`,
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      url: "",
      technologies: [],
    };

    setResume({
      ...resume,
      projects: [...resume.projects, newProject],
    });
  };

  const updateProject = (id: string, field: keyof Project, value: any) => {
    if (!resume) return;
    setResume({
      ...resume,
      projects: resume.projects.map((proj) => (proj.id === id ? { ...proj, [field]: value } : proj)),
    });
  };

  const removeProject = (id: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      projects: resume.projects.filter((proj) => proj.id !== id),
    });
  };

  // Certifications Section
  const addCertification = () => {
    if (!resume) return;
    const newCertification: Certification = {
      id: `cert_${Date.now()}`,
      name: "",
      issuer: "",
      date: "",
      url: "",
      description: "",
    };

    setResume({
      ...resume,
      certifications: [...resume.certifications, newCertification],
    });
  };

  const updateCertification = (id: string, field: keyof Certification, value: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      certifications: resume.certifications.map((cert) => (cert.id === id ? { ...cert, [field]: value } : cert)),
    });
  };

  const removeCertification = (id: string) => {
    if (!resume) return;
    setResume({
      ...resume,
      certifications: resume.certifications.filter((cert) => cert.id !== id),
    });
  };

  // Render section content based on section type
  const renderSectionContent = () => {
    if (!resume) return null;

    switch (section) {
      case "personal":
        return (
          <>
            <View style={styles.formRow}>
              <View style={styles.formColumn}>
                <Text style={styles.label}>First Name</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.firstName}
                  onChangeText={(text) => updatePersonalInfo("firstName", text)}
                  placeholder="First Name"
                />
              </View>
              <View style={styles.formColumn}>
                <Text style={styles.label}>Last Name</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.lastName}
                  onChangeText={(text) => updatePersonalInfo("lastName", text)}
                  placeholder="Last Name"
                />
              </View>
            </View>

            <View style={styles.formRow}>
              <View style={styles.formColumn}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.email}
                  onChangeText={(text) => updatePersonalInfo("email", text)}
                  placeholder="Email"
                  keyboardType="email-address"
                />
              </View>
              <View style={styles.formColumn}>
                <Text style={styles.label}>Phone</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.phone}
                  onChangeText={(text) => updatePersonalInfo("phone", text)}
                  placeholder="Phone"
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            <Text style={styles.label}>Professional Title</Text>
            <TextInput
              style={styles.input}
              value={resume.personalInfo.title}
              onChangeText={(text) => updatePersonalInfo("title", text)}
              placeholder="e.g. Software Engineer"
            />

            <Text style={styles.label}>Address</Text>
            <TextInput
              style={styles.input}
              value={resume.personalInfo.address}
              onChangeText={(text) => updatePersonalInfo("address", text)}
              placeholder="Street Address"
            />

            <View style={styles.formRow}>
              <View style={styles.formColumn}>
                <Text style={styles.label}>City</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.city}
                  onChangeText={(text) => updatePersonalInfo("city", text)}
                  placeholder="City"
                />
              </View>
              <View style={styles.formColumn}>
                <Text style={styles.label}>State</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.state}
                  onChangeText={(text) => updatePersonalInfo("state", text)}
                  placeholder="State"
                />
              </View>
            </View>

            <View style={styles.formRow}>
              <View style={styles.formColumn}>
                <Text style={styles.label}>Zip Code</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.zipCode}
                  onChangeText={(text) => updatePersonalInfo("zipCode", text)}
                  placeholder="Zip Code"
                />
              </View>
              <View style={styles.formColumn}>
                <Text style={styles.label}>Country</Text>
                <TextInput
                  style={styles.input}
                  value={resume.personalInfo.country}
                  onChangeText={(text) => updatePersonalInfo("country", text)}
                  placeholder="Country"
                />
              </View>
            </View>

            <Text style={styles.label}>Professional Summary</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={resume.personalInfo.summary}
              onChangeText={(text) => updatePersonalInfo("summary", text)}
              placeholder="Write a brief summary of your professional background and goals"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </>
        );

      case "education":
        return (
          <>
            {resume.education.map((edu, index) => (
              <View key={edu.id} style={styles.sectionItem}>
                <View style={styles.sectionItemHeader}>
                  <Text style={styles.sectionItemTitle}>Education #{index + 1}</Text>
                  <TouchableOpacity onPress={() => removeEducation(edu.id)}>
                    <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.label}>Institution</Text>
                <TextInput
                  style={styles.input}
                  value={edu.institution}
                  onChangeText={(text) => updateEducation(edu.id, "institution", text)}
                  placeholder="University or School Name"
                />

                <View style={styles.formRow}>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>Degree</Text>
                    <TextInput
                      style={styles.input}
                      value={edu.degree}
                      onChangeText={(text) => updateEducation(edu.id, "degree", text)}
                      placeholder="e.g. Bachelor's"
                    />
                  </View>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>Field of Study</Text>
                    <TextInput
                      style={styles.input}
                      value={edu.fieldOfStudy}
                      onChangeText={(text) => updateEducation(edu.id, "fieldOfStudy", text)}
                      placeholder="e.g. Computer Science"
                    />
                  </View>
                </View>

                <View style={styles.formRow}>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>Start Date</Text>
                    <TextInput
                      style={styles.input}
                      value={edu.startDate}
                      onChangeText={(text) => updateEducation(edu.id, "startDate", text)}
                      placeholder="MM/YYYY"
                    />
                  </View>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>End Date</Text>
                    <TextInput
                      style={styles.input}
                      value={edu.endDate}
                      onChangeText={(text) => updateEducation(edu.id, "endDate", text)}
                      placeholder="MM/YYYY or Present"
                    />
                  </View>
                </View>

                <Text style={styles.label}>Location</Text>
                <TextInput
                  style={styles.input}
                  value={edu.location}
                  onChangeText={(text) => updateEducation(edu.id, "location", text)}
                  placeholder="City, State, Country"
                />

                <Text style={styles.label}>Description</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={edu.description}
                  onChangeText={(text) => updateEducation(edu.id, "description", text)}
                  placeholder="Describe your studies, achievements, etc."
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            ))}

            <Button
              title="Add Education"
              onPress={addEducation}
              variant="outline"
              size="small"
              icon={<Ionicons name="add" size={16} color="#3498db" style={styles.buttonIcon} />}
            />
          </>
        );

      case "experience":
        return (
          <>
            {resume.experience.map((exp, index) => (
              <View key={exp.id} style={styles.sectionItem}>
                <View style={styles.sectionItemHeader}>
                  <Text style={styles.sectionItemTitle}>Experience #{index + 1}</Text>
                  <TouchableOpacity onPress={() => removeExperience(exp.id)}>
                    <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.label}>Company</Text>
                <TextInput
                  style={styles.input}
                  value={exp.company}
                  onChangeText={(text) => updateExperience(exp.id, "company", text)}
                  placeholder="Company Name"
                />

                <Text style={styles.label}>Position</Text>
                <TextInput
                  style={styles.input}
                  value={exp.position}
                  onChangeText={(text) => updateExperience(exp.id, "position", text)}
                  placeholder="Job Title"
                />

                <View style={styles.formRow}>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>Start Date</Text>
                    <TextInput
                      style={styles.input}
                      value={exp.startDate}
                      onChangeText={(text) => updateExperience(exp.id, "startDate", text)}
                      placeholder="MM/YYYY"
                    />
                  </View>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>End Date</Text>
                    <TextInput
                      style={styles.input}
                      value={exp.endDate}
                      onChangeText={(text) => updateExperience(exp.id, "endDate", text)}
                      placeholder="MM/YYYY or Present"
                    />
                  </View>
                </View>

                <Text style={styles.label}>Location</Text>
                <TextInput
                  style={styles.input}
                  value={exp.location}
                  onChangeText={(text) => updateExperience(exp.id, "location", text)}
                  placeholder="City, State, Country"
                />

                <Text style={styles.label}>Description</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={exp.description}
                  onChangeText={(text) => updateExperience(exp.id, "description", text)}
                  placeholder="Describe your role and responsibilities"
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            ))}

            <Button
              title="Add Experience"
              onPress={addExperience}
              variant="outline"
              size="small"
              icon={<Ionicons name="add" size={16} color="#3498db" style={styles.buttonIcon} />}
            />
          </>
        );

      case "skills":
        return (
          <>
            {resume.skills.map((skill, index) => (
              <View key={skill.id} style={styles.skillItem}>
                <TextInput
                  style={styles.skillInput}
                  value={skill.name}
                  onChangeText={(text) => updateSkill(skill.id, "name", text)}
                  placeholder="Skill name"
                />
                <TouchableOpacity onPress={() => removeSkill(skill.id)}>
                  <Ionicons name="close-circle" size={20} color="#e74c3c" />
                </TouchableOpacity>
              </View>
            ))}

            <Button
              title="Add Skill"
              onPress={addSkill}
              variant="outline"
              size="small"
              icon={<Ionicons name="add" size={16} color="#3498db" style={styles.buttonIcon} />}
            />
          </>
        );

      case "projects":
        return (
          <>
            {resume.projects.map((project, index) => (
              <View key={project.id} style={styles.sectionItem}>
                <View style={styles.sectionItemHeader}>
                  <Text style={styles.sectionItemTitle}>Project #{index + 1}</Text>
                  <TouchableOpacity onPress={() => removeProject(project.id)}>
                    <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.label}>Project Name</Text>
                <TextInput
                  style={styles.input}
                  value={project.name}
                  onChangeText={(text) => updateProject(project.id, "name", text)}
                  placeholder="Project Name"
                />

                <View style={styles.formRow}>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>Start Date</Text>
                    <TextInput
                      style={styles.input}
                      value={project.startDate}
                      onChangeText={(text) => updateProject(project.id, "startDate", text)}
                      placeholder="MM/YYYY"
                    />
                  </View>
                  <View style={styles.formColumn}>
                    <Text style={styles.label}>End Date</Text>
                    <TextInput
                      style={styles.input}
                      value={project.endDate}
                      onChangeText={(text) => updateProject(project.id, "endDate", text)}
                      placeholder="MM/YYYY or Present"
                    />
                  </View>
                </View>

                <Text style={styles.label}>URL</Text>
                <TextInput
                  style={styles.input}
                  value={project.url}
                  onChangeText={(text) => updateProject(project.id, "url", text)}
                  placeholder="Project URL or GitHub link"
                />

                <Text style={styles.label}>Description</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={project.description}
                  onChangeText={(text) => updateProject(project.id, "description", text)}
                  placeholder="Describe the project, your role, and achievements"
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />

                <Text style={styles.label}>Technologies</Text>
                <TextInput
                  style={styles.input}
                  value={project.technologies.join(", ")}
                  onChangeText={(text) => updateProject(project.id, "technologies", text.split(", "))}
                  placeholder="Technologies used (comma separated)"
                />
              </View>
            ))}

            <Button
              title="Add Project"
              onPress={addProject}
              variant="outline"
              size="small"
              icon={<Ionicons name="add" size={16} color="#3498db" style={styles.buttonIcon} />}
            />
          </>
        );

      case "certifications":
        return (
          <>
            {resume.certifications.map((cert, index) => (
              <View key={cert.id} style={styles.sectionItem}>
                <View style={styles.sectionItemHeader}>
                  <Text style={styles.sectionItemTitle}>Certification #{index + 1}</Text>
                  <TouchableOpacity onPress={() => removeCertification(cert.id)}>
                    <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.label}>Certification Name</Text>
                <TextInput
                  style={styles.input}
                  value={cert.name}
                  onChangeText={(text) => updateCertification(cert.id, "name", text)}
                  placeholder="Certification Name"
                />

                <Text style={styles.label}>Issuer</Text>
                <TextInput
                  style={styles.input}
                  value={cert.issuer}
                  onChangeText={(text) => updateCertification(cert.id, "issuer", text)}
                  placeholder="Issuing Organization"
                />

                <Text style={styles.label}>Date</Text>
                <TextInput
                  style={styles.input}
                  value={cert.date}
                  onChangeText={(text) => updateCertification(cert.id, "date", text)}
                  placeholder="MM/YYYY"
                />

                <Text style={styles.label}>URL</Text>
                <TextInput
                  style={styles.input}
                  value={cert.url}
                  onChangeText={(text) => updateCertification(cert.id, "url", text)}
                  placeholder="Certification URL"
                />

                <Text style={styles.label}>Description</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={cert.description}
                  onChangeText={(text) => updateCertification(cert.id, "description", text)}
                  placeholder="Describe the certification and its relevance"
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            ))}

            <Button
              title="Add Certification"
              onPress={addCertification}
              variant="outline"
              size="small"
              icon={<Ionicons name="add" size={16} color="#3498db" style={styles.buttonIcon} />}
            />
          </>
        );

      default:
        return (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Invalid section: {section}</Text>
          </View>
        );
    }
  };

  // Get section title
  const getSectionTitle = () => {
    switch (section) {
      case "personal":
        return "Personal Information";
      case "education":
        return "Education";
      case "experience":
        return "Experience";
      case "skills":
        return "Skills";
      case "projects":
        return "Projects";
      case "certifications":
        return "Certifications";
      default:
        return "Unknown Section";
    }
  };

  // Get section icon
  const getSectionIcon = () => {
    switch (section) {
      case "personal":
        return "person-outline";
      case "education":
        return "school-outline";
      case "experience":
        return "briefcase-outline";
      case "skills":
        return "construct-outline";
      case "projects":
        return "code-outline";
      case "certifications":
        return "ribbon-outline";
      default:
        return "document-outline";
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}
    >
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Ionicons name={getSectionIcon() as any} size={24} color="#3498db" />
          <Text style={styles.headerTitle}>{getSectionTitle()}</Text>
        </View>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.push(`/resume/${id}/sections`)}
        >
          <Ionicons name="close" size={24} color="#666" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {renderSectionContent()}
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={saving ? "Saving..." : "Save Changes"}
          onPress={handleSave}
          variant="primary"
          size="large"
          disabled={saving}
          loading={saving}
          style={styles.saveButton}
        />
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginLeft: 8,
  },
  backButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  formRow: {
    flexDirection: "row",
    marginHorizontal: -8,
  },
  formColumn: {
    flex: 1,
    paddingHorizontal: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    color: "#555",
    marginBottom: 4,
    marginTop: 12,
  },
  input: {
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    color: "#333",
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  sectionItem: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  sectionItemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  sectionItemTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  skillItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  skillInput: {
    flex: 1,
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    color: "#333",
    marginRight: 8,
  },
  footer: {
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#eee",
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
  },
  saveButton: {
    width: "100%",
  },
  buttonIcon: {
    marginRight: 8,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: "#ffebee",
    borderRadius: 8,
    marginVertical: 16,
  },
  errorText: {
    color: "#e53935",
    fontSize: 16,
  },
});
