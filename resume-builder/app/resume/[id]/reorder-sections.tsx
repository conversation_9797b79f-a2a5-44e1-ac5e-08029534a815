import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import SectionOrderManager from "../../../components/SectionOrderManager";
import { ResumeData } from "../../../types";
import { getResumeById, saveResume } from "../../../utils/storage";

export default function ReorderSectionsScreen() {
  const params = useLocalSearchParams();
  const id = params.id as string;
  
  const [resume, setResume] = useState<ResumeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadResume();
  }, [id]);

  const loadResume = async () => {
    try {
      const data = await getResumeById(id);
      if (data) {
        // If the resume doesn't have a sectionOrder, add the default one
        if (!data.sectionOrder) {
          data.sectionOrder = ["education", "experience", "skills", "projects", "certifications"];
        }
        setResume(data);
      } else {
        Alert.alert("Error", "Resume not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading resume:", error);
      Alert.alert("Error", "Failed to load resume");
    } finally {
      setLoading(false);
    }
  };

  const handleOrderChange = (newOrder: string[]) => {
    if (resume) {
      setResume({
        ...resume,
        sectionOrder: newOrder,
      });
    }
  };

  const handleSave = async () => {
    if (!resume) return;

    try {
      setSaving(true);
      const updatedResume = {
        ...resume,
        updatedAt: new Date().toISOString(),
      };
      await saveResume(updatedResume);
      console.log("Saved resume with new section order:", updatedResume.sectionOrder);
    } catch (error) {
      console.error("Error saving resume:", error);
      Alert.alert("Error", "Failed to save resume");
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>Loading resume...</Text>
      </View>
    );
  }

  if (!resume) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load resume</Text>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Reorder Sections</Text>
      </View>

      <SectionOrderManager
        resume={resume}
        onOrderChange={handleOrderChange}
        onSave={handleSave}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    backgroundColor: "#fff",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginLeft: 16,
  },
  backButton: {
    padding: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: "#e74c3c",
    marginBottom: 16,
  },
  backButtonText: {
    color: "#3498db",
    fontSize: 16,
    fontWeight: "600",
  },
});
