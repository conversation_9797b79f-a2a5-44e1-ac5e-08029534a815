import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Button from "../../../components/Button";
import FormSection from "../../../components/FormSection";
import { borderRadius, colors, shadows, typography } from "../../../constants/theme";
import {
  Certification,
  Education,
  Experience,
  PersonalInfo,
  Project,
  ResumeData,
  Skill,
} from "../../../types";
import { getResumeById, saveResume } from "../../../utils/storage";

export default function ResumeEditorScreen() {
  const params = useLocalSearchParams();
  const id = params.id as string;
  const templateId = params.templateId as string;
  const isNew = params.isNew === "true";
  const templateUpdate = params.template_update === "true";

  const [resume, setResume] = useState<ResumeData>({
    id,
    title: "My Resume",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    personalInfo: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
      title: "",
      summary: "",
    },
    education: [],
    experience: [],
    skills: [],
    projects: [],
    certifications: [],
    templateId: templateId || "modern",
    sectionOrder: ["education", "experience", "skills", "projects", "certifications"], // Default section order
  });

  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  // Keep track of the last template ID from params to detect changes
  const [lastTemplateId, setLastTemplateId] = useState<string | null>(templateId || null);

  useEffect(() => {
    if (!isNew) {
      loadResume();
    }
  }, [id, isNew]);

  // State to track template updating process
  const [updatingTemplate, setUpdatingTemplate] = useState(false);

  // This effect runs when the component mounts and when templateId, templateUpdate, or timestamp changes
  useEffect(() => {
    // Log the template ID from params for debugging
    console.log("Template ID from params:", templateId);
    console.log("Current template ID in resume:", resume.templateId);
    console.log("Template update flag:", templateUpdate);
    console.log("Timestamp:", params.timestamp);

    // Check if we have a template ID and either:
    // 1. It's different from the current template in the resume, or
    // 2. The template_update flag is set to true
    if (templateId && (resume.templateId !== templateId || templateUpdate === "true")) {
      console.log("Updating template ID to:", templateId);

      // Set updating state to true
      setUpdatingTemplate(true);

      // Create an updated resume object with the new template ID
      const updatedResumeData = {
        ...resume,
        templateId,
        updatedAt: new Date().toISOString(),
      };

      // Update the resume state with the new template ID
      setResume(updatedResumeData);

      // Update the last template ID we've seen
      setLastTemplateId(templateId);

      // Auto-save the resume with the new template
      const saveWithNewTemplate = async () => {
        try {
          await saveResume(updatedResumeData);
          console.log("Resume auto-saved with new template:", templateId);

          // Short delay to ensure the template is fully applied
          setTimeout(() => {
            // Set updating state to false
            setUpdatingTemplate(false);

            // Show a brief confirmation to the user
            Alert.alert(
              "Template Updated",
              `Your resume is now using the ${
                templateId.charAt(0).toUpperCase() + templateId.slice(1)
              } template.`,
              [{ text: "OK" }],
              { cancelable: true }
            );
          }, 500);
        } catch (error) {
          console.error("Error auto-saving resume with new template:", error);
          setUpdatingTemplate(false);
        }
      };

      saveWithNewTemplate();
    }
  }, [templateId, templateUpdate, params.timestamp]);

  const loadResume = async () => {
    try {
      const data = await getResumeById(id);
      if (data) {
        // If we have a template ID from params and it's different from the loaded resume's template,
        // or if template_update flag is set, update the template
        if (templateId && (data.templateId !== templateId || templateUpdate === "true")) {
          console.log("Applying new template during load:", templateId);

          // Create an updated resume object with the new template ID
          const updatedResumeData = {
            ...data,
            templateId: templateId,
            updatedAt: new Date().toISOString(),
          };

          // Update the resume state
          setResume(updatedResumeData);

          // Save the updated resume with the new template
          await saveResume(updatedResumeData);
          console.log("Resume saved with new template during load:", templateId);
        } else {
          setResume(data);
        }
      } else {
        Alert.alert("Error", "Resume not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading resume:", error);
      Alert.alert("Error", "Failed to load resume");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const updatedResume = {
        ...resume,
        updatedAt: new Date().toISOString(),
      };
      await saveResume(updatedResume);
      Alert.alert("Success", "Resume saved successfully");
    } catch (error) {
      console.error("Error saving resume:", error);
      Alert.alert("Error", "Failed to save resume");
    } finally {
      setSaving(false);
    }
  };

  const handlePreview = () => {
    console.log("Previewing resume with template:", resume.templateId);
    router.push(`/resume/${id}/preview`);
  };

  const handleChangeTemplate = () => {
    console.log("Changing template, current template:", resume.templateId);

    // First save the current state of the resume to ensure template changes are applied to the latest data
    handleSaveBeforeTemplateChange();
  };

  const handleSaveBeforeTemplateChange = async () => {
    try {
      setSaving(true);
      const updatedResume = {
        ...resume,
        updatedAt: new Date().toISOString(),
      };
      await saveResume(updatedResume);

      // After saving, navigate directly to the enhanced templates screen
      router.push({
        pathname: "/enhanced-templates",
        params: {
          resumeId: id,
          currentTemplateId: resume.templateId,
        },
      });
    } catch (error) {
      console.error("Error saving resume before template change:", error);
      Alert.alert("Error", "Failed to save resume before changing template");

      // Even if saving fails, still allow the user to change the template
      router.push({
        pathname: "/enhanced-templates",
        params: {
          resumeId: id,
          currentTemplateId: resume.templateId,
        },
      });
    } finally {
      setSaving(false);
    }
  };

  const updatePersonalInfo = (field: keyof PersonalInfo, value: string) => {
    setResume((prev) => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value,
      },
    }));
  };

  const addEducation = () => {
    const newEducation: Education = {
      id: `edu_${Date.now()}`,
      institution: "",
      degree: "",
      fieldOfStudy: "",
      startDate: "",
      endDate: "",
      location: "",
      description: "",
    };

    setResume((prev) => ({
      ...prev,
      education: [...prev.education, newEducation],
    }));
  };

  const updateEducation = (id: string, field: keyof Education, value: string) => {
    setResume((prev) => ({
      ...prev,
      education: prev.education.map((edu) => (edu.id === id ? { ...edu, [field]: value } : edu)),
    }));
  };

  const removeEducation = (id: string) => {
    setResume((prev) => ({
      ...prev,
      education: prev.education.filter((edu) => edu.id !== id),
    }));
  };

  const addExperience = () => {
    const newExperience: Experience = {
      id: `exp_${Date.now()}`,
      company: "",
      position: "",
      startDate: "",
      endDate: "",
      location: "",
      description: "",
      highlights: [],
      isCurrent: false,
    };

    setResume((prev) => ({
      ...prev,
      experience: [...prev.experience, newExperience],
    }));
  };

  const updateExperience = (id: string, field: keyof Experience, value: any) => {
    setResume((prev) => ({
      ...prev,
      experience: prev.experience.map((exp) => (exp.id === id ? { ...exp, [field]: value } : exp)),
    }));
  };

  const removeExperience = (id: string) => {
    setResume((prev) => ({
      ...prev,
      experience: prev.experience.filter((exp) => exp.id !== id),
    }));
  };

  const addSkill = () => {
    const newSkill: Skill = {
      id: `skill_${Date.now()}`,
      name: "",
    };

    setResume((prev) => ({
      ...prev,
      skills: [...prev.skills, newSkill],
    }));
  };

  const updateSkill = (id: string, field: keyof Skill, value: string) => {
    setResume((prev) => ({
      ...prev,
      skills: prev.skills.map((skill) => (skill.id === id ? { ...skill, [field]: value } : skill)),
    }));
  };

  const removeSkill = (id: string) => {
    setResume((prev) => ({
      ...prev,
      skills: prev.skills.filter((skill) => skill.id !== id),
    }));
  };

  // Projects
  const addProject = () => {
    const newProject: Project = {
      id: `proj_${Date.now()}`,
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      technologies: [],
    };

    setResume((prev) => ({
      ...prev,
      projects: [...prev.projects, newProject],
    }));
  };

  const updateProject = (id: string, field: keyof Project, value: any) => {
    setResume((prev) => ({
      ...prev,
      projects: prev.projects.map((proj) => (proj.id === id ? { ...proj, [field]: value } : proj)),
    }));
  };

  const removeProject = (id: string) => {
    setResume((prev) => ({
      ...prev,
      projects: prev.projects.filter((proj) => proj.id !== id),
    }));
  };

  // Certifications
  const addCertification = () => {
    const newCertification: Certification = {
      id: `cert_${Date.now()}`,
      name: "",
      issuer: "",
      date: "",
      description: "",
    };

    setResume((prev) => ({
      ...prev,
      certifications: [...prev.certifications, newCertification],
    }));
  };

  const updateCertification = (id: string, field: keyof Certification, value: string) => {
    setResume((prev) => ({
      ...prev,
      certifications: prev.certifications.map((cert) =>
        cert.id === id ? { ...cert, [field]: value } : cert
      ),
    }));
  };

  const removeCertification = (id: string) => {
    setResume((prev) => ({
      ...prev,
      certifications: prev.certifications.filter((cert) => cert.id !== id),
    }));
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>Loading resume...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}>
      <ScrollView style={styles.scrollView}>
        {/* Template updating overlay */}
        {updatingTemplate && (
          <View style={styles.updatingOverlay}>
            <View style={styles.updatingCard}>
              <ActivityIndicator size="large" color="#3498db" />
              <Text style={styles.updatingText}>Applying template...</Text>
            </View>
          </View>
        )}
        <View style={styles.header}>
          {/* Title and Template Badge Row */}
          <View style={styles.headerTitleRow}>
            <TextInput
              style={styles.titleInput}
              value={resume.title}
              onChangeText={(text) => setResume((prev) => ({ ...prev, title: text }))}
              placeholder="Resume Title"
            />
            <View style={styles.templateBadge}>
              <Ionicons name="document-text-outline" size={16} color={colors.primary} />
              <Text style={styles.templateName}>
                {resume.templateId.charAt(0).toUpperCase() + resume.templateId.slice(1)}
              </Text>
            </View>
          </View>

          {/* Action Buttons Row */}
          <View style={styles.actionButtonsRow}>
            {/* View & Export Buttons */}
            <Button
              title="View"
              onPress={handlePreview}
              variant="primary"
              size="small"
              icon={
                <Ionicons name="eye-outline" size={14} color="#fff" style={styles.buttonIcon} />
              }
              style={styles.actionButton}
            />
            <Button
              title="Export"
              onPress={() => router.push(`/resume/${id}/export`)}
              variant="outline"
              size="small"
              icon={
                <Ionicons
                  name="download-outline"
                  size={14}
                  color={colors.primary}
                  style={styles.buttonIcon}
                />
              }
              style={styles.actionButton}
            />

            {/* Customize Buttons */}
            <Button
              title="Template"
              onPress={handleChangeTemplate}
              variant="outline"
              size="small"
              icon={
                <Ionicons
                  name="grid-outline"
                  size={14}
                  color={colors.primary}
                  style={styles.buttonIcon}
                />
              }
              style={styles.actionButton}
            />
            <Button
              title="Sections"
              onPress={() => router.push(`/resume/${id}/sections`)}
              variant="outline"
              size="small"
              icon={
                <Ionicons
                  name="list-outline"
                  size={14}
                  color={colors.primary}
                  style={styles.buttonIcon}
                />
              }
              style={styles.actionButton}
            />
            <Button
              title="Reorder"
              onPress={() => router.push(`/resume/${id}/reorder-sections`)}
              variant="outline"
              size="small"
              icon={
                <Ionicons
                  name="swap-vertical-outline"
                  size={14}
                  color={colors.primary}
                  style={styles.buttonIcon}
                />
              }
              style={styles.actionButton}
            />
          </View>

          {/* Last Updated Text */}
          <Text style={styles.lastUpdated}>
            Last updated: {new Date(resume.updatedAt).toLocaleDateString()}
          </Text>
        </View>

        {/* Personal Information */}
        <FormSection title="Personal Information" icon="person">
          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Text style={styles.label}>First Name</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.firstName}
                onChangeText={(text) => updatePersonalInfo("firstName", text)}
                placeholder="First Name"
              />
            </View>
            <View style={styles.formColumn}>
              <Text style={styles.label}>Last Name</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.lastName}
                onChangeText={(text) => updatePersonalInfo("lastName", text)}
                placeholder="Last Name"
              />
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.email}
                onChangeText={(text) => updatePersonalInfo("email", text)}
                placeholder="Email"
                keyboardType="email-address"
              />
            </View>
            <View style={styles.formColumn}>
              <Text style={styles.label}>Phone</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.phone}
                onChangeText={(text) => updatePersonalInfo("phone", text)}
                placeholder="Phone"
                keyboardType="phone-pad"
              />
            </View>
          </View>

          <Text style={styles.label}>Professional Title</Text>
          <TextInput
            style={styles.input}
            value={resume.personalInfo.title}
            onChangeText={(text) => updatePersonalInfo("title", text)}
            placeholder="e.g. Software Engineer"
          />

          <Text style={styles.label}>Address</Text>
          <TextInput
            style={styles.input}
            value={resume.personalInfo.address}
            onChangeText={(text) => updatePersonalInfo("address", text)}
            placeholder="Street Address"
          />

          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Text style={styles.label}>City</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.city}
                onChangeText={(text) => updatePersonalInfo("city", text)}
                placeholder="City"
              />
            </View>
            <View style={styles.formColumn}>
              <Text style={styles.label}>State</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.state}
                onChangeText={(text) => updatePersonalInfo("state", text)}
                placeholder="State"
              />
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={styles.formColumn}>
              <Text style={styles.label}>Zip Code</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.zipCode}
                onChangeText={(text) => updatePersonalInfo("zipCode", text)}
                placeholder="Zip Code"
              />
            </View>
            <View style={styles.formColumn}>
              <Text style={styles.label}>Country</Text>
              <TextInput
                style={styles.input}
                value={resume.personalInfo.country}
                onChangeText={(text) => updatePersonalInfo("country", text)}
                placeholder="Country"
              />
            </View>
          </View>

          <Text style={styles.label}>Professional Summary</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={resume.personalInfo.summary}
            onChangeText={(text) => updatePersonalInfo("summary", text)}
            placeholder="Write a brief summary of your professional background and goals"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </FormSection>

        {/* Education */}
        <FormSection title="Education" icon="school">
          {resume.education.map((edu, index) => (
            <View key={edu.id} style={styles.sectionItem}>
              <View style={styles.sectionItemHeader}>
                <Text style={styles.sectionItemTitle}>Education #{index + 1}</Text>
                <TouchableOpacity onPress={() => removeEducation(edu.id)}>
                  <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                </TouchableOpacity>
              </View>

              <Text style={styles.label}>Institution</Text>
              <TextInput
                style={styles.input}
                value={edu.institution}
                onChangeText={(text) => updateEducation(edu.id, "institution", text)}
                placeholder="University or School Name"
              />

              <View style={styles.formRow}>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>Degree</Text>
                  <TextInput
                    style={styles.input}
                    value={edu.degree}
                    onChangeText={(text) => updateEducation(edu.id, "degree", text)}
                    placeholder="e.g. Bachelor's"
                  />
                </View>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>Field of Study</Text>
                  <TextInput
                    style={styles.input}
                    value={edu.fieldOfStudy}
                    onChangeText={(text) => updateEducation(edu.id, "fieldOfStudy", text)}
                    placeholder="e.g. Computer Science"
                  />
                </View>
              </View>

              <View style={styles.formRow}>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>Start Date</Text>
                  <TextInput
                    style={styles.input}
                    value={edu.startDate}
                    onChangeText={(text) => updateEducation(edu.id, "startDate", text)}
                    placeholder="MM/YYYY"
                  />
                </View>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>End Date</Text>
                  <TextInput
                    style={styles.input}
                    value={edu.endDate}
                    onChangeText={(text) => updateEducation(edu.id, "endDate", text)}
                    placeholder="MM/YYYY or Present"
                  />
                </View>
              </View>

              <Text style={styles.label}>Location</Text>
              <TextInput
                style={styles.input}
                value={edu.location}
                onChangeText={(text) => updateEducation(edu.id, "location", text)}
                placeholder="City, State, Country"
              />

              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={edu.description}
                onChangeText={(text) => updateEducation(edu.id, "description", text)}
                placeholder="Describe your studies, achievements, etc."
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          ))}

          <Button
            title="Add Education"
            onPress={addEducation}
            variant="outline"
            size="small"
            icon={
              <Ionicons
                name="add-outline"
                size={16}
                color={colors.primary}
                style={styles.buttonIcon}
              />
            }
          />
        </FormSection>

        {/* Experience */}
        <FormSection title="Experience" icon="briefcase">
          {resume.experience.map((exp, index) => (
            <View key={exp.id} style={styles.sectionItem}>
              <View style={styles.sectionItemHeader}>
                <Text style={styles.sectionItemTitle}>Experience #{index + 1}</Text>
                <TouchableOpacity onPress={() => removeExperience(exp.id)}>
                  <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                </TouchableOpacity>
              </View>

              <Text style={styles.label}>Company</Text>
              <TextInput
                style={styles.input}
                value={exp.company}
                onChangeText={(text) => updateExperience(exp.id, "company", text)}
                placeholder="Company Name"
              />

              <Text style={styles.label}>Position</Text>
              <TextInput
                style={styles.input}
                value={exp.position}
                onChangeText={(text) => updateExperience(exp.id, "position", text)}
                placeholder="Job Title"
              />

              <View style={styles.formRow}>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>Start Date</Text>
                  <TextInput
                    style={styles.input}
                    value={exp.startDate}
                    onChangeText={(text) => updateExperience(exp.id, "startDate", text)}
                    placeholder="MM/YYYY"
                  />
                </View>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>End Date</Text>
                  <TextInput
                    style={styles.input}
                    value={exp.endDate}
                    onChangeText={(text) => updateExperience(exp.id, "endDate", text)}
                    placeholder="MM/YYYY or Present"
                  />
                </View>
              </View>

              <Text style={styles.label}>Location</Text>
              <TextInput
                style={styles.input}
                value={exp.location}
                onChangeText={(text) => updateExperience(exp.id, "location", text)}
                placeholder="City, State, Country"
              />

              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={exp.description}
                onChangeText={(text) => updateExperience(exp.id, "description", text)}
                placeholder="Describe your role and responsibilities"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          ))}

          <Button
            title="Add Experience"
            onPress={addExperience}
            variant="outline"
            size="small"
            icon={
              <Ionicons
                name="add-outline"
                size={16}
                color={colors.primary}
                style={styles.buttonIcon}
              />
            }
          />
        </FormSection>

        {/* Skills */}
        <FormSection title="Skills" icon="construct">
          {resume.skills.map((skill) => (
            <View key={skill.id} style={styles.skillItem}>
              <TextInput
                style={styles.skillInput}
                value={skill.name}
                onChangeText={(text) => updateSkill(skill.id, "name", text)}
                placeholder="Skill name"
              />
              <TouchableOpacity onPress={() => removeSkill(skill.id)}>
                <Ionicons name="close-circle" size={20} color="#e74c3c" />
              </TouchableOpacity>
            </View>
          ))}

          <Button
            title="Add Skill"
            onPress={addSkill}
            variant="outline"
            size="small"
            icon={
              <Ionicons
                name="add-outline"
                size={16}
                color={colors.primary}
                style={styles.buttonIcon}
              />
            }
          />
        </FormSection>

        {/* Projects */}
        <FormSection title="Projects" icon="code">
          {resume.projects.map((project) => (
            <View key={project.id} style={styles.sectionItem}>
              <View style={styles.sectionItemHeader}>
                <Text style={styles.sectionItemTitle}>{project.name || "New Project"}</Text>
                <TouchableOpacity onPress={() => removeProject(project.id)}>
                  <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                </TouchableOpacity>
              </View>

              <Text style={styles.label}>Project Name</Text>
              <TextInput
                style={styles.input}
                value={project.name}
                onChangeText={(text) => updateProject(project.id, "name", text)}
                placeholder="Project Name"
              />

              <View style={styles.formRow}>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>Start Date</Text>
                  <TextInput
                    style={styles.input}
                    value={project.startDate}
                    onChangeText={(text) => updateProject(project.id, "startDate", text)}
                    placeholder="MM/YYYY"
                  />
                </View>
                <View style={styles.formColumn}>
                  <Text style={styles.label}>End Date</Text>
                  <TextInput
                    style={styles.input}
                    value={project.endDate}
                    onChangeText={(text) => updateProject(project.id, "endDate", text)}
                    placeholder="MM/YYYY or Present"
                  />
                </View>
              </View>

              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={project.description}
                onChangeText={(text) => updateProject(project.id, "description", text)}
                placeholder="Describe your project"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />

              <Text style={styles.label}>Technologies</Text>
              <TextInput
                style={styles.input}
                value={project.technologies.join(", ")}
                onChangeText={(text) =>
                  updateProject(
                    project.id,
                    "technologies",
                    text.split(",").map((t) => t.trim())
                  )
                }
                placeholder="Comma-separated list of technologies"
              />
            </View>
          ))}

          <Button
            title="Add Project"
            onPress={addProject}
            variant="outline"
            size="small"
            icon={
              <Ionicons
                name="add-outline"
                size={16}
                color={colors.primary}
                style={styles.buttonIcon}
              />
            }
          />
        </FormSection>

        {/* Certifications */}
        <FormSection title="Certifications" icon="ribbon">
          {resume.certifications.map((cert) => (
            <View key={cert.id} style={styles.sectionItem}>
              <View style={styles.sectionItemHeader}>
                <Text style={styles.sectionItemTitle}>{cert.name || "New Certification"}</Text>
                <TouchableOpacity onPress={() => removeCertification(cert.id)}>
                  <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                </TouchableOpacity>
              </View>

              <Text style={styles.label}>Certification Name</Text>
              <TextInput
                style={styles.input}
                value={cert.name}
                onChangeText={(text) => updateCertification(cert.id, "name", text)}
                placeholder="Certification Name"
              />

              <Text style={styles.label}>Issuer</Text>
              <TextInput
                style={styles.input}
                value={cert.issuer}
                onChangeText={(text) => updateCertification(cert.id, "issuer", text)}
                placeholder="Issuing Organization"
              />

              <Text style={styles.label}>Date</Text>
              <TextInput
                style={styles.input}
                value={cert.date}
                onChangeText={(text) => updateCertification(cert.id, "date", text)}
                placeholder="MM/YYYY"
              />

              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={cert.description}
                onChangeText={(text) => updateCertification(cert.id, "description", text)}
                placeholder="Description of the certification"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          ))}

          <Button
            title="Add Certification"
            onPress={addCertification}
            variant="outline"
            size="small"
            icon={
              <Ionicons
                name="add-outline"
                size={16}
                color={colors.primary}
                style={styles.buttonIcon}
              />
            }
          />
        </FormSection>

        <View style={styles.saveButtonContainer}>
          <Button
            title={saving ? "Saving..." : "Save Resume"}
            onPress={handleSave}
            variant="primary"
            size="medium"
            disabled={saving}
            loading={saving}
            style={styles.saveButton}
            icon={
              !saving && (
                <Ionicons name="save-outline" size={18} color="#fff" style={styles.buttonIcon} />
              )
            }
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
  },
  updatingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  updatingCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    width: "80%",
    maxWidth: 300,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  updatingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#333",
    textAlign: "center",
  },
  header: {
    padding: 12,
    paddingBottom: 8,
    backgroundColor: colors.card,
    borderRadius: borderRadius.md,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
    ...shadows.sm,
  },
  headerTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  titleInput: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.text,
    flex: 1,
    marginRight: 12,
    paddingVertical: 0,
  },
  lastUpdated: {
    fontSize: 11,
    color: colors.textSecondary,
    marginTop: 10,
    textAlign: "right",
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 6,
  },
  templateBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: `${colors.primary}10`,
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: borderRadius.sm,
  },
  actionButtonsRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "flex-start",
    marginBottom: 8,
    marginHorizontal: -3,
  },
  actionButton: {
    flexGrow: 1,
    flexBasis: "auto",
    minWidth: 90,
    maxWidth: 120,
    margin: 3,
  },
  customizeSection: {
    marginTop: 16,
    marginBottom: 24,
  },
  sectionLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: "600",
    color: colors.primary,
    marginBottom: 12,
    paddingLeft: 4,
    letterSpacing: 0.3,
  },
  customizeButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  customizeButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  actionGroup: {
    marginBottom: 24,
    backgroundColor: colors.card,
    borderRadius: borderRadius.md,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
    ...shadows.sm,
  },
  actionGroupTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: "600",
    color: colors.primary,
    marginBottom: 12,
    paddingLeft: 4,
    letterSpacing: 0.3,
  },
  actionGroupButtons: {
    flexDirection: "column",
    gap: 12,
  },
  formRow: {
    flexDirection: "row",
    marginHorizontal: -8,
  },
  formColumn: {
    flex: 1,
    paddingHorizontal: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    color: "#555",
    marginBottom: 4,
    marginTop: 12,
  },
  input: {
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    color: "#333",
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  sectionItem: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  sectionItemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  sectionItemTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  skillItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  skillInput: {
    flex: 1,
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    color: "#333",
    marginRight: 8,
  },
  saveButtonContainer: {
    padding: 16,
    marginBottom: 100, // Increased to ensure it's above the tab bar
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.card,
    ...shadows.md,
  },
  saveButton: {
    width: "100%",
    height: 50,
    borderRadius: borderRadius.md,
  },
  buttonIcon: {
    marginRight: 4,
  },
  templateInfo: {
    marginVertical: 8,
    padding: 8,
    backgroundColor: "#f0f7ff",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "#3498db",
  },
  templateLabel: {
    fontSize: 14,
    color: "#666",
  },
  templateName: {
    fontWeight: "600",
    color: colors.primary,
    marginLeft: 4,
    fontSize: 12,
  },
});
