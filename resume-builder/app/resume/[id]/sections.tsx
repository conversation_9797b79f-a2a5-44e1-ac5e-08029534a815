import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { ResumeData } from "../../../types";
import { getResumeById } from "../../../utils/storage";

// Define the sections available for editing
const SECTIONS = [
  {
    id: "personal",
    title: "Personal Information",
    icon: "person-outline",
    description: "Your name, contact details, and professional summary",
  },
  {
    id: "education",
    title: "Education",
    icon: "school-outline",
    description: "Your academic background and qualifications",
  },
  {
    id: "experience",
    title: "Experience",
    icon: "briefcase-outline",
    description: "Your work history and professional experience",
  },
  {
    id: "skills",
    title: "Skills",
    icon: "construct-outline",
    description: "Technical and professional skills",
  },
  {
    id: "projects",
    title: "Projects",
    icon: "code-outline",
    description: "Notable projects you've worked on",
  },
  {
    id: "certifications",
    title: "Certifications",
    icon: "ribbon-outline",
    description: "Professional certifications and credentials",
  },
];

export default function SectionsScreen() {
  const params = useLocalSearchParams();
  const id = params.id as string;

  const [resume, setResume] = useState<ResumeData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadResume();
  }, [id]);

  const loadResume = async () => {
    try {
      const data = await getResumeById(id);
      if (data) {
        setResume(data);
      } else {
        console.error("Resume not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading resume:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSectionPress = (sectionId: string) => {
    router.push(`/resume/${id}/edit/${sectionId}`);
  };

  const renderSectionItem = ({ item }: { item: (typeof SECTIONS)[0] }) => {
    // Determine completion status based on resume data
    let completionStatus = "Not started";
    let isComplete = false;

    if (resume) {
      switch (item.id) {
        case "personal":
          isComplete = Boolean(
            resume.personalInfo.firstName &&
              resume.personalInfo.lastName &&
              resume.personalInfo.email
          );
          break;
        case "education":
          isComplete = resume.education.length > 0;
          break;
        case "experience":
          isComplete = resume.experience.length > 0;
          break;
        case "skills":
          isComplete = resume.skills.length > 0;
          break;
        case "projects":
          isComplete = resume.projects.length > 0;
          break;
        case "certifications":
          isComplete = resume.certifications.length > 0;
          break;
      }
      completionStatus = isComplete ? "Complete" : "Incomplete";
    }

    return (
      <TouchableOpacity
        style={[styles.sectionItem, isComplete && styles.sectionItemComplete]}
        onPress={() => handleSectionPress(item.id)}
        activeOpacity={0.7}>
        <View style={styles.sectionIcon}>
          <Ionicons name={item.icon as any} size={24} color={isComplete ? "#27ae60" : "#3498db"} />
        </View>
        <View style={styles.sectionContent}>
          <Text style={styles.sectionTitle}>{item.title}</Text>
          <Text style={styles.sectionDescription}>{item.description}</Text>
          <View style={[styles.statusBadge, isComplete && styles.statusBadgeComplete]}>
            <Text style={[styles.statusText, isComplete && styles.statusTextComplete]}>
              {completionStatus}
            </Text>
          </View>
        </View>
        <Ionicons name="chevron-forward" size={20} color="#999" />
      </TouchableOpacity>
    );
  };

  const handlePreview = () => {
    router.push(`/resume/${id}/preview`);
  };

  const handleBack = () => {
    router.push(`/resume/${id}`);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Edit Resume Sections</Text>
        <Text style={styles.subtitle}>Select a section to edit your resume information</Text>
      </View>

      <FlatList
        data={SECTIONS}
        renderItem={renderSectionItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
      />

      <View style={styles.footer}>
        <TouchableOpacity style={styles.footerButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={20} color="#3498db" />
          <Text style={styles.footerButtonText}>Back to Editor</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.footerButton} onPress={handlePreview}>
          <Ionicons name="eye-outline" size={20} color="#3498db" />
          <Text style={styles.footerButtonText}>Preview</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.footerButton}
          onPress={() => router.push(`/resume/${id}/reorder-sections`)}>
          <Ionicons name="swap-vertical-outline" size={20} color="#3498db" />
          <Text style={styles.footerButtonText}>Reorder Sections</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
  },
  listContent: {
    padding: 16,
  },
  sectionItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: "#3498db",
  },
  sectionItemComplete: {
    borderLeftColor: "#27ae60",
  },
  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f0f7ff",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  sectionContent: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  statusBadge: {
    alignSelf: "flex-start",
    backgroundColor: "#f0f7ff",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusBadgeComplete: {
    backgroundColor: "#e6f7ef",
  },
  statusText: {
    fontSize: 12,
    color: "#3498db",
  },
  statusTextComplete: {
    color: "#27ae60",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#eee",
  },
  footerButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
  },
  footerButtonText: {
    marginLeft: 8,
    color: "#3498db",
    fontWeight: "500",
  },
});
