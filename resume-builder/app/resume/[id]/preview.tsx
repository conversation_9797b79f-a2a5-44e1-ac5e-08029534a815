import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import { ActivityIndicator, Alert, StyleSheet, Text, View } from "react-native";
import { WebView } from "react-native-webview";
import Button from "../../../components/Button";
import { ResumeData } from "../../../types";
import { generatePDF, generateResumeHTML, sharePDF } from "../../../utils/pdf";
import { getResumeById } from "../../../utils/storage";

export default function ResumePreviewScreen() {
  const params = useLocalSearchParams();
  const id = params.id as string;

  const [resume, setResume] = useState<ResumeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [generatingPDF, setGeneratingPDF] = useState(false);
  const [pdfUri, setPdfUri] = useState<string | null>(null);

  const webViewRef = useRef<WebView>(null);

  useEffect(() => {
    loadResume();
  }, [id]);

  const loadResume = async () => {
    try {
      const data = await getResumeById(id);
      if (data) {
        setResume(data);
      } else {
        Alert.alert("Error", "Resume not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading resume:", error);
      Alert.alert("Error", "Failed to load resume");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.back();
  };

  const handleGeneratePDF = async () => {
    if (!resume) return;

    try {
      setGeneratingPDF(true);
      const uri = await generatePDF(resume);
      setPdfUri(uri);
      Alert.alert(
        "PDF Generated",
        "Your resume has been generated as a PDF. Would you like to share it?",
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          {
            text: "Share",
            onPress: () => handleSharePDF(uri),
          },
        ]
      );
    } catch (error) {
      console.error("Error generating PDF:", error);
      Alert.alert("Error", "Failed to generate PDF");
    } finally {
      setGeneratingPDF(false);
    }
  };

  const handleSharePDF = async (uri: string) => {
    try {
      await sharePDF(uri);
    } catch (error) {
      console.error("Error sharing PDF:", error);
      Alert.alert("Error", "Failed to share PDF");
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
      </View>
    );
  }

  if (!resume) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Resume not found</Text>
        <Button title="Go Back" onPress={() => router.back()} variant="outline" />
      </View>
    );
  }

  const html = generateResumeHTML(resume);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{resume.title}</Text>
        <Text style={styles.subtitle}>Preview</Text>
      </View>

      <View style={styles.previewContainer}>
        <WebView
          ref={webViewRef}
          source={{ html }}
          style={styles.webView}
          originWhitelist={["*"]}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          key={resume.templateId} // Add key to force re-render when template changes
          renderLoading={() => (
            <View style={styles.webViewLoading}>
              <ActivityIndicator size="large" color="#3498db" />
            </View>
          )}
          onLoadEnd={() => {
            console.log(`WebView loaded for template: ${resume.templateId}`);
          }}
        />
      </View>

      <View style={styles.actions}>
        <Button
          title="Edit Resume"
          onPress={handleEdit}
          variant="outline"
          style={styles.actionButton}
          icon={
            <Ionicons name="create-outline" size={20} color="#3498db" style={styles.buttonIcon} />
          }
        />
        <Button
          title={generatingPDF ? "Generating..." : "Generate PDF"}
          onPress={handleGeneratePDF}
          variant="primary"
          disabled={generatingPDF}
          loading={generatingPDF}
          style={styles.actionButton}
          icon={
            !generatingPDF && (
              <Ionicons name="document-outline" size={20} color="#fff" style={styles.buttonIcon} />
            )
          }
        />
        {pdfUri && (
          <Button
            title="Share PDF"
            onPress={() => handleSharePDF(pdfUri)}
            variant="primary"
            style={styles.actionButton}
            icon={
              <Ionicons name="share-outline" size={20} color="#fff" style={styles.buttonIcon} />
            }
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: "#e74c3c",
    marginBottom: 16,
  },
  header: {
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  subtitle: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  previewContainer: {
    flex: 1,
    backgroundColor: "#fff",
    margin: 16,
    borderRadius: 12,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  webView: {
    flex: 1,
  },
  webViewLoading: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#eee",
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
