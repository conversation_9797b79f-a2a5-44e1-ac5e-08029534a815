import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Button from "../components/Button";
import FormSection from "../components/FormSection";
import Input from "../components/Input";
import { colors, spacing, typography, borderRadius } from "../constants/theme";

export default function UIDemoScreen() {
  const [text, setText] = useState("");
  const [password, setPassword] = useState("");
  
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', colors.backgroundDark]}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />
      
      <View style={styles.header}>
        <Ionicons
          name="arrow-back"
          size={24}
          color={colors.text}
          onPress={() => router.back()}
          style={styles.backButton}
        />
        <Text style={styles.title}>UI Components</Text>
      </View>
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Buttons Section */}
        <FormSection 
          title="Buttons" 
          icon="construct"
          accentColor={colors.primary}
        >
          <Text style={styles.sectionSubtitle}>Primary Buttons</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Small"
              size="small"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Medium"
              size="medium"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Large"
              size="large"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
            />
          </View>
          
          <Text style={styles.sectionSubtitle}>Button Variants</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Primary"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Secondary"
              variant="secondary"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Outline"
              variant="outline"
              onPress={() => {}}
              style={styles.button}
            />
          </View>
          <View style={styles.buttonRow}>
            <Button
              title="Success"
              variant="success"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Danger"
              variant="danger"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Text"
              variant="text"
              onPress={() => {}}
              style={styles.button}
            />
          </View>
          
          <Text style={styles.sectionSubtitle}>With Icons</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Add"
              variant="primary"
              icon={<Ionicons name="add" size={18} color="#fff" />}
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Edit"
              variant="outline"
              icon={<Ionicons name="create" size={18} color={colors.primary} />}
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Delete"
              variant="danger"
              icon={<Ionicons name="trash" size={18} color="#fff" />}
              onPress={() => {}}
              style={styles.button}
            />
          </View>
          
          <Text style={styles.sectionSubtitle}>Rounded Buttons</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Primary"
              variant="primary"
              rounded
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Outline"
              variant="outline"
              rounded
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Icon"
              variant="primary"
              rounded
              icon={<Ionicons name="star" size={18} color="#fff" />}
              onPress={() => {}}
              style={styles.button}
            />
          </View>
          
          <Text style={styles.sectionSubtitle}>States</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Loading"
              variant="primary"
              loading
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Disabled"
              variant="primary"
              disabled
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Full Width"
              variant="primary"
              fullWidth
              onPress={() => {}}
              style={styles.fullWidthButton}
            />
          </View>
        </FormSection>
        
        {/* Inputs Section */}
        <FormSection 
          title="Inputs" 
          icon="create"
          accentColor={colors.secondary}
        >
          <Text style={styles.sectionSubtitle}>Variants</Text>
          
          <Input
            label="Outlined Input"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            variant="outlined"
          />
          
          <Input
            label="Filled Input"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            variant="filled"
          />
          
          <Input
            label="Underlined Input"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            variant="underlined"
          />
          
          <Text style={styles.sectionSubtitle}>With Icons</Text>
          
          <Input
            label="Left Icon"
            placeholder="Search..."
            value={text}
            onChangeText={setText}
            leftIcon="search"
          />
          
          <Input
            label="Right Icon"
            placeholder="Enter email"
            value={text}
            onChangeText={setText}
            rightIcon="mail"
          />
          
          <Input
            label="Password Input"
            placeholder="Enter password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          <Input
            label="Error State"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            error="This field is required"
          />
        </FormSection>
        
        {/* Form Sections */}
        <FormSection 
          title="Form Sections" 
          icon="list"
          accentColor={colors.accent}
        >
          <Text style={styles.sectionSubtitle}>Different Colors</Text>
          
          <FormSection
            title="Primary Section"
            icon="star"
            accentColor={colors.primary}
          >
            <Text style={styles.demoText}>This is a primary section</Text>
          </FormSection>
          
          <FormSection
            title="Secondary Section"
            icon="heart"
            accentColor={colors.secondary}
          >
            <Text style={styles.demoText}>This is a secondary section</Text>
          </FormSection>
          
          <FormSection
            title="Success Section"
            icon="checkmark"
            accentColor={colors.success}
          >
            <Text style={styles.demoText}>This is a success section</Text>
          </FormSection>
          
          <Text style={styles.sectionSubtitle}>Collapsible Section</Text>
          
          <FormSection
            title="Click to Toggle"
            icon="chevron-down"
            accentColor={colors.warning}
            collapsible
            initiallyCollapsed
          >
            <Text style={styles.demoText}>This section can be collapsed</Text>
            <Text style={styles.demoText}>Click the header to toggle</Text>
          </FormSection>
        </FormSection>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.md,
  },
  backButton: {
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: "700",
    color: colors.text,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: spacing.lg,
    paddingBottom: spacing.xxl * 2,
  },
  sectionSubtitle: {
    fontSize: typography.fontSize.md,
    fontWeight: "600",
    color: colors.textSecondary,
    marginBottom: spacing.sm,
    marginTop: spacing.sm,
  },
  buttonRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: spacing.md,
  },
  button: {
    margin: spacing.xs,
  },
  fullWidthButton: {
    marginVertical: spacing.sm,
  },
  demoText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginBottom: spacing.sm,
  },
});
