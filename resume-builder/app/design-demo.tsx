import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Button from "../components/Button";
import FloatingActionButton from "../components/FloatingActionButton";
import { colors, spacing, typography, borderRadius, shadows } from "../constants/theme";

export default function DesignDemoScreen() {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', colors.backgroundDark]}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />
      
      <View style={styles.header}>
        <Ionicons
          name="arrow-back"
          size={24}
          color={colors.text}
          onPress={() => router.back()}
          style={styles.backButton}
        />
        <Text style={styles.title}>Design System</Text>
      </View>
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Colors</Text>
          <View style={styles.colorRow}>
            <View style={[styles.colorBox, { backgroundColor: colors.primary }]}>
              <Text style={styles.colorText}>Primary</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: colors.secondary }]}>
              <Text style={styles.colorText}>Secondary</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: colors.accent }]}>
              <Text style={styles.colorText}>Accent</Text>
            </View>
          </View>
          <View style={styles.colorRow}>
            <View style={[styles.colorBox, { backgroundColor: colors.success }]}>
              <Text style={styles.colorText}>Success</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: colors.warning }]}>
              <Text style={styles.colorText}>Warning</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: colors.error }]}>
              <Text style={styles.colorText}>Error</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Typography</Text>
          <View style={styles.card}>
            <Text style={styles.displayText}>Display Text</Text>
            <Text style={styles.headingText}>Heading Text</Text>
            <Text style={styles.subheadingText}>Subheading Text</Text>
            <Text style={styles.bodyText}>Body Text - The quick brown fox jumps over the lazy dog.</Text>
            <Text style={styles.captionText}>Caption Text - Small details and information</Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Buttons</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Primary"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Secondary"
              variant="secondary"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Outline"
              variant="outline"
              onPress={() => {}}
              style={styles.button}
            />
          </View>
          <View style={styles.buttonRow}>
            <Button
              title="Success"
              variant="success"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Danger"
              variant="danger"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="With Icon"
              variant="primary"
              icon={<Ionicons name="star" size={16} color="#fff" />}
              onPress={() => {}}
              style={styles.button}
            />
          </View>
          <View style={styles.buttonRow}>
            <Button
              title="Rounded"
              variant="primary"
              rounded
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Loading"
              variant="primary"
              loading
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Disabled"
              variant="primary"
              disabled
              onPress={() => {}}
              style={styles.button}
            />
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cards</Text>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Standard Card</Text>
            <Text style={styles.cardText}>
              This is a standard card with a clean design and subtle shadow.
            </Text>
          </View>
          
          <View style={styles.gradientCardContainer}>
            <LinearGradient
              colors={colors.gradientPrimary}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.gradientCard}
            >
              <Text style={styles.gradientCardTitle}>Gradient Card</Text>
              <Text style={styles.gradientCardText}>
                This card uses a beautiful gradient background.
              </Text>
            </LinearGradient>
          </View>
          
          <View style={styles.accentCardContainer}>
            <LinearGradient
              colors={colors.gradientSecondary}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.accentBar}
            />
            <View style={styles.accentCard}>
              <Text style={styles.cardTitle}>Accent Card</Text>
              <Text style={styles.cardText}>
                This card has a colorful accent bar on the left side.
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Floating Action Button</Text>
          <View style={styles.fabDemoContainer}>
            <View style={styles.fabDemo}>
              <FloatingActionButton size="small" icon="add" />
              <Text style={styles.fabLabel}>Small</Text>
            </View>
            <View style={styles.fabDemo}>
              <FloatingActionButton size="medium" icon="add" />
              <Text style={styles.fabLabel}>Medium</Text>
            </View>
            <View style={styles.fabDemo}>
              <FloatingActionButton size="large" icon="add" />
              <Text style={styles.fabLabel}>Large</Text>
            </View>
          </View>
          <View style={styles.fabDemoContainer}>
            <View style={styles.fabDemo}>
              <FloatingActionButton color={colors.primary} icon="add" />
              <Text style={styles.fabLabel}>Primary</Text>
            </View>
            <View style={styles.fabDemo}>
              <FloatingActionButton color={colors.secondary} icon="heart" />
              <Text style={styles.fabLabel}>Secondary</Text>
            </View>
            <View style={styles.fabDemo}>
              <FloatingActionButton color={colors.success} icon="checkmark" />
              <Text style={styles.fabLabel}>Success</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.md,
  },
  backButton: {
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: spacing.lg,
    paddingBottom: spacing.xxl * 2,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  colorRow: {
    flexDirection: "row",
    marginBottom: spacing.md,
  },
  colorBox: {
    flex: 1,
    height: 80,
    borderRadius: borderRadius.md,
    marginHorizontal: spacing.xs,
    justifyContent: "center",
    alignItems: "center",
    ...shadows.md,
  },
  colorText: {
    color: "#ffffff",
    fontWeight: typography.fontWeight.bold,
    fontSize: typography.fontSize.sm,
  },
  card: {
    backgroundColor: colors.card,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.md,
    ...shadows.md,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  cardText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  gradientCardContainer: {
    borderRadius: borderRadius.lg,
    marginBottom: spacing.md,
    ...shadows.md,
    overflow: 'hidden',
  },
  gradientCard: {
    padding: spacing.lg,
  },
  gradientCardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: "#ffffff",
    marginBottom: spacing.sm,
  },
  gradientCardText: {
    fontSize: typography.fontSize.md,
    color: "rgba(255, 255, 255, 0.9)",
    lineHeight: 22,
  },
  accentCardContainer: {
    flexDirection: 'row',
    borderRadius: borderRadius.lg,
    marginBottom: spacing.md,
    ...shadows.md,
    overflow: 'hidden',
    backgroundColor: colors.card,
  },
  accentBar: {
    width: 6,
  },
  accentCard: {
    flex: 1,
    padding: spacing.lg,
  },
  buttonRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: spacing.md,
  },
  button: {
    margin: spacing.xs,
  },
  displayText: {
    fontSize: typography.fontSize.display,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  headingText: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  subheadingText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  bodyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
    lineHeight: 22,
  },
  captionText: {
    fontSize: typography.fontSize.sm,
    color: colors.textLight,
    lineHeight: 18,
  },
  fabDemoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.lg,
  },
  fabDemo: {
    alignItems: 'center',
  },
  fabLabel: {
    marginTop: 60, // Space for the FAB
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
});
