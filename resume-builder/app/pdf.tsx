import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
  ScrollView,
} from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Button from "../components/Button";
import { getResumeById } from "../utils/storage";
import { generatePDF, sharePDF } from "../utils/pdf";
import { ResumeData } from "../types";

export default function PDFExportScreen() {
  const params = useLocalSearchParams();
  const id = params.id as string;

  const [resume, setResume] = useState<ResumeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [pdfUri, setPdfUri] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadResume();
    } else {
      setLoading(false);
    }
  }, [id]);

  const loadResume = async () => {
    try {
      const data = await getResumeById(id);
      if (data) {
        setResume(data);
      } else {
        Alert.alert("Error", "Resume not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading resume:", error);
      Alert.alert("Error", "Failed to load resume");
    } finally {
      setLoading(false);
    }
  };

  const handleGeneratePDF = async () => {
    if (!resume) return;

    try {
      setGenerating(true);
      const uri = await generatePDF(resume);
      setPdfUri(uri);
    } catch (error) {
      console.error("Error generating PDF:", error);
      Alert.alert("Error", "Failed to generate PDF");
    } finally {
      setGenerating(false);
    }
  };

  const handleSharePDF = async () => {
    if (!pdfUri) return;

    try {
      await sharePDF(pdfUri);
    } catch (error) {
      console.error("Error sharing PDF:", error);
      Alert.alert("Error", "Failed to share PDF");
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
      </View>
    );
  }

  if (!resume) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={60} color="#e74c3c" />
        <Text style={styles.errorTitle}>Resume Not Found</Text>
        <Text style={styles.errorText}>
          The resume you're trying to export could not be found.
        </Text>
        <Button
          title="Go Back"
          onPress={() => router.back()}
          variant="outline"
          style={styles.errorButton}
        />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.header}>
        <Ionicons name="document-text" size={60} color="#3498db" />
        <Text style={styles.title}>Export Resume as PDF</Text>
        <Text style={styles.subtitle}>
          Generate a professional PDF version of your resume that you can share
          or print.
        </Text>
      </View>

      <View style={styles.resumeInfo}>
        <Text style={styles.resumeTitle}>{resume.title}</Text>
        <Text style={styles.resumeDetails}>
          {resume.personalInfo.firstName} {resume.personalInfo.lastName}
        </Text>
        <Text style={styles.resumeDetails}>{resume.personalInfo.title}</Text>
        <Text style={styles.resumeDetails}>
          Template: {resume.templateId.charAt(0).toUpperCase() + resume.templateId.slice(1)}
        </Text>
      </View>

      {!pdfUri ? (
        <Button
          title={generating ? "Generating PDF..." : "Generate PDF"}
          onPress={handleGeneratePDF}
          variant="primary"
          size="large"
          disabled={generating}
          loading={generating}
          style={styles.generateButton}
          icon={
            !generating && (
              <Ionicons name="document-outline" size={20} color="#fff" style={styles.buttonIcon} />
            )
          }
        />
      ) : (
        <View style={styles.successContainer}>
          <View style={styles.successIcon}>
            <Ionicons name="checkmark-circle" size={60} color="#2ecc71" />
          </View>
          <Text style={styles.successTitle}>PDF Generated Successfully!</Text>
          <Text style={styles.successText}>
            Your resume has been converted to PDF format and is ready to share.
          </Text>

          <Button
            title="Share PDF"
            onPress={handleSharePDF}
            variant="primary"
            size="large"
            style={styles.shareButton}
            icon={<Ionicons name="share-outline" size={20} color="#fff" style={styles.buttonIcon} />}
          />

          <Button
            title="Generate Again"
            onPress={handleGeneratePDF}
            variant="outline"
            style={styles.regenerateButton}
            icon={<Ionicons name="refresh-outline" size={20} color="#3498db" style={styles.buttonIcon} />}
          />
        </View>
      )}

      <Button
        title="Back to Resume"
        onPress={() => router.back()}
        variant="outline"
        style={styles.backButton}
        icon={<Ionicons name="arrow-back-outline" size={20} color="#3498db" style={styles.buttonIcon} />}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
  },
  errorButton: {
    width: 200,
  },
  header: {
    alignItems: "center",
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  resumeInfo: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resumeTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  resumeDetails: {
    fontSize: 16,
    color: "#666",
    marginBottom: 4,
  },
  generateButton: {
    marginBottom: 16,
  },
  successContainer: {
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
    textAlign: "center",
  },
  successText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
  },
  shareButton: {
    width: "100%",
    marginBottom: 12,
  },
  regenerateButton: {
    width: "100%",
  },
  backButton: {
    marginTop: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
