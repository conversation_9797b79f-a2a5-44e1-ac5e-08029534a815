import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { Image, ScrollView, StyleSheet, Text, View } from "react-native";
import Button from "../components/Button";

export default function Index() {
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Image source={require("../assets/logo.png")} style={styles.logo} resizeMode="contain" />
        <Text style={styles.title}>Professional Resume Builder</Text>
        <Text style={styles.subtitle}>
          Create, customize, and export professional resumes in minutes
        </Text>
      </View>

      <View style={styles.features}>
        <View style={styles.featureItem}>
          <View style={styles.featureIcon}>
            <Ionicons name="document-text-outline" size={24} color="#3498db" />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Multiple Templates</Text>
            <Text style={styles.featureDescription}>
              Choose from a variety of professional templates to make your resume stand out
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <View style={styles.featureIcon}>
            <Ionicons name="eye-outline" size={24} color="#3498db" />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Live Preview</Text>
            <Text style={styles.featureDescription}>
              See changes to your resume in real-time as you edit
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <View style={styles.featureIcon}>
            <Ionicons name="time-outline" size={24} color="#3498db" />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Version History</Text>
            <Text style={styles.featureDescription}>
              Keep track of all changes and revert to previous versions if needed
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <View style={styles.featureIcon}>
            <Ionicons name="download-outline" size={24} color="#3498db" />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Export as PDF</Text>
            <Text style={styles.featureDescription}>
              Download your resume as a professional PDF ready to share
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.actions}>
        <Button
          title="Create New Resume"
          onPress={() => router.push("/templates")}
          variant="primary"
          size="large"
          style={styles.mainButton}
          icon={
            <Ionicons name="add-circle-outline" size={20} color="#fff" style={styles.buttonIcon} />
          }
        />
        <Button
          title="View My Resumes"
          onPress={() => router.push("/(tabs)")}
          variant="outline"
          size="large"
          style={styles.secondaryButton}
          icon={
            <Ionicons name="list-outline" size={20} color="#3498db" style={styles.buttonIcon} />
          }
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  contentContainer: {
    padding: 20,
  },
  header: {
    alignItems: "center",
    marginBottom: 40,
    marginTop: 20,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginHorizontal: 20,
  },
  features: {
    marginBottom: 40,
  },
  featureItem: {
    flexDirection: "row",
    marginBottom: 20,
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  featureIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#e6f2fa",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
  actions: {
    alignItems: "center",
  },
  mainButton: {
    width: "100%",
    marginBottom: 12,
  },
  secondaryButton: {
    width: "100%",
  },
  buttonIcon: {
    marginRight: 8,
  },
});
