import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import Button from "../components/Button";
import FormSection from "../components/FormSection";
import Input from "../components/Input";
import { borderRadius, colors, shadows, spacing, typography } from "../constants/theme";

export default function ProfessionalUIDemoScreen() {
  const [text, setText] = useState("");
  const [password, setPassword] = useState("");
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons
          name="arrow-back-outline"
          size={24}
          color={colors.text}
          onPress={() => router.back()}
          style={styles.backButton}
        />
        <Text style={styles.title}>Professional UI Components</Text>
      </View>
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Buttons Section */}
        <FormSection 
          title="Buttons" 
          icon="construct-outline"
        >
          <Text style={styles.sectionSubtitle}>Primary Buttons</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Small"
              size="small"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
              elevated
            />
            <Button
              title="Medium"
              size="medium"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
              elevated
            />
            <Button
              title="Large"
              size="large"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
              elevated
            />
          </View>
          
          <Text style={styles.sectionSubtitle}>Button Variants</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Primary"
              variant="primary"
              onPress={() => {}}
              style={styles.button}
              elevated
            />
            <Button
              title="Secondary"
              variant="secondary"
              onPress={() => {}}
              style={styles.button}
              elevated
            />
            <Button
              title="Outline"
              variant="outline"
              onPress={() => {}}
              style={styles.button}
              elevated
            />
          </View>
          <View style={styles.buttonRow}>
            <Button
              title="Text"
              variant="text"
              onPress={() => {}}
              style={styles.button}
            />
            <Button
              title="Disabled"
              variant="primary"
              disabled
              onPress={() => {}}
              style={styles.button}
              elevated
            />
            <Button
              title="Loading"
              variant="primary"
              loading
              onPress={() => {}}
              style={styles.button}
              elevated
            />
          </View>
          
          <Text style={styles.sectionSubtitle}>With Icons</Text>
          <View style={styles.buttonRow}>
            <Button
              title="Add"
              variant="primary"
              icon={<Ionicons name="add-outline" size={18} color="#fff" />}
              onPress={() => {}}
              style={styles.button}
              elevated
            />
            <Button
              title="Edit"
              variant="outline"
              icon={<Ionicons name="create-outline" size={18} color={colors.primary} />}
              onPress={() => {}}
              style={styles.button}
              elevated
            />
            <Button
              title="Delete"
              variant="secondary"
              icon={<Ionicons name="trash-outline" size={18} color="#fff" />}
              onPress={() => {}}
              style={styles.button}
              elevated
            />
          </View>
          
          <Text style={styles.sectionSubtitle}>Full Width Buttons</Text>
          <Button
            title="Primary Full Width"
            variant="primary"
            fullWidth
            onPress={() => {}}
            style={styles.fullWidthButton}
            elevated
          />
          <Button
            title="Outline Full Width"
            variant="outline"
            fullWidth
            onPress={() => {}}
            style={styles.fullWidthButton}
            elevated
          />
        </FormSection>
        
        {/* Inputs Section */}
        <FormSection 
          title="Inputs" 
          icon="create-outline"
        >
          <Text style={styles.sectionSubtitle}>Variants</Text>
          
          <Input
            label="Outlined Input"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            variant="outlined"
          />
          
          <Input
            label="Filled Input"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            variant="filled"
          />
          
          <Input
            label="Underlined Input"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            variant="underlined"
          />
          
          <Text style={styles.sectionSubtitle}>With Icons</Text>
          
          <Input
            label="Left Icon"
            placeholder="Search..."
            value={text}
            onChangeText={setText}
            leftIcon="search-outline"
          />
          
          <Input
            label="Right Icon"
            placeholder="Enter email"
            value={text}
            onChangeText={setText}
            rightIcon="mail-outline"
          />
          
          <Input
            label="Password Input"
            placeholder="Enter password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          <Input
            label="With Helper Text"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            helperText="This is a helper text"
          />
          
          <Input
            label="Error State"
            placeholder="Enter text"
            value={text}
            onChangeText={setText}
            error="This field is required"
          />
        </FormSection>
        
        {/* Form Sections */}
        <FormSection 
          title="Form Sections" 
          icon="list-outline"
        >
          <Text style={styles.sectionSubtitle}>Basic Section</Text>
          
          <FormSection
            title="Nested Section"
            icon="document-outline"
          >
            <Text style={styles.demoText}>This is a nested section</Text>
          </FormSection>
          
          <Text style={styles.sectionSubtitle}>Collapsible Section</Text>
          
          <FormSection
            title="Click to Toggle"
            icon="chevron-down-outline"
            collapsible
            initiallyCollapsed
          >
            <Text style={styles.demoText}>This section can be collapsed</Text>
            <Text style={styles.demoText}>Click the header to toggle</Text>
          </FormSection>
        </FormSection>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.md,
    backgroundColor: colors.card,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    ...shadows.sm,
  },
  backButton: {
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: "700",
    color: colors.text,
    letterSpacing: 0.3,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: spacing.lg,
    paddingBottom: spacing.xxl * 2,
  },
  sectionSubtitle: {
    fontSize: typography.fontSize.md,
    fontWeight: "600",
    color: colors.primary,
    marginBottom: spacing.sm,
    marginTop: spacing.sm,
    letterSpacing: 0.3,
  },
  buttonRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: spacing.md,
  },
  button: {
    margin: spacing.xs,
  },
  fullWidthButton: {
    marginVertical: spacing.sm,
  },
  demoText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginBottom: spacing.sm,
    lineHeight: typography.lineHeight.md,
  },
});
