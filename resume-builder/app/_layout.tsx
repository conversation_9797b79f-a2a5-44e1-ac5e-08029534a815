import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useColorScheme } from "react-native";

export default function RootLayout() {
  const colorScheme = useColorScheme();

  return (
    <>
      <StatusBar style={colorScheme === "dark" ? "light" : "dark"} />
      <Stack
        screenOptions={{
          headerStyle: {
            backgroundColor: "#fff",
          },
          headerTintColor: "#333",
          headerTitleStyle: {
            fontWeight: "bold",
          },
          headerShadowVisible: false,
        }}>
        <Stack.Screen name="index" options={{ title: "Resume Builder" }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="resume/[id]" options={{ title: "Edit Resume" }} />
        <Stack.Screen name="resume/[id]/preview" options={{ title: "Preview Resume" }} />
        <Stack.Screen name="templates" options={{ title: "Choose Template" }} />
        <Stack.Screen name="history" options={{ title: "Resume History" }} />
        <Stack.Screen name="pdf" options={{ title: "Export PDF" }} />
      </Stack>
    </>
  );
}
