import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import Button from "../components/Button";
import { colors, spacing, typography } from "../constants/theme";

export default function ButtonDemoScreen() {
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.header}>
        <Ionicons
          name="arrow-back"
          size={24}
          color={colors.text}
          onPress={() => router.back()}
          style={styles.backButton}
        />
        <Text style={styles.title}>Button Showcase</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Primary Buttons</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Small"
            size="small"
            variant="primary"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Medium"
            size="medium"
            variant="primary"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Large"
            size="large"
            variant="primary"
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Secondary Buttons</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Small"
            size="small"
            variant="secondary"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Medium"
            size="medium"
            variant="secondary"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Large"
            size="large"
            variant="secondary"
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Outline Buttons</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Small"
            size="small"
            variant="outline"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Medium"
            size="medium"
            variant="outline"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Large"
            size="large"
            variant="outline"
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Success Buttons</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Small"
            size="small"
            variant="success"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Medium"
            size="medium"
            variant="success"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Large"
            size="large"
            variant="success"
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Danger Buttons</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Small"
            size="small"
            variant="danger"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Medium"
            size="medium"
            variant="danger"
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Large"
            size="large"
            variant="danger"
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Rounded Buttons</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Primary"
            size="medium"
            variant="primary"
            rounded
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Success"
            size="medium"
            variant="success"
            rounded
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Danger"
            size="medium"
            variant="danger"
            rounded
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Buttons with Icons</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Add"
            size="medium"
            variant="primary"
            icon={<Ionicons name="add" size={18} color="#fff" />}
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Save"
            size="medium"
            variant="success"
            icon={<Ionicons name="save" size={18} color="#fff" />}
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Delete"
            size="medium"
            variant="danger"
            icon={<Ionicons name="trash" size={18} color="#fff" />}
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Loading State</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Loading"
            size="medium"
            variant="primary"
            loading
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Loading"
            size="medium"
            variant="outline"
            loading
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Loading"
            size="medium"
            variant="secondary"
            loading
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Disabled State</Text>
        <View style={styles.buttonRow}>
          <Button
            title="Disabled"
            size="medium"
            variant="primary"
            disabled
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Disabled"
            size="medium"
            variant="outline"
            disabled
            onPress={() => {}}
            style={styles.button}
          />
          <Button
            title="Disabled"
            size="medium"
            variant="secondary"
            disabled
            onPress={() => {}}
            style={styles.button}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Full Width Buttons</Text>
        <Button
          title="Primary Full Width"
          size="large"
          variant="primary"
          fullWidth
          onPress={() => {}}
          style={styles.fullWidthButton}
        />
        <Button
          title="Success Full Width"
          size="large"
          variant="success"
          fullWidth
          onPress={() => {}}
          style={styles.fullWidthButton}
        />
        <Button
          title="Outline Full Width"
          size="large"
          variant="outline"
          fullWidth
          onPress={() => {}}
          style={styles.fullWidthButton}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    padding: spacing.lg,
    paddingBottom: spacing.xxl * 2,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xl,
  },
  backButton: {
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontWeight: "bold",
    color: colors.text,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: "600",
    color: colors.text,
    marginBottom: spacing.md,
  },
  buttonRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -spacing.xs,
  },
  button: {
    margin: spacing.xs,
  },
  fullWidthButton: {
    marginBottom: spacing.md,
  },
});
