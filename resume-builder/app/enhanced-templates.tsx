import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect } from "react";
import { ActivityIndicator, StyleSheet, View } from "react-native";

// This file is now just a redirect to the templates tab

export default function EnhancedTemplatesScreen() {
  const params = useLocalSearchParams();

  useEffect(() => {
    // Navigate to the templates tab
    router.push({
      pathname: "/(tabs)/templates",
      params: params,
    });
  }, []);

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#3498db" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
  },
});
