import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface BasicTemplatePreviewProps {
  templateId: string;
}

const BasicTemplatePreview: React.FC<BasicTemplatePreviewProps> = ({ templateId }) => {
  // Get template-specific styles
  const getTemplateStyle = () => {
    switch (templateId) {
      case 'modern':
        return {
          container: { backgroundColor: '#ffffff' },
          header: { backgroundColor: '#2c3e50', padding: 10 },
          headerText: { color: '#ffffff', fontWeight: 'bold', fontSize: 16, textAlign: 'center' },
          body: { padding: 10 },
          bodyText: { color: '#333333', fontSize: 12 },
          accent: { backgroundColor: '#3498db', height: 5, marginVertical: 10 }
        };
      case 'professional':
        return {
          container: { backgroundColor: '#ffffff' },
          header: { backgroundColor: '#ffffff', padding: 10, borderBottomWidth: 2, borderBottomColor: '#000000' },
          headerText: { color: '#000000', fontWeight: 'bold', fontSize: 16, textAlign: 'center' },
          body: { padding: 10 },
          bodyText: { color: '#333333', fontSize: 12 },
          accent: { backgroundColor: '#000000', height: 1, marginVertical: 10 }
        };
      case 'creative':
        return {
          container: { backgroundColor: '#fffaf0' },
          header: { backgroundColor: '#ff9a9e', padding: 10, borderRadius: 5 },
          headerText: { color: '#ffffff', fontWeight: 'bold', fontSize: 16, textAlign: 'center' },
          body: { padding: 10 },
          bodyText: { color: '#333333', fontSize: 12 },
          accent: { backgroundColor: '#ff9a9e', height: 3, marginVertical: 10, borderRadius: 1.5 }
        };
      case 'minimal':
        return {
          container: { backgroundColor: '#ffffff' },
          header: { backgroundColor: '#ffffff', padding: 10 },
          headerText: { color: '#333333', fontWeight: 'bold', fontSize: 16, textAlign: 'center' },
          body: { padding: 10 },
          bodyText: { color: '#333333', fontSize: 12 },
          accent: { backgroundColor: '#eeeeee', height: 1, marginVertical: 10 }
        };
      case 'executive':
        return {
          container: { backgroundColor: '#f9f9f9' },
          header: { backgroundColor: '#f9f9f9', padding: 10, borderBottomWidth: 1, borderBottomColor: '#1a3a5f' },
          headerText: { color: '#1a3a5f', fontWeight: 'bold', fontSize: 16, textAlign: 'center' },
          body: { padding: 10 },
          bodyText: { color: '#333333', fontSize: 12 },
          accent: { backgroundColor: '#1a3a5f', height: 2, marginVertical: 10 }
        };
      default:
        return {
          container: { backgroundColor: '#ffffff' },
          header: { backgroundColor: '#333333', padding: 10 },
          headerText: { color: '#ffffff', fontWeight: 'bold', fontSize: 16, textAlign: 'center' },
          body: { padding: 10 },
          bodyText: { color: '#333333', fontSize: 12 },
          accent: { backgroundColor: '#333333', height: 2, marginVertical: 10 }
        };
    }
  };

  const templateStyle = getTemplateStyle();

  return (
    <View style={[styles.container, templateStyle.container]}>
      <View style={[styles.header, templateStyle.header]}>
        <Text style={[styles.headerText, templateStyle.headerText]}>John Doe</Text>
        <Text style={[styles.subHeaderText, { color: templateStyle.headerText.color }]}>Software Engineer</Text>
      </View>
      
      <View style={templateStyle.body}>
        <Text style={[styles.sectionTitle, { color: templateStyle.headerText.color }]}>SUMMARY</Text>
        <View style={templateStyle.accent} />
        <Text style={[styles.bodyText, templateStyle.bodyText]}>
          Experienced software engineer with expertise in web development and mobile applications.
        </Text>
        
        <Text style={[styles.sectionTitle, { color: templateStyle.headerText.color, marginTop: 15 }]}>EXPERIENCE</Text>
        <View style={templateStyle.accent} />
        <Text style={[styles.companyText, { color: templateStyle.headerText.color }]}>Tech Innovations Inc.</Text>
        <View style={styles.positionRow}>
          <Text style={[styles.positionText, templateStyle.bodyText]}>Senior Software Engineer</Text>
          <Text style={styles.dateText}>2020-Present</Text>
        </View>
        <Text style={[styles.bodyText, templateStyle.bodyText]}>
          Lead developer for web and mobile applications, managing a team of 5 engineers.
        </Text>
        
        <Text style={[styles.sectionTitle, { color: templateStyle.headerText.color, marginTop: 15 }]}>EDUCATION</Text>
        <View style={templateStyle.accent} />
        <Text style={[styles.companyText, { color: templateStyle.headerText.color }]}>University of California</Text>
        <View style={styles.positionRow}>
          <Text style={[styles.positionText, templateStyle.bodyText]}>B.S. Computer Science</Text>
          <Text style={styles.dateText}>2015-2019</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 4,
    overflow: 'hidden',
  },
  header: {
    padding: 10,
    alignItems: 'center',
  },
  headerText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subHeaderText: {
    fontSize: 12,
    marginBottom: 4,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  bodyText: {
    fontSize: 12,
    lineHeight: 16,
  },
  companyText: {
    fontSize: 13,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  positionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  positionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  dateText: {
    fontSize: 10,
    color: '#666666',
  },
});

export default BasicTemplatePreview;
