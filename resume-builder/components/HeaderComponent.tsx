import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { Platform, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from "react-native";
import { borderRadius, colors, spacing, typography } from "../constants/theme";

interface ActionButton {
  icon: string;
  label: string;
  onPress: () => void;
  color?: string;
  backgroundColor?: string;
}

interface HeaderComponentProps {
  title: string;
  subtitle?: string;
  actionButton?: ActionButton;
  containerStyle?: ViewStyle;
  showBorder?: boolean;
}

/**
 * A reusable header component for consistent headers across the app
 */
const HeaderComponent: React.FC<HeaderComponentProps> = ({
  title,
  subtitle,
  actionButton,
  containerStyle,
  showBorder = true,
}) => {
  return (
    <View style={[styles.container, showBorder && styles.borderBottom, containerStyle]}>
      <View style={{ flex: 1 }}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      {actionButton && (
        <View>
          <TouchableOpacity
            onPress={actionButton.onPress}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            style={[
              styles.actionButton,
              {
                backgroundColor: actionButton.backgroundColor || `${colors.primary}15`,
              },
            ]}>
            <Ionicons
              name={actionButton.icon as any}
              size={16}
              color={actionButton.color || colors.primary}
            />
            <Text
              style={[styles.actionButtonText, { color: actionButton.color || colors.primary }]}>
              {actionButton.label}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    // justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: Platform.OS === "ios" ? spacing.xl : spacing.xxl, // Account for status bar
    paddingBottom: spacing.md,
    backgroundColor: colors.background,
    zIndex: 10,
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: "700",
    color: colors.text,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.round,
  },
  actionButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: "600",
    marginLeft: spacing.xs,
  },
});

export default HeaderComponent;
