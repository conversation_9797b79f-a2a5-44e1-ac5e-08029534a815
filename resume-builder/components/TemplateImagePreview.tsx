import React from 'react';
import { Image, StyleSheet, View, Dimensions } from 'react-native';

interface TemplateImagePreviewProps {
  templateId: string;
}

const TemplateImagePreview: React.FC<TemplateImagePreviewProps> = ({ templateId }) => {
  // Function to get the appropriate image source based on template ID
  const getImageSource = () => {
    switch (templateId) {
      case 'modern':
        return require('../assets/previews/modern-preview.png');
      case 'professional':
        return require('../assets/previews/professional-preview.png');
      case 'creative':
        return require('../assets/previews/creative-preview.png');
      case 'minimal':
        return require('../assets/previews/minimal-preview.png');
      case 'executive':
        return require('../assets/previews/executive-preview.png');
      default:
        return require('../assets/previews/modern-preview.png');
    }
  };

  return (
    <View style={styles.container}>
      <Image source={getImageSource()} style={styles.image} resizeMode="contain" />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
});

export default TemplateImagePreview;
