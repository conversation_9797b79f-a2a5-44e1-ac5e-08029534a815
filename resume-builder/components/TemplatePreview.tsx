import React from "react";
import { StyleSheet, Text, View } from "react-native";

interface TemplatePreviewProps {
  templateId: string;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({ templateId }) => {
  // Function to render a simplified preview based on template ID
  const renderTemplatePreview = () => {
    switch (templateId) {
      case "modern":
        return <ModernPreview />;
      case "professional":
        return <ProfessionalPreview />;
      case "creative":
        return <CreativePreview />;
      case "minimal":
        return <MinimalPreview />;
      case "executive":
        return <ExecutivePreview />;
      default:
        return (
          <View style={styles.fallbackContainer}>
            <Text style={styles.fallbackText}>Preview not available</Text>
          </View>
        );
    }
  };

  return <View style={styles.container}>{renderTemplatePreview()}</View>;
};

// Simple preview components for each template
const ModernPreview = () => (
  <View style={styles.previewContainer}>
    <View style={styles.modernLayout}>
      <View style={styles.modernSidebar}>
        <View style={styles.nameContainer}>
          <Text style={styles.modernName}><PERSON></Text>
          <Text style={styles.modernTitle}>Software Engineer</Text>
        </View>
        <View style={styles.contactContainer}>
          <Text style={styles.modernContactItem}><EMAIL></Text>
          <Text style={styles.modernContactItem}>(*************</Text>
          <Text style={styles.modernContactItem}>San Francisco, CA</Text>
        </View>
        <View style={styles.skillsContainer}>
          <Text style={styles.modernSectionTitle}>SKILLS</Text>
          <View style={styles.skillsRow}>
            <View style={styles.modernSkillTag}>
              <Text style={styles.modernSkillText}>JavaScript</Text>
            </View>
            <View style={styles.modernSkillTag}>
              <Text style={styles.modernSkillText}>React</Text>
            </View>
            <View style={styles.modernSkillTag}>
              <Text style={styles.modernSkillText}>Node.js</Text>
            </View>
          </View>
        </View>
      </View>
      <View style={styles.modernMain}>
        <View style={styles.section}>
          <Text style={styles.modernSectionTitle}>SUMMARY</Text>
          <Text style={styles.paragraph}>
            Experienced software engineer with expertise in web development...
          </Text>
        </View>
        <View style={styles.section}>
          <Text style={styles.modernSectionTitle}>EXPERIENCE</Text>
          <View style={styles.experienceItem}>
            <View style={styles.itemHeader}>
              <Text style={styles.companyName}>Tech Innovations Inc.</Text>
              <Text style={styles.date}>2020 - Present</Text>
            </View>
            <Text style={styles.position}>Senior Software Engineer</Text>
            <Text style={styles.paragraph}>Lead developer for web and mobile applications...</Text>
          </View>
        </View>
      </View>
    </View>
  </View>
);

const ProfessionalPreview = () => (
  <View style={styles.previewContainer}>
    <View style={styles.professionalLayout}>
      <View style={styles.professionalHeader}>
        <Text style={styles.professionalName}>ALEX JOHNSON</Text>
        <Text style={styles.professionalTitle}>Software Engineer</Text>
        <View style={styles.contactRow}>
          <Text style={styles.professionalContactItem}><EMAIL> | (*************</Text>
        </View>
      </View>
      <View style={styles.section}>
        <Text style={styles.professionalSectionTitle}>SUMMARY</Text>
        <Text style={styles.paragraph}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.section}>
        <Text style={styles.professionalSectionTitle}>EXPERIENCE</Text>
        <View style={styles.experienceItem}>
          <View style={styles.itemHeader}>
            <Text style={styles.professionalCompanyName}>Tech Innovations Inc.</Text>
            <Text style={styles.professionalDate}>2020 - Present</Text>
          </View>
          <Text style={styles.professionalPosition}>Senior Software Engineer</Text>
          <Text style={styles.paragraph}>Lead developer for web and mobile applications...</Text>
        </View>
      </View>
    </View>
  </View>
);

const CreativePreview = () => (
  <View style={styles.previewContainer}>
    <View style={styles.creativeLayout}>
      <View style={styles.creativeHeader}>
        <Text style={styles.creativeName}>Alex Johnson</Text>
        <Text style={styles.creativeTitle}>Software Engineer</Text>
      </View>
      <View style={styles.creativeContact}>
        <Text style={styles.creativeContactItem}><EMAIL></Text>
        <Text style={styles.creativeContactItem}>(*************</Text>
      </View>
      <View style={styles.section}>
        <Text style={styles.creativeSectionTitle}>SUMMARY</Text>
        <Text style={styles.paragraph}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.section}>
        <Text style={styles.creativeSectionTitle}>EXPERIENCE</Text>
        <View style={styles.experienceItem}>
          <View style={styles.itemHeader}>
            <Text style={styles.creativeCompanyName}>Tech Innovations Inc.</Text>
            <Text style={styles.creativeDate}>2020 - Present</Text>
          </View>
          <Text style={styles.creativePosition}>Senior Software Engineer</Text>
          <Text style={styles.paragraph}>Lead developer for web and mobile applications...</Text>
        </View>
      </View>
    </View>
  </View>
);

const MinimalPreview = () => (
  <View style={styles.previewContainer}>
    <View style={styles.minimalLayout}>
      <View style={styles.minimalHeader}>
        <Text style={styles.minimalName}>Alex Johnson</Text>
        <Text style={styles.minimalTitle}>Software Engineer</Text>
        <View style={styles.contactRow}>
          <Text style={styles.minimalContactItem}><EMAIL></Text>
          <Text style={styles.minimalContactItem}>(*************</Text>
          <Text style={styles.minimalContactItem}>San Francisco, CA</Text>
        </View>
      </View>
      <View style={styles.section}>
        <Text style={styles.minimalSectionTitle}>SUMMARY</Text>
        <Text style={styles.paragraph}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.section}>
        <Text style={styles.minimalSectionTitle}>EXPERIENCE</Text>
        <View style={styles.experienceItem}>
          <View style={styles.itemHeader}>
            <Text style={styles.minimalCompanyName}>Tech Innovations Inc.</Text>
            <Text style={styles.minimalDate}>2020 - Present</Text>
          </View>
          <Text style={styles.minimalPosition}>Senior Software Engineer</Text>
          <Text style={styles.paragraph}>Lead developer for web and mobile applications...</Text>
        </View>
      </View>
    </View>
  </View>
);

const ExecutivePreview = () => (
  <View style={styles.previewContainer}>
    <View style={styles.executiveLayout}>
      <View style={styles.executiveHeader}>
        <Text style={styles.executiveName}>Alex Johnson</Text>
        <Text style={styles.executiveTitle}>Software Engineer</Text>
      </View>
      <View style={styles.contactRow}>
        <Text style={styles.executiveContactItem}><EMAIL> | (*************</Text>
      </View>
      <View style={styles.divider} />
      <View style={styles.section}>
        <Text style={styles.executiveSectionTitle}>PROFESSIONAL SUMMARY</Text>
        <Text style={styles.executiveParagraph}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.section}>
        <Text style={styles.executiveSectionTitle}>PROFESSIONAL EXPERIENCE</Text>
        <View style={styles.experienceItem}>
          <View style={styles.itemHeader}>
            <Text style={styles.executiveCompanyName}>Tech Innovations Inc.</Text>
            <Text style={styles.executiveDate}>2020 - Present</Text>
          </View>
          <Text style={styles.executivePosition}>Senior Software Engineer</Text>
          <Text style={styles.executiveParagraph}>
            Lead developer for web and mobile applications...
          </Text>
        </View>
      </View>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: "hidden",
    borderRadius: 4,
  },
  fallbackContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  fallbackText: {
    fontSize: 14,
    color: "#666",
  },
  previewContainer: {
    flex: 1,
    padding: 4,
  },
  // Modern template styles
  modernLayout: {
    flex: 1,
    flexDirection: "row",
  },
  modernSidebar: {
    width: "30%",
    backgroundColor: "#2c3e50",
    padding: 4,
  },
  modernMain: {
    width: "70%",
    padding: 4,
    backgroundColor: "#fff",
  },
  modernName: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 2,
  },
  modernTitle: {
    fontSize: 10,
    color: "#ecf0f1",
    marginBottom: 4,
  },
  modernContactItem: {
    fontSize: 8,
    color: "#fff",
    marginBottom: 2,
  },
  modernSectionTitle: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#ecf0f1",
    marginBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: "#3498db",
  },
  modernSkillTag: {
    backgroundColor: "#3498db",
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 2,
    marginRight: 4,
    marginBottom: 4,
  },
  modernSkillText: {
    color: "#fff",
    fontSize: 8,
  },
  // Professional template styles
  professionalLayout: {
    flex: 1,
    backgroundColor: "#fff",
    padding: 4,
  },
  professionalHeader: {
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    paddingBottom: 4,
    marginBottom: 4,
    alignItems: "center",
  },
  professionalName: {
    fontSize: 12,
    fontWeight: "bold",
    textTransform: "uppercase",
    marginBottom: 2,
  },
  professionalTitle: {
    fontSize: 10,
    color: "#666",
    marginBottom: 2,
  },
  professionalContactItem: {
    fontSize: 8,
    marginBottom: 2,
  },
  professionalSectionTitle: {
    fontSize: 10,
    fontWeight: "bold",
    textTransform: "uppercase",
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    marginBottom: 4,
  },
  professionalCompanyName: {
    fontSize: 10,
    fontWeight: "bold",
  },
  professionalDate: {
    fontSize: 8,
    fontStyle: "italic",
    color: "#666",
  },
  professionalPosition: {
    fontSize: 9,
    fontWeight: "bold",
    marginBottom: 2,
  },
  // Creative template styles
  creativeLayout: {
    flex: 1,
    backgroundColor: "#fffaf0",
    padding: 4,
  },
  creativeHeader: {
    backgroundColor: "#ff9a9e",
    borderRadius: 2,
    padding: 4,
    marginBottom: 4,
    alignItems: "center",
  },
  creativeName: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 2,
  },
  creativeTitle: {
    fontSize: 10,
    color: "rgba(255, 255, 255, 0.9)",
  },
  creativeContact: {
    backgroundColor: "#fff",
    borderRadius: 2,
    padding: 4,
    marginBottom: 4,
    alignItems: "center",
  },
  creativeContactItem: {
    fontSize: 8,
    marginBottom: 2,
  },
  creativeSectionTitle: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#ff9a9e",
    marginBottom: 4,
  },
  creativeCompanyName: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#ff9a9e",
  },
  creativeDate: {
    fontSize: 8,
    color: "#666",
  },
  creativePosition: {
    fontSize: 9,
    fontWeight: "bold",
    marginBottom: 2,
  },
  // Minimal template styles
  minimalLayout: {
    flex: 1,
    backgroundColor: "#fff",
    padding: 4,
  },
  minimalHeader: {
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    paddingBottom: 4,
    marginBottom: 4,
  },
  minimalName: {
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 2,
  },
  minimalTitle: {
    fontSize: 10,
    color: "#666",
    marginBottom: 2,
  },
  minimalContactItem: {
    fontSize: 8,
    color: "#666",
    marginRight: 4,
  },
  minimalSectionTitle: {
    fontSize: 10,
    fontWeight: "bold",
    textTransform: "uppercase",
    marginBottom: 4,
  },
  minimalCompanyName: {
    fontSize: 10,
    fontWeight: "bold",
  },
  minimalDate: {
    fontSize: 8,
    color: "#999",
  },
  minimalPosition: {
    fontSize: 9,
    fontWeight: "bold",
    marginBottom: 2,
  },
  // Executive template styles
  executiveLayout: {
    flex: 1,
    backgroundColor: "#f9f9f9",
    padding: 4,
  },
  executiveHeader: {
    borderBottomWidth: 1,
    borderBottomColor: "#1a3a5f",
    paddingBottom: 4,
    marginBottom: 4,
    alignItems: "center",
  },
  executiveName: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#1a3a5f",
    marginBottom: 2,
  },
  executiveTitle: {
    fontSize: 10,
    color: "#666",
    fontStyle: "italic",
  },
  executiveContactItem: {
    fontSize: 8,
    textAlign: "center",
  },
  executiveSectionTitle: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#1a3a5f",
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
    marginBottom: 4,
  },
  executiveCompanyName: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#1a3a5f",
  },
  executiveDate: {
    fontSize: 8,
    fontStyle: "italic",
    color: "#666",
  },
  executivePosition: {
    fontSize: 9,
    fontWeight: "bold",
    marginBottom: 2,
  },
  executiveParagraph: {
    fontSize: 8,
    lineHeight: 12,
    color: "#555",
  },
  // Common components
  nameContainer: {
    marginBottom: 4,
  },
  contactContainer: {
    marginBottom: 4,
  },
  contactRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    marginBottom: 2,
  },
  skillsContainer: {
    marginBottom: 4,
  },
  skillsRow: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  section: {
    marginBottom: 4,
  },
  paragraph: {
    fontSize: 8,
    marginBottom: 2,
  },
  experienceItem: {
    marginBottom: 4,
  },
  itemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 2,
  },
  companyName: {
    fontSize: 10,
    fontWeight: "bold",
  },
  date: {
    fontSize: 8,
    color: "#999",
  },
  position: {
    fontSize: 9,
    fontWeight: "bold",
    marginBottom: 2,
  },
  divider: {
    height: 1,
    backgroundColor: "#ddd",
    marginVertical: 4,
  },
});

export default TemplatePreview;
