import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  Alert,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
} from "react-native";
import { ResumeData } from "../types";

// Define the section data structure
interface SectionItem {
  id: string;
  title: string;
  icon: string;
}

// Define the available sections
const AVAILABLE_SECTIONS: SectionItem[] = [
  {
    id: "education",
    title: "Education",
    icon: "school-outline",
  },
  {
    id: "experience",
    title: "Experience",
    icon: "briefcase-outline",
  },
  {
    id: "skills",
    title: "Skills",
    icon: "construct-outline",
  },
  {
    id: "projects",
    title: "Projects",
    icon: "code-outline",
  },
  {
    id: "certifications",
    title: "Certifications",
    icon: "ribbon-outline",
  },
];

interface SectionOrderManagerProps {
  resume: ResumeData;
  onOrderChange: (newOrder: string[]) => void;
  onSave: () => void;
}

const SectionOrderManager: React.FC<SectionOrderManagerProps> = ({
  resume,
  onOrderChange,
  onSave,
}) => {
  // Initialize sections based on resume's sectionOrder or default order
  const [sections, setSections] = useState<SectionItem[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [draggedItem, setDraggedItem] = useState<SectionItem | null>(null);

  // Initialize sections based on resume's sectionOrder
  useEffect(() => {
    if (resume.sectionOrder) {
      // Map the section order to the full section objects
      const orderedSections = resume.sectionOrder
        .map(id => AVAILABLE_SECTIONS.find(section => section.id === id))
        .filter((section): section is SectionItem => section !== undefined);
      
      setSections(orderedSections);
    } else {
      // Use default order if none is specified
      setSections([...AVAILABLE_SECTIONS]);
    }
  }, [resume.sectionOrder]);

  // Move an item up in the list
  const moveItemUp = (index: number) => {
    if (index <= 0) return;
    
    const newSections = [...sections];
    const item = newSections[index];
    newSections[index] = newSections[index - 1];
    newSections[index - 1] = item;
    
    setSections(newSections);
    updateOrder(newSections);
  };

  // Move an item down in the list
  const moveItemDown = (index: number) => {
    if (index >= sections.length - 1) return;
    
    const newSections = [...sections];
    const item = newSections[index];
    newSections[index] = newSections[index + 1];
    newSections[index + 1] = item;
    
    setSections(newSections);
    updateOrder(newSections);
  };

  // Update the order and mark as changed
  const updateOrder = (newSections: SectionItem[]) => {
    const newOrder = newSections.map(section => section.id);
    onOrderChange(newOrder);
    setHasChanges(true);
  };

  // Handle save button press
  const handleSave = () => {
    if (hasChanges) {
      onSave();
      Alert.alert("Success", "Section order saved successfully");
      setHasChanges(false);
    } else {
      Alert.alert("No Changes", "No changes to save");
    }
  };

  // Render a section item
  const renderItem = ({ item, index }: { item: SectionItem; index: number }) => {
    return (
      <View style={styles.sectionItem}>
        <View style={styles.sectionContent}>
          <Ionicons
            name={item.icon as any}
            size={24}
            color="#3498db"
            style={styles.sectionIcon}
          />
          <Text style={styles.sectionTitle}>{item.title}</Text>
        </View>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, index === 0 && styles.disabledButton]}
            onPress={() => moveItemUp(index)}
            disabled={index === 0}
          >
            <Ionicons name="chevron-up" size={24} color={index === 0 ? "#ccc" : "#666"} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, index === sections.length - 1 && styles.disabledButton]}
            onPress={() => moveItemDown(index)}
            disabled={index === sections.length - 1}
          >
            <Ionicons name="chevron-down" size={24} color={index === sections.length - 1 ? "#ccc" : "#666"} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Arrange Resume Sections</Text>
        <Text style={styles.subtitle}>
          Use the up and down arrows to change the order of sections on your resume
        </Text>
      </View>
      
      <FlatList
        data={sections}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
      />
      
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.saveButton, !hasChanges && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={!hasChanges}
        >
          <Ionicons
            name="save-outline"
            size={20}
            color={hasChanges ? "#fff" : "#999"}
            style={styles.saveIcon}
          />
          <Text style={[styles.saveButtonText, !hasChanges && styles.saveButtonTextDisabled]}>
            Save Section Order
          </Text>
        </TouchableOpacity>
        
        <Text style={styles.note}>
          Note: Personal Information section will always appear first
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
  listContent: {
    paddingBottom: 16,
  },
  sectionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: "#3498db",
  },
  sectionContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  sectionIcon: {
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  actionButtons: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  disabledButton: {
    opacity: 0.5,
  },
  footer: {
    marginTop: 24,
    alignItems: "center",
  },
  saveButton: {
    backgroundColor: "#3498db",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginBottom: 16,
  },
  saveButtonDisabled: {
    backgroundColor: "#e0e0e0",
  },
  saveIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  saveButtonTextDisabled: {
    color: "#999",
  },
  note: {
    fontSize: 12,
    color: "#666",
    fontStyle: "italic",
  },
});

export default SectionOrderManager;
