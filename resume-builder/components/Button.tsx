import React, { useEffect, useRef } from "react";
import {
  ActivityIndicator,
  Animated,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { borderRadius, colors, shadows, spacing, typography } from "../constants/theme";

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "text";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  fullWidth?: boolean;
  rounded?: boolean;
  elevated?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  disabled = false,
  loading = false,
  style,
  textStyle,
  icon,
  iconPosition = "left",
  fullWidth = false,
  rounded = false,
  elevated = true,
}) => {
  // Animation for press feedback
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Animate opacity when disabled state changes
    Animated.timing(opacityAnim, {
      toValue: disabled ? 0.6 : 1,
      duration: 150,
      useNativeDriver: true,
    }).start();
  }, [disabled]);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      friction: 8,
      tension: 300,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 300,
      useNativeDriver: true,
    }).start();
  };

  // Determine button styles based on variant and size
  const getButtonStyle = () => {
    let buttonStyle: ViewStyle = { ...styles.button };

    // Rounded style
    if (rounded) {
      buttonStyle = { ...buttonStyle, ...styles.roundedButton };
    }

    // Size styles
    switch (size) {
      case "small":
        buttonStyle = { ...buttonStyle, ...styles.smallButton };
        break;
      case "medium":
        buttonStyle = { ...buttonStyle, ...styles.mediumButton };
        break;
      case "large":
        buttonStyle = { ...buttonStyle, ...styles.largeButton };
        break;
    }

    // Full width style
    if (fullWidth) {
      buttonStyle = { ...buttonStyle, ...styles.fullWidthButton };
    }

    // Variant styles
    switch (variant) {
      case "primary":
        buttonStyle = { ...buttonStyle, ...styles.primaryButton };
        break;
      case "secondary":
        buttonStyle = { ...buttonStyle, ...styles.secondaryButton };
        break;
      case "outline":
        buttonStyle = { ...buttonStyle, ...styles.outlineButton };
        break;
      case "text":
        buttonStyle = { ...buttonStyle, ...styles.textButton };
        break;
    }

    // Add shadow if elevated
    if (elevated && variant !== "text" && !disabled) {
      buttonStyle = {
        ...buttonStyle,
        ...(variant === "outline" ? shadows.xs : shadows.sm),
      };
    }

    return buttonStyle;
  };

  // Determine text styles based on variant and size
  const getTextStyle = () => {
    let textStyleObj: TextStyle = { ...styles.buttonText };

    // Variant text styles
    switch (variant) {
      case "primary":
        textStyleObj = { ...textStyleObj, ...styles.primaryButtonText };
        break;
      case "secondary":
        textStyleObj = { ...textStyleObj, ...styles.secondaryButtonText };
        break;
      case "outline":
        textStyleObj = { ...textStyleObj, ...styles.outlineButtonText };
        break;
      case "text":
        textStyleObj = { ...textStyleObj, ...styles.textButtonText };
        break;
    }

    // Size text styles
    switch (size) {
      case "small":
        textStyleObj = { ...textStyleObj, ...styles.smallButtonText };
        break;
      case "medium":
        textStyleObj = { ...textStyleObj, ...styles.mediumButtonText };
        break;
      case "large":
        textStyleObj = { ...textStyleObj, ...styles.largeButtonText };
        break;
    }

    return textStyleObj;
  };

  // Get loading indicator color based on variant
  const getLoadingColor = () => {
    switch (variant) {
      case "primary":
        return "#ffffff";
      case "secondary":
        return "#ffffff";
      case "outline":
        return colors.primary;
      case "text":
        return colors.primary;
      default:
        return "#ffffff";
    }
  };

  // Render button content
  const renderButtonContent = () => {
    return (
      <View style={styles.contentContainer}>
        {loading ? (
          <ActivityIndicator size="small" color={getLoadingColor()} />
        ) : (
          <>
            {icon && iconPosition === "left" && (
              <View style={styles.iconLeftContainer}>{icon}</View>
            )}
            <Text style={[getTextStyle(), textStyle]}>{title}</Text>
            {icon && iconPosition === "right" && (
              <View style={styles.iconRightContainer}>{icon}</View>
            )}
          </>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={style}>
      <Animated.View
        style={[
          getButtonStyle(),
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}>
        {renderButtonContent()}
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: borderRadius.md,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    overflow: "hidden",
  },
  roundedButton: {
    borderRadius: borderRadius.round,
  },
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    paddingHorizontal: spacing.sm,
  },
  iconLeftContainer: {
    marginRight: spacing.xs,
  },
  iconRightContainer: {
    marginLeft: spacing.xs,
  },
  buttonText: {
    fontWeight: "600",
    textAlign: "center",
    letterSpacing: 0.3,
  },

  // Variant styles
  primaryButton: {
    backgroundColor: colors.primary,
  },
  secondaryButton: {
    backgroundColor: colors.secondary,
  },
  outlineButton: {
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  textButton: {
    backgroundColor: "transparent",
    paddingHorizontal: 0,
  },

  // Text styles
  primaryButtonText: {
    color: "#ffffff",
  },
  secondaryButtonText: {
    color: "#ffffff",
  },
  outlineButtonText: {
    color: colors.primary,
  },
  textButtonText: {
    color: colors.primary,
  },

  // Size styles
  smallButton: {
    paddingVertical: spacing.xs / 1.5,
    paddingHorizontal: spacing.sm,
    minWidth: 0, // Allow buttons to be as small as needed
  },
  smallButtonText: {
    fontSize: typography.fontSize.xs,
  },
  mediumButton: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
  },
  mediumButtonText: {
    fontSize: typography.fontSize.sm,
  },
  largeButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
  largeButtonText: {
    fontSize: typography.fontSize.md,
  },

  // Full width style
  fullWidthButton: {
    width: "100%",
  },
});

export default Button;
