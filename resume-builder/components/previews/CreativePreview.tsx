import React from 'react';
import { View, StyleSheet } from 'react-native';

const CreativePreview = () => {
  return (
    <View style={styles.container}>
      {/* Header with gradient background */}
      <View style={styles.header}>
        <View style={styles.namePlaceholder} />
        <View style={styles.titlePlaceholder} />
      </View>
      
      {/* Contact Info */}
      <View style={styles.contactContainer}>
        <View style={styles.contactItem} />
        <View style={styles.contactItem} />
        <View style={styles.contactItem} />
      </View>
      
      {/* Summary */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.paragraph} />
      </View>
      
      {/* Experience */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.experienceItem}>
          <View style={styles.itemHeader}>
            <View style={styles.companyName} />
            <View style={styles.date} />
          </View>
          <View style={styles.position} />
          <View style={styles.paragraph} />
          <View style={styles.bulletPoints}>
            <View style={styles.bullet} />
            <View style={styles.bullet} />
          </View>
        </View>
      </View>
      
      {/* Education */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.educationItem}>
          <View style={styles.itemHeader}>
            <View style={styles.schoolName} />
            <View style={styles.date} />
          </View>
          <View style={styles.degree} />
          <View style={styles.shortText} />
        </View>
      </View>
      
      {/* Skills */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.skillsRow}>
          <View style={styles.skillTag} />
          <View style={styles.skillTag} />
          <View style={styles.skillTag} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    backgroundColor: '#fffaf0',
    padding: 10,
  },
  header: {
    backgroundColor: '#ff9a9e',
    borderRadius: 5,
    padding: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  namePlaceholder: {
    height: 12,
    backgroundColor: '#fff',
    marginBottom: 5,
    width: '60%',
  },
  titlePlaceholder: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    width: '40%',
  },
  contactContainer: {
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  contactItem: {
    height: 6,
    backgroundColor: '#ddd',
    marginBottom: 5,
    width: '80%',
  },
  section: {
    marginBottom: 10,
  },
  sectionTitle: {
    height: 8,
    backgroundColor: '#ff9a9e',
    marginBottom: 8,
    width: '30%',
  },
  paragraph: {
    height: 6,
    backgroundColor: '#ddd',
    marginBottom: 5,
    width: '100%',
  },
  experienceItem: {
    marginBottom: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  companyName: {
    height: 8,
    backgroundColor: '#ff9a9e',
    width: '50%',
  },
  date: {
    height: 6,
    backgroundColor: '#ddd',
    width: '20%',
  },
  position: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '40%',
  },
  bulletPoints: {
    marginLeft: 10,
  },
  bullet: {
    height: 4,
    backgroundColor: '#ddd',
    marginBottom: 3,
    width: '90%',
  },
  educationItem: {
    marginBottom: 8,
  },
  schoolName: {
    height: 8,
    backgroundColor: '#ff9a9e',
    width: '50%',
  },
  degree: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '60%',
  },
  shortText: {
    height: 6,
    backgroundColor: '#ddd',
    width: '30%',
  },
  skillsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillTag: {
    height: 8,
    backgroundColor: '#ff9a9e',
    marginRight: 5,
    marginBottom: 5,
    width: 30,
    borderRadius: 2,
  },
});

export default CreativePreview;
