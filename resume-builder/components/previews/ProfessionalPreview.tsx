import React from 'react';
import { View, StyleSheet } from 'react-native';

const ProfessionalPreview = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.namePlaceholder} />
        <View style={styles.titlePlaceholder} />
        <View style={styles.contactRow}>
          <View style={styles.contactItem} />
          <View style={styles.contactItem} />
        </View>
      </View>
      
      {/* Summary */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.paragraph} />
      </View>
      
      {/* Experience */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.experienceItem}>
          <View style={styles.itemHeader}>
            <View style={styles.companyName} />
            <View style={styles.date} />
          </View>
          <View style={styles.position} />
          <View style={styles.paragraph} />
          <View style={styles.bulletPoints}>
            <View style={styles.bullet} />
            <View style={styles.bullet} />
          </View>
        </View>
      </View>
      
      {/* Education */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.educationItem}>
          <View style={styles.itemHeader}>
            <View style={styles.schoolName} />
            <View style={styles.date} />
          </View>
          <View style={styles.degree} />
          <View style={styles.shortText} />
        </View>
      </View>
      
      {/* Skills */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.skillsRow}>
          <View style={styles.skillItem} />
          <View style={styles.skillItem} />
          <View style={styles.skillItem} />
          <View style={styles.skillItem} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    backgroundColor: '#fff',
    padding: 10,
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: '#000',
    paddingBottom: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  namePlaceholder: {
    height: 12,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '60%',
  },
  titlePlaceholder: {
    height: 8,
    backgroundColor: '#666',
    marginBottom: 8,
    width: '40%',
  },
  contactRow: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contactItem: {
    height: 6,
    backgroundColor: '#999',
    marginHorizontal: 5,
    width: '30%',
  },
  section: {
    marginBottom: 10,
  },
  sectionTitle: {
    height: 8,
    backgroundColor: '#000',
    marginBottom: 8,
    width: '30%',
  },
  paragraph: {
    height: 6,
    backgroundColor: '#ddd',
    marginBottom: 5,
    width: '100%',
  },
  experienceItem: {
    marginBottom: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  companyName: {
    height: 8,
    backgroundColor: '#333',
    width: '50%',
  },
  date: {
    height: 6,
    backgroundColor: '#999',
    width: '20%',
  },
  position: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '40%',
  },
  bulletPoints: {
    marginLeft: 10,
  },
  bullet: {
    height: 4,
    backgroundColor: '#ddd',
    marginBottom: 3,
    width: '90%',
  },
  educationItem: {
    marginBottom: 8,
  },
  schoolName: {
    height: 8,
    backgroundColor: '#333',
    width: '50%',
  },
  degree: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '60%',
  },
  shortText: {
    height: 6,
    backgroundColor: '#ddd',
    width: '30%',
  },
  skillsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillItem: {
    height: 6,
    backgroundColor: '#999',
    marginRight: 10,
    marginBottom: 5,
    width: '20%',
  },
});

export default ProfessionalPreview;
