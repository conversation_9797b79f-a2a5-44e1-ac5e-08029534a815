import React from 'react';
import { View, StyleSheet } from 'react-native';

const ExecutivePreview = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.namePlaceholder} />
        <View style={styles.titlePlaceholder} />
      </View>
      
      {/* Contact Info */}
      <View style={styles.contactContainer}>
        <View style={styles.contactRow}>
          <View style={styles.contactItem} />
          <View style={styles.contactItem} />
        </View>
        <View style={styles.contactRow}>
          <View style={styles.contactItem} />
          <View style={styles.contactItem} />
        </View>
      </View>
      
      {/* Summary */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.summaryText} />
      </View>
      
      {/* Divider */}
      <View style={styles.divider} />
      
      {/* Experience */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.experienceItem}>
          <View style={styles.itemHeader}>
            <View style={styles.companyName} />
            <View style={styles.date} />
          </View>
          <View style={styles.position} />
          <View style={styles.paragraph} />
          <View style={styles.bulletPoints}>
            <View style={styles.bullet} />
            <View style={styles.bullet} />
          </View>
        </View>
      </View>
      
      {/* Education */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.educationItem}>
          <View style={styles.itemHeader}>
            <View style={styles.schoolName} />
            <View style={styles.date} />
          </View>
          <View style={styles.degree} />
          <View style={styles.shortText} />
        </View>
      </View>
      
      {/* Skills */}
      <View style={styles.section}>
        <View style={styles.sectionTitle} />
        <View style={styles.skillsRow}>
          <View style={styles.skillTag} />
          <View style={styles.skillTag} />
          <View style={styles.skillTag} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    backgroundColor: '#f9f9f9',
    padding: 10,
  },
  header: {
    borderBottomWidth: 2,
    borderBottomColor: '#1a3a5f',
    paddingBottom: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  namePlaceholder: {
    height: 12,
    backgroundColor: '#1a3a5f',
    marginBottom: 5,
    width: '60%',
  },
  titlePlaceholder: {
    height: 8,
    backgroundColor: '#666',
    width: '40%',
    fontStyle: 'italic',
  },
  contactContainer: {
    alignItems: 'center',
    marginBottom: 10,
  },
  contactRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 3,
  },
  contactItem: {
    height: 6,
    backgroundColor: '#999',
    marginHorizontal: 5,
    width: '30%',
  },
  section: {
    marginBottom: 10,
  },
  sectionTitle: {
    height: 8,
    backgroundColor: '#1a3a5f',
    marginBottom: 8,
    width: '40%',
  },
  summaryText: {
    height: 6,
    backgroundColor: '#555',
    marginBottom: 3,
    width: '100%',
  },
  divider: {
    height: 1,
    backgroundColor: '#ddd',
    marginVertical: 8,
  },
  paragraph: {
    height: 6,
    backgroundColor: '#ddd',
    marginBottom: 5,
    width: '100%',
  },
  experienceItem: {
    marginBottom: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  companyName: {
    height: 8,
    backgroundColor: '#1a3a5f',
    width: '50%',
  },
  date: {
    height: 6,
    backgroundColor: '#999',
    width: '20%',
  },
  position: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '40%',
  },
  bulletPoints: {
    marginLeft: 10,
  },
  bullet: {
    height: 4,
    backgroundColor: '#ddd',
    marginBottom: 3,
    width: '90%',
  },
  educationItem: {
    marginBottom: 8,
  },
  schoolName: {
    height: 8,
    backgroundColor: '#1a3a5f',
    width: '50%',
  },
  degree: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '60%',
  },
  shortText: {
    height: 6,
    backgroundColor: '#ddd',
    width: '30%',
  },
  skillsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillTag: {
    height: 8,
    backgroundColor: '#f5f5f5',
    marginRight: 5,
    marginBottom: 5,
    width: 30,
    borderRadius: 2,
    borderWidth: 1,
    borderColor: '#ddd',
  },
});

export default ExecutivePreview;
