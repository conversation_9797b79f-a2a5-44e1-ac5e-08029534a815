import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const ModernPreview = () => {
  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <View style={styles.sidebar}>
        <View style={styles.nameContainer}>
          <View style={styles.namePlaceholder} />
          <View style={styles.titlePlaceholder} />
        </View>
        <View style={styles.contactContainer}>
          <View style={styles.contactItem} />
          <View style={styles.contactItem} />
          <View style={styles.contactItem} />
        </View>
        <View style={styles.skillsContainer}>
          <View style={styles.sectionTitle} />
          <View style={styles.skillsRow}>
            <View style={styles.skillTag} />
            <View style={styles.skillTag} />
            <View style={styles.skillTag} />
          </View>
        </View>
      </View>
      
      {/* Main Content */}
      <View style={styles.mainContent}>
        <View style={styles.section}>
          <View style={styles.sectionTitle} />
          <View style={styles.paragraph} />
        </View>
        
        <View style={styles.section}>
          <View style={styles.sectionTitle} />
          <View style={styles.experienceItem}>
            <View style={styles.itemHeader}>
              <View style={styles.companyName} />
              <View style={styles.date} />
            </View>
            <View style={styles.position} />
            <View style={styles.paragraph} />
            <View style={styles.bulletPoints}>
              <View style={styles.bullet} />
              <View style={styles.bullet} />
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <View style={styles.sectionTitle} />
          <View style={styles.educationItem}>
            <View style={styles.itemHeader}>
              <View style={styles.schoolName} />
              <View style={styles.date} />
            </View>
            <View style={styles.degree} />
            <View style={styles.shortText} />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: '100%',
    width: '100%',
    backgroundColor: '#fff',
  },
  sidebar: {
    width: '30%',
    backgroundColor: '#2c3e50',
    padding: 10,
  },
  mainContent: {
    width: '70%',
    padding: 10,
  },
  nameContainer: {
    marginBottom: 15,
  },
  namePlaceholder: {
    height: 12,
    backgroundColor: '#fff',
    marginBottom: 5,
    width: '80%',
  },
  titlePlaceholder: {
    height: 8,
    backgroundColor: '#ecf0f1',
    width: '60%',
  },
  contactContainer: {
    marginBottom: 15,
  },
  contactItem: {
    height: 6,
    backgroundColor: '#ecf0f1',
    marginBottom: 5,
    width: '90%',
  },
  skillsContainer: {
    marginBottom: 15,
  },
  sectionTitle: {
    height: 8,
    backgroundColor: '#3498db',
    marginBottom: 8,
    width: '70%',
  },
  skillsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillTag: {
    height: 8,
    width: 30,
    backgroundColor: '#3498db',
    marginRight: 5,
    marginBottom: 5,
    borderRadius: 2,
  },
  section: {
    marginBottom: 15,
  },
  paragraph: {
    height: 6,
    backgroundColor: '#ddd',
    marginBottom: 5,
    width: '100%',
  },
  experienceItem: {
    marginBottom: 10,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  companyName: {
    height: 8,
    backgroundColor: '#2980b9',
    width: '50%',
  },
  date: {
    height: 6,
    backgroundColor: '#ddd',
    width: '20%',
  },
  position: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '40%',
  },
  bulletPoints: {
    marginLeft: 10,
  },
  bullet: {
    height: 4,
    backgroundColor: '#ddd',
    marginBottom: 3,
    width: '90%',
  },
  educationItem: {
    marginBottom: 10,
  },
  schoolName: {
    height: 8,
    backgroundColor: '#2980b9',
    width: '50%',
  },
  degree: {
    height: 6,
    backgroundColor: '#333',
    marginBottom: 5,
    width: '60%',
  },
  shortText: {
    height: 6,
    backgroundColor: '#ddd',
    width: '30%',
  },
});

export default ModernPreview;
