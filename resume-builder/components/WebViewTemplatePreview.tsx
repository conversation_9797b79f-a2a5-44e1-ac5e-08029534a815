import React, { useEffect, useState } from "react";
import { ActivityIndicator, StyleSheet, Text, View } from "react-native";
import { WebView } from "react-native-webview";
import { generateTemplatePreview } from "../utils/previewGenerator";

interface WebViewTemplatePreviewProps {
  templateId: string;
}

const WebViewTemplatePreview: React.FC<WebViewTemplatePreviewProps> = ({ templateId }) => {
  const [html, setHtml] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Reset loading state when template changes
    setLoading(true);
    setError(null);

    try {
      console.log(`Generating preview for template: ${templateId}`);
      // Generate the HTML preview
      const previewHtml = generateTemplatePreview(templateId);
      console.log(`Preview HTML generated, length: ${previewHtml.length}`);
      setHtml(previewHtml);
    } catch (err) {
      console.error("Error generating preview:", err);
      setError("Failed to generate preview");
    } finally {
      setLoading(false);
    }
  }, [templateId]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color="#3498db" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  // Simple JavaScript to scale down the content
  const getScaleFactor = () => {
    switch (templateId) {
      case "modern":
        return 0.38;
      case "minimal":
        return 0.42;
      case "creative":
        return 0.36;
      case "professional":
      case "executive":
        return 0.4;
      default:
        return 0.4;
    }
  };

  const injectedJavaScript = `
    (function() {
      document.body.style.margin = '0';
      document.body.style.padding = '0';
      document.documentElement.style.margin = '0';
      document.documentElement.style.padding = '0';

      const container = document.querySelector('.container');
      if (container) {
        container.style.margin = '0 auto';
        container.style.maxWidth = 'none';
        container.style.transform = 'scale(${getScaleFactor()})';
        container.style.transformOrigin = 'top center';
      } else {
        document.body.style.transform = 'scale(${getScaleFactor()})';
        document.body.style.transformOrigin = 'top center';
      }
      true;
    })();
  `;

  return (
    <View style={styles.container}>
      <WebView
        source={{ html }}
        style={styles.webview}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        originWhitelist={["*"]}
        javaScriptEnabled={true}
        injectedJavaScript={injectedJavaScript}
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error("WebView error:", nativeEvent);
          setError("WebView error: " + nativeEvent.description);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
    overflow: "hidden",
    backgroundColor: "#f9f9f9",
  },
  webview: {
    flex: 1,
    backgroundColor: "transparent",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 10,
  },
  errorText: {
    color: "red",
    textAlign: "center",
    fontSize: 12,
  },
});

export default WebViewTemplatePreview;
