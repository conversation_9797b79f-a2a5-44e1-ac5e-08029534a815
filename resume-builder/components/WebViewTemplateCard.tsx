import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Template } from "../types";
import WebViewTemplatePreview from "./WebViewTemplatePreview";

interface TemplateCardProps {
  template: Template;
  selected?: boolean;
  onSelect: (templateId: string) => void;
}

const WebViewTemplateCard: React.FC<TemplateCardProps> = ({ template, selected = false, onSelect }) => {
  return (
    <TouchableOpacity
      style={[styles.card, selected && styles.selectedCard]}
      onPress={() => onSelect(template.id)}
      activeOpacity={0.7}>
      <View style={styles.thumbnailContainer}>
        <View style={styles.thumbnail}>
          <WebViewTemplatePreview templateId={template.id} />
        </View>
        {selected && (
          <View style={styles.selectedOverlay}>
            <View style={styles.checkmark}>
              <Text style={styles.checkmarkText}>✓</Text>
            </View>
          </View>
        )}
      </View>
      <View style={styles.content}>
        <Text style={styles.name}>{template.name}</Text>
        <Text style={styles.description}>{template.description}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: 16,
    marginHorizontal: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 2,
    borderColor: "transparent",
    width: "100%",
  },
  selectedCard: {
    borderColor: "#3498db",
  },
  thumbnailContainer: {
    position: "relative",
    height: 400,
    backgroundColor: "#f9f9f9",
    overflow: "hidden",
    padding: 10,
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#ffffff",
    overflow: "hidden",
    padding: 5,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(52, 152, 219, 0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  checkmark: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#3498db",
    justifyContent: "center",
    alignItems: "center",
  },
  checkmarkText: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
  },
  content: {
    padding: 16,
  },
  name: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: "#666",
  },
});

export default WebViewTemplateCard;
