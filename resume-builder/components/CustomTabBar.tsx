import { Ionicons } from "@expo/vector-icons";
import { BottomTabBarProps } from "@react-navigation/bottom-tabs";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import React, { useEffect, useRef } from "react";
import { Animated, Platform, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { borderRadius, colors, shadows, spacing, typography } from "../constants/theme";

const isIOS = Platform.OS === "ios";

// Custom tab bar icon component with modern design
function TabBarIcon({
  name,
  color,
  focused,
  label,
}: {
  name: string;
  color: string;
  focused: boolean;
  label: string;
}) {
  // Animation values
  const scaleAnim = useRef(new Animated.Value(focused ? 1.2 : 1)).current;
  const opacityAnim = useRef(new Animated.Value(focused ? 1 : 0.7)).current;

  useEffect(() => {
    // Animate when focus changes - keep native drivers consistent
    Animated.spring(scaleAnim, {
      toValue: focused ? 1.2 : 1,
      friction: 5,
      tension: 300,
      useNativeDriver: true,
    }).start();

    Animated.timing(opacityAnim, {
      toValue: focused ? 1 : 0.7,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [focused]);

  return (
    <View style={styles.tabIconContainer}>
      <Animated.View
        style={[
          styles.iconWrapper,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}>
        <Ionicons name={name as any} size={22} color={color} />
      </Animated.View>
      <Text style={[styles.tabLabel, { color, opacity: focused ? 1 : 0.7 }]}>{label}</Text>
      {focused && (
        <View style={styles.indicatorContainer}>
          <View style={[styles.indicator, { backgroundColor: color }]} />
        </View>
      )}
    </View>
  );
}

export default function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const focusedOptions = descriptors[state.routes[state.index].key].options;

  // Check if tab bar should be hidden
  if (focusedOptions.tabBarStyle && (focusedOptions.tabBarStyle as any).display === "none") {
    return null;
  }

  const iconMap: Record<string, string> = {
    index: "document-text",
    templates: "grid",
    history: "time",
    settings: "settings",
  };

  const labelMap: Record<string, string> = {
    index: "Resumes",
    templates: "Templates",
    history: "History",
    settings: "Settings",
  };

  return (
    <View style={styles.container}>
      {isIOS && <BlurView tint="light" intensity={80} style={StyleSheet.absoluteFill} />}
      <LinearGradient
        colors={["rgba(255,255,255,0.9)", "rgba(255,255,255,0.97)"]}
        style={StyleSheet.absoluteFill}
      />
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: "tabPress",
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: "tabLongPress",
              target: route.key,
            });
          };

          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel as string}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tabButton}>
              <TabBarIcon
                name={iconMap[route.name]}
                color={isFocused ? colors.primary : colors.textSecondary}
                focused={isFocused}
                label={labelMap[route.name]}
              />
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    overflow: "hidden",
    ...shadows.lg,
    backgroundColor: isIOS ? "rgba(255, 255, 255, 0.8)" : colors.card,
  },
  tabBar: {
    flexDirection: "row",
    height: "100%",
    paddingBottom: spacing.md,
    paddingTop: spacing.md,
  },
  tabButton: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  tabIconContainer: {
    alignItems: "center",
    justifyContent: "center",
    width: 70,
    height: 50,
  },
  iconWrapper: {
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
    marginBottom: spacing.xs,
    height: 30,
  },
  indicatorContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  indicator: {
    width: 20,
    height: 3,
    borderRadius: borderRadius.xs,
  },
  tabLabel: {
    fontSize: typography.fontSize.xs,
    fontWeight: "600",
    textAlign: "center",
    marginTop: spacing.xs,
  },
});
