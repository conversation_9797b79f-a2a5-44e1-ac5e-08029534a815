import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import { Animated, Platform, StyleSheet, TouchableOpacity, View } from "react-native";
import { colors } from "../constants/theme";

interface FloatingActionButtonProps {
  onPress?: () => void;
  icon?: string;
  color?: string;
  size?: "small" | "medium" | "large";
  gradient?: boolean;
}

export default function FloatingActionButton({
  onPress,
  icon = "add",
  color = colors.primary,
  size = "medium",
  gradient = true,
}: FloatingActionButtonProps) {
  // Use state for non-animated shadow properties instead of Animated values
  const [shadowStyle, setShadowStyle] = useState({
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
  });

  // Only use native driver for scale and rotation animations
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animate the FAB in when component mounts
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 6,
      tension: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  const handlePressIn = () => {
    // Only animate scale with native driver
    Animated.spring(scaleAnim, {
      toValue: 0.92,
      friction: 8,
      tension: 300,
      useNativeDriver: true,
    }).start();

    // Use setState for shadow properties instead of animations
    setShadowStyle({
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 8,
    });
  };

  const handlePressOut = () => {
    // Only animate scale with native driver
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 300,
      useNativeDriver: true,
    }).start();

    // Use setState for shadow properties instead of animations
    setShadowStyle({
      shadowOpacity: 0.3,
      shadowRadius: 12,
      elevation: 12,
    });
  };

  const handlePress = () => {
    // Animate rotation on press
    Animated.sequence([
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    // Execute the onPress function
    if (onPress) {
      onPress();
    } else {
      // Default action is to navigate to templates
      router.push("/templates");
    }
  };

  // Get button size based on size prop
  const getFabSize = () => {
    switch (size) {
      case "small":
        return { width: 48, height: 48, iconSize: 22 };
      case "large":
        return { width: 64, height: 64, iconSize: 32 };
      default:
        return { width: 56, height: 56, iconSize: 28 };
    }
  };

  const { width, height, iconSize } = getFabSize();
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "45deg"],
  });

  // Get gradient colors based on color
  const getGradientColors = () => {
    if (color === colors.primary) {
      return colors.gradientPrimary;
    } else if (color === colors.secondary) {
      return colors.gradientSecondary;
    } else if (color === colors.accent) {
      return colors.gradientAccent;
    } else if (color === colors.success) {
      return colors.gradientSuccess;
    } else {
      return [color, color];
    }
  };

  return (
    <Animated.View
      style={[
        styles.fabContainer,
        shadowStyle, // Use the state-based shadow style
        {
          transform: [{ scale: scaleAnim }],
        },
      ]}>
      <TouchableOpacity
        style={[
          styles.touchableContainer,
          {
            width,
            height,
            borderRadius: width / 2,
          },
        ]}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}>
        {gradient ? (
          <LinearGradient
            colors={getGradientColors()}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={[
              styles.fab,
              {
                width,
                height,
                borderRadius: width / 2,
              },
            ]}>
            <Animated.View
              style={{
                transform: [{ rotate: spin }],
              }}>
              <Ionicons name={icon as any} size={iconSize} color="#fff" />
            </Animated.View>
          </LinearGradient>
        ) : (
          <View
            style={[
              styles.fab,
              {
                width,
                height,
                borderRadius: width / 2,
                backgroundColor: color,
              },
            ]}>
            <Animated.View
              style={{
                transform: [{ rotate: spin }],
              }}>
              <Ionicons name={icon as any} size={iconSize} color="#fff" />
            </Animated.View>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  fabContainer: {
    position: "absolute",
    bottom: Platform.OS === "ios" ? 120 : 100, // Increased to ensure it's above the tab bar
    right: 20,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 999, // Increased z-index to ensure it's above all elements
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 5 },
  },
  touchableContainer: {
    overflow: "hidden",
  },
  fab: {
    justifyContent: "center",
    alignItems: "center",
  },
});
