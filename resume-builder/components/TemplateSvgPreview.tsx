import React from "react";
import { StyleSheet, View } from "react-native";
import Svg, { Line, Rect, Text as SvgText } from "react-native-svg";

interface TemplateSvgPreviewProps {
  templateId: string;
}

const TemplateSvgPreview: React.FC<TemplateSvgPreviewProps> = ({ templateId }) => {
  // Function to render the appropriate SVG preview based on template ID
  const renderPreview = () => {
    switch (templateId) {
      case "modern":
        return <ModernPreview />;
      case "professional":
        return <ProfessionalPreview />;
      case "creative":
        return <CreativePreview />;
      case "minimal":
        return <MinimalPreview />;
      case "executive":
        return <ExecutivePreview />;
      default:
        return <ModernPreview />;
    }
  };

  return <View style={styles.container}>{renderPreview()}</View>;
};

// Modern template preview
const ModernPreview = () => (
  <Svg width="100%" height="100%" viewBox="0 0 300 400">
    {/* Background */}
    <Rect x="0" y="0" width="300" height="400" fill="#ffffff" />

    {/* Sidebar */}
    <Rect x="0" y="0" width="100" height="400" fill="#2c3e50" />

    {/* Name and title */}
    <SvgText x="20" y="40" fill="#ffffff" fontSize="14" fontWeight="bold">
      John Doe
    </SvgText>
    <SvgText x="20" y="60" fill="#ecf0f1" fontSize="10">
      Software Engineer
    </SvgText>

    {/* Contact info */}
    <SvgText x="20" y="90" fill="#ffffff" fontSize="8">
      <EMAIL>
    </SvgText>
    <SvgText x="20" y="105" fill="#ffffff" fontSize="8">
      (555) 123-4567
    </SvgText>
    <SvgText x="20" y="120" fill="#ffffff" fontSize="8">
      San Francisco, CA
    </SvgText>

    {/* Skills section */}
    <SvgText x="20" y="150" fill="#ffffff" fontSize="10" fontWeight="bold">
      SKILLS
    </SvgText>
    <Line x1="20" y1="155" x2="80" y2="155" stroke="#3498db" strokeWidth="1" />

    {/* Skill tags */}
    <Rect x="20" y="165" width="60" height="15" rx="2" fill="#3498db" />
    <SvgText x="25" y="175" fill="#ffffff" fontSize="8">
      JavaScript
    </SvgText>

    <Rect x="20" y="185" width="40" height="15" rx="2" fill="#3498db" />
    <SvgText x="25" y="195" fill="#ffffff" fontSize="8">
      React
    </SvgText>

    <Rect x="20" y="205" width="50" height="15" rx="2" fill="#3498db" />
    <SvgText x="25" y="215" fill="#ffffff" fontSize="8">
      Node.js
    </SvgText>

    {/* Main content */}
    <SvgText x="120" y="40" fill="#2c3e50" fontSize="12" fontWeight="bold">
      SUMMARY
    </SvgText>
    <Line x1="120" y1="45" x2="280" y2="45" stroke="#3498db" strokeWidth="1" />
    <SvgText x="120" y="60" fill="#333333" fontSize="8">
      Experienced software engineer with expertise in web development...
    </SvgText>

    {/* Experience section */}
    <SvgText x="120" y="90" fill="#2c3e50" fontSize="12" fontWeight="bold">
      EXPERIENCE
    </SvgText>
    <Line x1="120" y1="95" x2="280" y2="95" stroke="#3498db" strokeWidth="1" />

    <SvgText x="120" y="110" fill="#2980b9" fontSize="10" fontWeight="bold">
      Tech Innovations Inc.
    </SvgText>
    <SvgText x="250" y="110" fill="#7f8c8d" fontSize="8">
      2020-Present
    </SvgText>
    <SvgText x="120" y="125" fill="#333333" fontSize="9" fontWeight="bold">
      Senior Software Engineer
    </SvgText>
    <SvgText x="120" y="140" fill="#333333" fontSize="8">
      Lead developer for web and mobile applications...
    </SvgText>

    {/* Education section */}
    <SvgText x="120" y="170" fill="#2c3e50" fontSize="12" fontWeight="bold">
      EDUCATION
    </SvgText>
    <Line x1="120" y1="175" x2="280" y2="175" stroke="#3498db" strokeWidth="1" />

    <SvgText x="120" y="190" fill="#2980b9" fontSize="10" fontWeight="bold">
      University of California
    </SvgText>
    <SvgText x="250" y="190" fill="#7f8c8d" fontSize="8">
      2015-2019
    </SvgText>
    <SvgText x="120" y="205" fill="#333333" fontSize="9" fontWeight="bold">
      B.S. Computer Science
    </SvgText>
  </Svg>
);

// Professional template preview
const ProfessionalPreview = () => (
  <Svg width="100%" height="100%" viewBox="0 0 300 400">
    {/* Background */}
    <Rect x="0" y="0" width="300" height="400" fill="#ffffff" />

    {/* Header */}
    <SvgText x="150" y="30" fill="#000000" fontSize="16" fontWeight="bold" textAnchor="middle">
      JOHN DOE
    </SvgText>
    <SvgText x="150" y="45" fill="#666666" fontSize="10" textAnchor="middle">
      Software Engineer
    </SvgText>

    {/* Contact info */}
    <SvgText x="150" y="65" fill="#333333" fontSize="8" textAnchor="middle">
      <EMAIL> | (555) 123-4567 | San Francisco, CA
    </SvgText>

    {/* Divider */}
    <Line x1="30" y1="80" x2="270" y2="80" stroke="#000000" strokeWidth="1" />

    {/* Summary section */}
    <SvgText x="30" y="100" fill="#000000" fontSize="12" fontWeight="bold">
      SUMMARY
    </SvgText>
    <Line x1="30" y1="105" x2="100" y2="105" stroke="#000000" strokeWidth="1" />
    <SvgText x="30" y="120" fill="#333333" fontSize="8">
      Experienced software engineer with expertise in web development...
    </SvgText>

    {/* Experience section */}
    <SvgText x="30" y="150" fill="#000000" fontSize="12" fontWeight="bold">
      EXPERIENCE
    </SvgText>
    <Line x1="30" y1="155" x2="120" y2="155" stroke="#000000" strokeWidth="1" />

    <SvgText x="30" y="170" fill="#000000" fontSize="10" fontWeight="bold">
      Tech Innovations Inc.
    </SvgText>
    <SvgText x="270" y="170" fill="#666666" fontSize="8" textAnchor="end">
      2020-Present
    </SvgText>
    <SvgText x="30" y="185" fill="#000000" fontSize="9" fontWeight="bold">
      Senior Software Engineer
    </SvgText>
    <SvgText x="30" y="200" fill="#333333" fontSize="8">
      Lead developer for web and mobile applications...
    </SvgText>

    {/* Education section */}
    <SvgText x="30" y="230" fill="#000000" fontSize="12" fontWeight="bold">
      EDUCATION
    </SvgText>
    <Line x1="30" y1="235" x2="120" y2="235" stroke="#000000" strokeWidth="1" />

    <SvgText x="30" y="250" fill="#000000" fontSize="10" fontWeight="bold">
      University of California
    </SvgText>
    <SvgText x="270" y="250" fill="#666666" fontSize="8" textAnchor="end">
      2015-2019
    </SvgText>
    <SvgText x="30" y="265" fill="#000000" fontSize="9" fontWeight="bold">
      B.S. Computer Science
    </SvgText>

    {/* Skills section */}
    <SvgText x="30" y="295" fill="#000000" fontSize="12" fontWeight="bold">
      SKILLS
    </SvgText>
    <Line x1="30" y1="300" x2="80" y2="300" stroke="#000000" strokeWidth="1" />

    <SvgText x="30" y="315" fill="#333333" fontSize="8">
      JavaScript • React • Node.js • TypeScript • AWS
    </SvgText>
  </Svg>
);

// Creative template preview
const CreativePreview = () => (
  <Svg width="100%" height="100%" viewBox="0 0 300 400">
    {/* Background */}
    <Rect x="0" y="0" width="300" height="400" fill="#fffaf0" />

    {/* Header */}
    <Rect x="30" y="20" width="240" height="60" rx="5" fill="#ff9a9e" />
    <SvgText x="150" y="45" fill="#ffffff" fontSize="16" fontWeight="bold" textAnchor="middle">
      John Doe
    </SvgText>
    <SvgText x="150" y="65" fill="#ffffff" fontSize="10" textAnchor="middle">
      Software Engineer
    </SvgText>

    {/* Contact info */}
    <Rect x="60" y="90" width="180" height="40" rx="5" fill="#ffffff" />
    <SvgText x="150" y="110" fill="#333333" fontSize="8" textAnchor="middle">
      <EMAIL>
    </SvgText>
    <SvgText x="150" y="125" fill="#333333" fontSize="8" textAnchor="middle">
      (555) 123-4567
    </SvgText>

    {/* Summary section */}
    <SvgText x="30" y="150" fill="#ff9a9e" fontSize="12" fontWeight="bold">
      SUMMARY
    </SvgText>
    <SvgText x="30" y="165" fill="#333333" fontSize="8">
      Experienced software engineer with expertise in web development...
    </SvgText>

    {/* Experience section */}
    <SvgText x="30" y="195" fill="#ff9a9e" fontSize="12" fontWeight="bold">
      EXPERIENCE
    </SvgText>

    <SvgText x="30" y="215" fill="#ff9a9e" fontSize="10" fontWeight="bold">
      Tech Innovations Inc.
    </SvgText>
    <SvgText x="270" y="215" fill="#666666" fontSize="8" textAnchor="end">
      2020-Present
    </SvgText>
    <SvgText x="30" y="230" fill="#333333" fontSize="9" fontWeight="bold">
      Senior Software Engineer
    </SvgText>
    <SvgText x="30" y="245" fill="#333333" fontSize="8">
      Lead developer for web and mobile applications...
    </SvgText>

    {/* Skills section */}
    <SvgText x="30" y="275" fill="#ff9a9e" fontSize="12" fontWeight="bold">
      SKILLS
    </SvgText>

    <Rect x="30" y="285" width="60" height="20" rx="10" fill="#fff0f0" />
    <SvgText x="60" y="298" fill="#ff9a9e" fontSize="8" textAnchor="middle">
      JavaScript
    </SvgText>

    <Rect x="95" y="285" width="40" height="20" rx="10" fill="#fff0f0" />
    <SvgText x="115" y="298" fill="#ff9a9e" fontSize="8" textAnchor="middle">
      React
    </SvgText>

    <Rect x="140" y="285" width="50" height="20" rx="10" fill="#fff0f0" />
    <SvgText x="165" y="298" fill="#ff9a9e" fontSize="8" textAnchor="middle">
      Node.js
    </SvgText>
  </Svg>
);

// Minimal template preview
const MinimalPreview = () => (
  <Svg width="100%" height="100%" viewBox="0 0 300 400">
    {/* Background */}
    <Rect x="0" y="0" width="300" height="400" fill="#ffffff" />

    {/* Header */}
    <SvgText x="30" y="40" fill="#333333" fontSize="14" fontWeight="bold">
      John Doe
    </SvgText>
    <SvgText x="30" y="55" fill="#666666" fontSize="10">
      Software Engineer
    </SvgText>

    {/* Contact info */}
    <SvgText x="30" y="75" fill="#999999" fontSize="8">
      <EMAIL>
    </SvgText>
    <SvgText x="130" y="75" fill="#999999" fontSize="8">
      (555) 123-4567
    </SvgText>
    <SvgText x="230" y="75" fill="#999999" fontSize="8">
      San Francisco, CA
    </SvgText>

    {/* Divider */}
    <Line x1="30" y1="90" x2="270" y2="90" stroke="#eeeeee" strokeWidth="1" />

    {/* Summary section */}
    <SvgText x="30" y="110" fill="#333333" fontSize="10" fontWeight="bold">
      SUMMARY
    </SvgText>
    <SvgText x="30" y="125" fill="#333333" fontSize="8">
      Experienced software engineer with expertise in web development...
    </SvgText>

    {/* Experience section */}
    <SvgText x="30" y="155" fill="#333333" fontSize="10" fontWeight="bold">
      EXPERIENCE
    </SvgText>

    <SvgText x="30" y="175" fill="#333333" fontSize="10" fontWeight="bold">
      Tech Innovations Inc.
    </SvgText>
    <SvgText x="270" y="175" fill="#999999" fontSize="8" textAnchor="end">
      2020-Present
    </SvgText>
    <SvgText x="30" y="190" fill="#333333" fontSize="9" fontWeight="bold">
      Senior Software Engineer
    </SvgText>
    <SvgText x="30" y="205" fill="#333333" fontSize="8">
      Lead developer for web and mobile applications...
    </SvgText>

    {/* Education section */}
    <SvgText x="30" y="235" fill="#333333" fontSize="10" fontWeight="bold">
      EDUCATION
    </SvgText>

    <SvgText x="30" y="255" fill="#333333" fontSize="10" fontWeight="bold">
      University of California
    </SvgText>
    <SvgText x="270" y="255" fill="#999999" fontSize="8" textAnchor="end">
      2015-2019
    </SvgText>
    <SvgText x="30" y="270" fill="#333333" fontSize="9" fontWeight="bold">
      B.S. Computer Science
    </SvgText>

    {/* Skills section */}
    <SvgText x="30" y="300" fill="#333333" fontSize="10" fontWeight="bold">
      SKILLS
    </SvgText>

    <Rect
      x="30"
      y="315"
      width="60"
      height="15"
      rx="2"
      stroke="#ddd"
      strokeWidth="1"
      fill="#ffffff"
    />
    <SvgText x="60" y="325" fill="#333333" fontSize="8" textAnchor="middle">
      JavaScript
    </SvgText>

    <Rect
      x="95"
      y="315"
      width="40"
      height="15"
      rx="2"
      stroke="#ddd"
      strokeWidth="1"
      fill="#ffffff"
    />
    <SvgText x="115" y="325" fill="#333333" fontSize="8" textAnchor="middle">
      React
    </SvgText>

    <Rect
      x="140"
      y="315"
      width="50"
      height="15"
      rx="2"
      stroke="#ddd"
      strokeWidth="1"
      fill="#ffffff"
    />
    <SvgText x="165" y="325" fill="#333333" fontSize="8" textAnchor="middle">
      Node.js
    </SvgText>
  </Svg>
);

// Executive template preview
const ExecutivePreview = () => (
  <Svg width="100%" height="100%" viewBox="0 0 300 400">
    {/* Background */}
    <Rect x="0" y="0" width="300" height="400" fill="#f9f9f9" />
    <Rect x="20" y="20" width="260" height="360" fill="#ffffff" rx="2" />

    {/* Header */}
    <SvgText x="150" y="50" fill="#1a3a5f" fontSize="16" fontWeight="bold" textAnchor="middle">
      John Doe
    </SvgText>
    <SvgText x="150" y="65" fill="#666666" fontSize="10" fontStyle="italic" textAnchor="middle">
      Software Engineer
    </SvgText>

    {/* Contact info */}
    <SvgText x="150" y="85" fill="#333333" fontSize="8" textAnchor="middle">
      <EMAIL> | (555) 123-4567
    </SvgText>
    <SvgText x="150" y="100" fill="#333333" fontSize="8" textAnchor="middle">
      San Francisco, CA
    </SvgText>

    {/* Divider */}
    <Line x1="50" y1="115" x2="250" y2="115" stroke="#dddddd" strokeWidth="1" />

    {/* Summary section */}
    <SvgText x="50" y="135" fill="#1a3a5f" fontSize="12" fontWeight="bold">
      PROFESSIONAL SUMMARY
    </SvgText>
    <Line x1="50" y1="140" x2="250" y2="140" stroke="#dddddd" strokeWidth="1" />
    <SvgText x="50" y="155" fill="#555555" fontSize="8" fontStyle="italic">
      Experienced software engineer with expertise in web development...
    </SvgText>

    {/* Experience section */}
    <SvgText x="50" y="185" fill="#1a3a5f" fontSize="12" fontWeight="bold">
      PROFESSIONAL EXPERIENCE
    </SvgText>
    <Line x1="50" y1="190" x2="250" y2="190" stroke="#dddddd" strokeWidth="1" />

    <SvgText x="50" y="210" fill="#1a3a5f" fontSize="10" fontWeight="bold">
      Tech Innovations Inc.
    </SvgText>
    <SvgText x="250" y="210" fill="#666666" fontSize="8" textAnchor="end" fontStyle="italic">
      2020-Present
    </SvgText>
    <SvgText x="50" y="225" fill="#333333" fontSize="9" fontWeight="bold">
      Senior Software Engineer
    </SvgText>
    <SvgText x="50" y="240" fill="#555555" fontSize="8">
      Lead developer for web and mobile applications...
    </SvgText>

    {/* Education section */}
    <SvgText x="50" y="270" fill="#1a3a5f" fontSize="12" fontWeight="bold">
      EDUCATION
    </SvgText>
    <Line x1="50" y1="275" x2="250" y2="275" stroke="#dddddd" strokeWidth="1" />

    <SvgText x="50" y="295" fill="#1a3a5f" fontSize="10" fontWeight="bold">
      University of California
    </SvgText>
    <SvgText x="250" y="295" fill="#666666" fontSize="8" textAnchor="end" fontStyle="italic">
      2015-2019
    </SvgText>
    <SvgText x="50" y="310" fill="#333333" fontSize="9" fontWeight="bold">
      B.S. Computer Science
    </SvgText>

    {/* Skills section */}
    <SvgText x="50" y="340" fill="#1a3a5f" fontSize="12" fontWeight="bold">
      AREAS OF EXPERTISE
    </SvgText>
    <Line x1="50" y1="345" x2="250" y2="345" stroke="#dddddd" strokeWidth="1" />

    <Rect
      x="50"
      y="355"
      width="60"
      height="15"
      rx="2"
      fill="#f5f5f5"
      stroke="#dddddd"
      strokeWidth="1"
    />
    <SvgText x="80" y="365" fill="#333333" fontSize="8" textAnchor="middle">
      JavaScript
    </SvgText>

    <Rect
      x="115"
      y="355"
      width="40"
      height="15"
      rx="2"
      fill="#f5f5f5"
      stroke="#dddddd"
      strokeWidth="1"
    />
    <SvgText x="135" y="365" fill="#333333" fontSize="8" textAnchor="middle">
      React
    </SvgText>

    <Rect
      x="160"
      y="355"
      width="50"
      height="15"
      rx="2"
      fill="#f5f5f5"
      stroke="#dddddd"
      strokeWidth="1"
    />
    <SvgText x="185" y="365" fill="#333333" fontSize="8" textAnchor="middle">
      Node.js
    </SvgText>
  </Svg>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 10,
    backgroundColor: "transparent",
  },
});

export default TemplateSvgPreview;
