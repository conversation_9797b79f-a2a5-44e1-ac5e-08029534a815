import React, { ReactNode, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { borderRadius, colors, shadows, spacing, typography } from '../constants/theme';

interface FormSectionProps {
  title: string;
  icon?: keyof typeof Ionicons.glyphMap;
  children: ReactNode;
  collapsible?: boolean;
  initiallyCollapsed?: boolean;
  onPress?: () => void;
}

const FormSection: React.FC<FormSectionProps> = ({
  title,
  icon,
  children,
  collapsible = false,
  initiallyCollapsed = false,
  onPress,
}) => {
  const [collapsed, setCollapsed] = useState(initiallyCollapsed);
  const [contentHeight] = useState(new Animated.Value(initiallyCollapsed ? 0 : 1));

  const toggleCollapsed = () => {
    if (collapsible) {
      setCollapsed(!collapsed);
      Animated.timing(contentHeight, {
        toValue: collapsed ? 1 : 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
    
    if (onPress) {
      onPress();
    }
  };

  return (
    <View style={[styles.container, shadows.sm]}>
      <TouchableOpacity 
        activeOpacity={collapsible || onPress ? 0.7 : 1}
        onPress={toggleCollapsed}
        style={styles.headerContainer}
      >
        <View style={[
          styles.header, 
          !collapsed && styles.headerWithBorder
        ]}>
          <View style={styles.titleContainer}>
            {icon && (
              <View style={styles.iconContainer}>
                <Ionicons name={icon} size={18} color={colors.primary} />
              </View>
            )}
            <Text style={styles.title}>{title}</Text>
          </View>
          {collapsible && (
            <View style={styles.collapseIconContainer}>
              <Ionicons
                name={collapsed ? 'chevron-down' : 'chevron-up'}
                size={20}
                color={colors.textSecondary}
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
      
      {!collapsed && (
        <View style={styles.content}>
          {children}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.border,
  },
  headerContainer: {
    flexDirection: 'row',
  },
  header: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.backgroundDark,
  },
  headerWithBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize.md,
    fontWeight: '600',
    color: colors.text,
    letterSpacing: 0.3,
  },
  collapseIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: spacing.md,
  },
});

export default FormSection;
