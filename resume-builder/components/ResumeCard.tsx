import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useRef } from "react";
import { Animated, Pressable, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { borderRadius, colors, spacing, typography, shadows } from "../constants/theme";
import { ResumeData } from "../types";

interface ResumeCardProps {
  resume: ResumeData;
  onDelete?: () => void;
  onEdit?: () => void;
  onPreview?: () => void;
}

const ResumeCard: React.FC<ResumeCardProps> = ({ resume, onDelete, onEdit, onPreview }) => {
  // Animation for press feedback
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      router.push(`/resume/${resume.id}`);
    }
  };

  const handlePreview = () => {
    if (onPreview) {
      onPreview();
    } else {
      router.push(`/resume/${resume.id}/preview`);
    }
  };

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      friction: 8,
      tension: 300,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 300,
      useNativeDriver: true,
    }).start();
  };

  // Get template color for the card accent
  const getTemplateColor = () => {
    switch (resume.templateId) {
      case "professional":
        return colors.primary;
      case "creative":
        return colors.accent;
      case "minimal":
        return colors.secondary;
      case "executive":
        return colors.success;
      case "modern":
        return colors.warning;
      default:
        return colors.primary;
    }
  };

  const templateColor = getTemplateColor();

  return (
    <Pressable onPressIn={handlePressIn} onPressOut={handlePressOut} onPress={handleEdit}>
      <Animated.View
        style={[
          styles.cardContainer,
          shadows.md,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}>
        {/* Card Header */}
        <View style={[styles.cardHeader, { borderLeftColor: templateColor }]}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{resume.title}</Text>
            <View style={styles.templateBadge}>
              <Text style={styles.templateText}>
                {resume.templateId.charAt(0).toUpperCase() + resume.templateId.slice(1)}
              </Text>
            </View>
          </View>
          <Text style={styles.date}>Last updated: {formatDate(resume.updatedAt)}</Text>
        </View>

        {/* Card Content */}
        <View style={styles.cardContent}>
          <View style={styles.infoSection}>
            <View style={styles.infoItem}>
              <Ionicons name="person-outline" size={16} color={colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoText}>
                {resume.personalInfo.firstName} {resume.personalInfo.lastName}
              </Text>
            </View>
            
            <View style={styles.infoItem}>
              <Ionicons name="briefcase-outline" size={16} color={colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoText}>{resume.personalInfo.title || "No title"}</Text>
            </View>
          </View>

          <View style={styles.statsSection}>
            <View style={styles.statItem}>
              <View style={styles.statBadge}>
                <Text style={styles.statCount}>{resume.education.length}</Text>
              </View>
              <Text style={styles.statLabel}>
                {resume.education.length === 1 ? "Education" : "Education"}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <View style={styles.statBadge}>
                <Text style={styles.statCount}>{resume.experience.length}</Text>
              </View>
              <Text style={styles.statLabel}>
                {resume.experience.length === 1 ? "Experience" : "Experience"}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <View style={styles.statBadge}>
                <Text style={styles.statCount}>{resume.skills.length}</Text>
              </View>
              <Text style={styles.statLabel}>
                {resume.skills.length === 1 ? "Skill" : "Skills"}
              </Text>
            </View>
          </View>

          {/* Card Actions */}
          <View style={styles.actions}>
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={handleEdit}
            >
              <Ionicons name="create-outline" size={18} color={colors.primary} />
              <Text style={styles.actionText}>Edit</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={handlePreview}
            >
              <Ionicons name="eye-outline" size={18} color={colors.primary} />
              <Text style={styles.actionText}>Preview</Text>
            </TouchableOpacity>

            {onDelete && (
              <TouchableOpacity 
                style={[styles.actionButton, styles.deleteButton]} 
                onPress={onDelete}
              >
                <Ionicons name="trash-outline" size={18} color={colors.error} />
                <Text style={[styles.actionText, { color: colors.error }]}>Delete</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    marginBottom: spacing.lg,
    borderRadius: borderRadius.md,
    backgroundColor: colors.card,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: colors.border,
  },
  cardHeader: {
    padding: spacing.md,
    backgroundColor: colors.backgroundDark,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  title: {
    fontSize: typography.fontSize.lg,
    fontWeight: "700",
    color: colors.text,
    flex: 1,
  },
  templateBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
  },
  templateText: {
    fontSize: typography.fontSize.xs,
    fontWeight: "600",
    color: colors.textSecondary,
  },
  date: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  },
  cardContent: {
    padding: spacing.md,
  },
  infoSection: {
    marginBottom: spacing.md,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  infoIcon: {
    marginRight: spacing.sm,
  },
  infoText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    fontWeight: "500",
  },
  statsSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
    paddingVertical: spacing.sm,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.border,
  },
  statItem: {
    alignItems: "center",
  },
  statBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.backgroundDark,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.xs,
    borderWidth: 1,
    borderColor: colors.border,
  },
  statCount: {
    fontSize: typography.fontSize.sm,
    fontWeight: "700",
    color: colors.primary,
  },
  statLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.sm,
    flex: 1,
    marginHorizontal: spacing.xs,
    justifyContent: "center",
    backgroundColor: colors.backgroundDark,
  },
  deleteButton: {
    backgroundColor: `${colors.error}10`,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: "500",
  },
});

export default ResumeCard;
