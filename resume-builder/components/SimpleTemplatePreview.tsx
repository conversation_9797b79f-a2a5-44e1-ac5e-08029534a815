import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface SimpleTemplatePreviewProps {
  templateId: string;
}

const SimpleTemplatePreview: React.FC<SimpleTemplatePreviewProps> = ({ templateId }) => {
  // Get colors based on template ID
  const getColors = () => {
    switch (templateId) {
      case 'modern':
        return { primary: '#2c3e50', secondary: '#3498db', text: '#333333' };
      case 'professional':
        return { primary: '#000000', secondary: '#444444', text: '#333333' };
      case 'creative':
        return { primary: '#ff9a9e', secondary: '#fad0c4', text: '#333333' };
      case 'minimal':
        return { primary: '#333333', secondary: '#eeeeee', text: '#333333' };
      case 'executive':
        return { primary: '#1a3a5f', secondary: '#dddddd', text: '#333333' };
      default:
        return { primary: '#333333', secondary: '#eeeeee', text: '#333333' };
    }
  };

  const colors = getColors();

  // Render different layouts based on template ID
  const renderLayout = () => {
    switch (templateId) {
      case 'modern':
        return <ModernLayout colors={colors} />;
      case 'professional':
        return <ProfessionalLayout colors={colors} />;
      case 'creative':
        return <CreativeLayout colors={colors} />;
      case 'minimal':
        return <MinimalLayout colors={colors} />;
      case 'executive':
        return <ExecutiveLayout colors={colors} />;
      default:
        return <ModernLayout colors={colors} />;
    }
  };

  return (
    <View style={styles.container}>
      {renderLayout()}
    </View>
  );
};

// Modern template layout
const ModernLayout = ({ colors }: { colors: any }) => (
  <View style={styles.previewContainer}>
    <View style={styles.modernLayout}>
      <View style={[styles.modernSidebar, { backgroundColor: colors.primary }]}>
        <View style={styles.nameContainer}>
          <Text style={[styles.modernName, { color: '#ffffff' }]}>John Doe</Text>
          <Text style={[styles.modernTitle, { color: '#ecf0f1' }]}>Software Engineer</Text>
        </View>
        <View style={styles.contactContainer}>
          <Text style={[styles.contactItem, { color: '#ffffff' }]}><EMAIL></Text>
          <Text style={[styles.contactItem, { color: '#ffffff' }]}>(*************</Text>
        </View>
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: '#ffffff' }]}>SKILLS</Text>
          <View style={styles.skillsContainer}>
            <View style={[styles.skillTag, { backgroundColor: colors.secondary }]}>
              <Text style={[styles.skillText, { color: '#ffffff' }]}>JavaScript</Text>
            </View>
            <View style={[styles.skillTag, { backgroundColor: colors.secondary }]}>
              <Text style={[styles.skillText, { color: '#ffffff' }]}>React</Text>
            </View>
          </View>
        </View>
      </View>
      <View style={styles.modernMain}>
        <View style={styles.sectionContainer}>
          <Text style={[styles.mainSectionTitle, { color: colors.primary }]}>SUMMARY</Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Experienced software engineer with expertise in web development...
          </Text>
        </View>
        <View style={styles.sectionContainer}>
          <Text style={[styles.mainSectionTitle, { color: colors.primary }]}>EXPERIENCE</Text>
          <View style={styles.experienceItem}>
            <View style={styles.experienceHeader}>
              <Text style={[styles.companyName, { color: colors.primary }]}>Tech Innovations Inc.</Text>
              <Text style={styles.date}>2020-Present</Text>
            </View>
            <Text style={[styles.position, { color: colors.text }]}>Senior Software Engineer</Text>
            <Text style={[styles.paragraph, { color: colors.text }]}>
              Lead developer for web and mobile applications...
            </Text>
          </View>
        </View>
      </View>
    </View>
  </View>
);

// Professional template layout
const ProfessionalLayout = ({ colors }: { colors: any }) => (
  <View style={styles.previewContainer}>
    <View style={styles.professionalLayout}>
      <View style={styles.professionalHeader}>
        <Text style={[styles.professionalName, { color: colors.primary }]}>JOHN DOE</Text>
        <Text style={[styles.professionalTitle, { color: colors.text }]}>Software Engineer</Text>
        <Text style={[styles.contactRow, { color: colors.text }]}>
          <EMAIL> | (************* | San Francisco, CA
        </Text>
      </View>
      <View style={[styles.divider, { backgroundColor: colors.primary }]} />
      <View style={styles.sectionContainer}>
        <Text style={[styles.professionalSectionTitle, { color: colors.primary }]}>SUMMARY</Text>
        <View style={[styles.underline, { backgroundColor: colors.primary }]} />
        <Text style={[styles.paragraph, { color: colors.text }]}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.sectionContainer}>
        <Text style={[styles.professionalSectionTitle, { color: colors.primary }]}>EXPERIENCE</Text>
        <View style={[styles.underline, { backgroundColor: colors.primary }]} />
        <View style={styles.experienceItem}>
          <View style={styles.experienceHeader}>
            <Text style={[styles.companyName, { color: colors.primary }]}>Tech Innovations Inc.</Text>
            <Text style={styles.date}>2020-Present</Text>
          </View>
          <Text style={[styles.position, { color: colors.text }]}>Senior Software Engineer</Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Lead developer for web and mobile applications...
          </Text>
        </View>
      </View>
    </View>
  </View>
);

// Creative template layout
const CreativeLayout = ({ colors }: { colors: any }) => (
  <View style={styles.previewContainer}>
    <View style={[styles.creativeLayout, { backgroundColor: '#fffaf0' }]}>
      <View style={[styles.creativeHeader, { backgroundColor: colors.primary }]}>
        <Text style={[styles.creativeName, { color: '#ffffff' }]}>John Doe</Text>
        <Text style={[styles.creativeTitle, { color: '#ffffff' }]}>Software Engineer</Text>
      </View>
      <View style={styles.creativeContact}>
        <Text style={[styles.contactItem, { color: colors.text }]}><EMAIL></Text>
        <Text style={[styles.contactItem, { color: colors.text }]}>(*************</Text>
      </View>
      <View style={styles.sectionContainer}>
        <Text style={[styles.creativeSectionTitle, { color: colors.primary }]}>SUMMARY</Text>
        <Text style={[styles.paragraph, { color: colors.text }]}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.sectionContainer}>
        <Text style={[styles.creativeSectionTitle, { color: colors.primary }]}>EXPERIENCE</Text>
        <View style={styles.experienceItem}>
          <View style={styles.experienceHeader}>
            <Text style={[styles.creativeCompanyName, { color: colors.primary }]}>Tech Innovations Inc.</Text>
            <Text style={styles.date}>2020-Present</Text>
          </View>
          <Text style={[styles.position, { color: colors.text }]}>Senior Software Engineer</Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Lead developer for web and mobile applications...
          </Text>
        </View>
      </View>
    </View>
  </View>
);

// Minimal template layout
const MinimalLayout = ({ colors }: { colors: any }) => (
  <View style={styles.previewContainer}>
    <View style={styles.minimalLayout}>
      <View style={styles.minimalHeader}>
        <Text style={[styles.minimalName, { color: colors.primary }]}>John Doe</Text>
        <Text style={[styles.minimalTitle, { color: colors.text }]}>Software Engineer</Text>
        <View style={styles.contactRow}>
          <Text style={[styles.minimalContactItem, { color: colors.text }]}><EMAIL></Text>
          <Text style={[styles.minimalContactItem, { color: colors.text }]}>(*************</Text>
        </View>
      </View>
      <View style={[styles.divider, { backgroundColor: colors.secondary }]} />
      <View style={styles.sectionContainer}>
        <Text style={[styles.minimalSectionTitle, { color: colors.primary }]}>SUMMARY</Text>
        <Text style={[styles.paragraph, { color: colors.text }]}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.sectionContainer}>
        <Text style={[styles.minimalSectionTitle, { color: colors.primary }]}>EXPERIENCE</Text>
        <View style={styles.experienceItem}>
          <View style={styles.experienceHeader}>
            <Text style={[styles.minimalCompanyName, { color: colors.primary }]}>Tech Innovations Inc.</Text>
            <Text style={styles.date}>2020-Present</Text>
          </View>
          <Text style={[styles.position, { color: colors.text }]}>Senior Software Engineer</Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Lead developer for web and mobile applications...
          </Text>
        </View>
      </View>
    </View>
  </View>
);

// Executive template layout
const ExecutiveLayout = ({ colors }: { colors: any }) => (
  <View style={styles.previewContainer}>
    <View style={[styles.executiveLayout, { backgroundColor: '#f9f9f9' }]}>
      <View style={styles.executiveHeader}>
        <Text style={[styles.executiveName, { color: colors.primary }]}>John Doe</Text>
        <Text style={[styles.executiveTitle, { color: colors.text }]}>Software Engineer</Text>
      </View>
      <Text style={[styles.executiveContact, { color: colors.text }]}>
        <EMAIL> | (*************
      </Text>
      <View style={[styles.divider, { backgroundColor: colors.secondary }]} />
      <View style={styles.sectionContainer}>
        <Text style={[styles.executiveSectionTitle, { color: colors.primary }]}>PROFESSIONAL SUMMARY</Text>
        <View style={[styles.underline, { backgroundColor: colors.secondary }]} />
        <Text style={[styles.paragraph, { color: colors.text }]}>
          Experienced software engineer with expertise in web development...
        </Text>
      </View>
      <View style={styles.sectionContainer}>
        <Text style={[styles.executiveSectionTitle, { color: colors.primary }]}>PROFESSIONAL EXPERIENCE</Text>
        <View style={[styles.underline, { backgroundColor: colors.secondary }]} />
        <View style={styles.experienceItem}>
          <View style={styles.experienceHeader}>
            <Text style={[styles.executiveCompanyName, { color: colors.primary }]}>Tech Innovations Inc.</Text>
            <Text style={[styles.executiveDate, { color: colors.text }]}>2020-Present</Text>
          </View>
          <Text style={[styles.executivePosition, { color: colors.text }]}>Senior Software Engineer</Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Lead developer for web and mobile applications...
          </Text>
        </View>
      </View>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewContainer: {
    flex: 1,
    width: '100%',
    padding: 5,
  },
  // Modern template styles
  modernLayout: {
    flex: 1,
    flexDirection: 'row',
  },
  modernSidebar: {
    width: '30%',
    padding: 8,
  },
  modernMain: {
    width: '70%',
    padding: 8,
    backgroundColor: '#fff',
  },
  modernName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  modernTitle: {
    fontSize: 10,
    marginBottom: 8,
  },
  // Professional template styles
  professionalLayout: {
    flex: 1,
    padding: 8,
    backgroundColor: '#fff',
  },
  professionalHeader: {
    alignItems: 'center',
    marginBottom: 8,
  },
  professionalName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  professionalTitle: {
    fontSize: 12,
    marginBottom: 2,
  },
  professionalSectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  // Creative template styles
  creativeLayout: {
    flex: 1,
    padding: 8,
  },
  creativeHeader: {
    padding: 8,
    alignItems: 'center',
    borderRadius: 4,
    marginBottom: 8,
  },
  creativeName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  creativeTitle: {
    fontSize: 12,
  },
  creativeContact: {
    backgroundColor: '#fff',
    padding: 8,
    alignItems: 'center',
    borderRadius: 4,
    marginBottom: 8,
  },
  creativeSectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  creativeCompanyName: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Minimal template styles
  minimalLayout: {
    flex: 1,
    padding: 8,
    backgroundColor: '#fff',
  },
  minimalHeader: {
    marginBottom: 8,
  },
  minimalName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  minimalTitle: {
    fontSize: 12,
    marginBottom: 2,
  },
  minimalContactItem: {
    fontSize: 10,
    marginRight: 8,
  },
  minimalSectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  minimalCompanyName: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Executive template styles
  executiveLayout: {
    flex: 1,
    padding: 8,
  },
  executiveHeader: {
    alignItems: 'center',
    marginBottom: 4,
  },
  executiveName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  executiveTitle: {
    fontSize: 12,
    fontStyle: 'italic',
    marginBottom: 2,
  },
  executiveContact: {
    fontSize: 10,
    textAlign: 'center',
    marginBottom: 8,
  },
  executiveSectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  executiveCompanyName: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  executiveDate: {
    fontSize: 10,
    fontStyle: 'italic',
  },
  executivePosition: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  // Common components
  nameContainer: {
    marginBottom: 8,
  },
  contactContainer: {
    marginBottom: 8,
  },
  contactRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 4,
    fontSize: 10,
  },
  contactItem: {
    fontSize: 10,
    marginBottom: 2,
  },
  sectionContainer: {
    marginBottom: 8,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 2,
    marginRight: 4,
    marginBottom: 4,
  },
  skillText: {
    fontSize: 8,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  mainSectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  paragraph: {
    fontSize: 10,
    marginBottom: 4,
  },
  experienceItem: {
    marginBottom: 8,
  },
  experienceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 2,
  },
  companyName: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  date: {
    fontSize: 10,
    color: '#666',
  },
  position: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  divider: {
    height: 1,
    marginVertical: 8,
  },
  underline: {
    height: 1,
    width: '100%',
    marginBottom: 4,
  },
});

export default SimpleTemplatePreview;
