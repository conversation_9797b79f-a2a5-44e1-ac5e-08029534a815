import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  Animated,
  Dimensions,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { Template } from "../types";
import WebViewTemplatePreview from "./WebViewTemplatePreview";

interface EnhancedTemplateCardProps {
  template: Template;
  selected?: boolean;
  onSelect: (templateId: string) => void;
  index: number;
}

const { width } = Dimensions.get("window");
const cardWidth = width / 2 - 24; // Two cards per row with spacing

const EnhancedTemplateCard: React.FC<EnhancedTemplateCardProps> = ({
  template,
  selected = false,
  onSelect,
  index,
}) => {
  // Animation value for card scale
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  // Handle press animation
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      friction: 8,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  // Get tag color based on template type
  const getTagColor = () => {
    switch (template.id) {
      case "modern":
        return "#3498db"; // Blue
      case "professional":
        return "#2ecc71"; // Green
      case "creative":
        return "#e74c3c"; // Red
      case "minimal":
        return "#9b59b6"; // Purple
      case "executive":
        return "#f39c12"; // Orange
      default:
        return "#95a5a6"; // Gray
    }
  };

  // Get tag icon based on template type
  const getTagIcon = () => {
    switch (template.id) {
      case "modern":
        return "trending-up";
      case "professional":
        return "briefcase";
      case "creative":
        return "color-palette";
      case "minimal":
        return "remove";
      case "executive":
        return "business";
      default:
        return "document";
    }
  };

  return (
    <Animated.View
      style={[
        styles.cardContainer,
        { transform: [{ scale: scaleAnim }] },
        // Add a slight delay to the entrance animation based on index
        { opacity: 1, translateY: 0 },
      ]}>
      <TouchableOpacity
        style={[styles.card, selected && styles.selectedCard]}
        onPress={() => onSelect(template.id)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}>
        {/* Template Tag */}
        <View style={[styles.templateTag, { backgroundColor: getTagColor() }]}>
          <Ionicons name={getTagIcon()} size={10} color="#fff" />
          <Text style={styles.templateTagText}>{template.name}</Text>
        </View>

        {/* Preview Container */}
        <View style={styles.previewContainer}>
          <View style={styles.previewWrapper}>
            <WebViewTemplatePreview templateId={template.id} />
          </View>

          {/* Selection Indicator */}
          {selected && (
            <View style={styles.selectedOverlay}>
              <View style={styles.checkmarkContainer}>
                <Ionicons name="checkmark-circle" size={36} color="#3498db" />
              </View>
            </View>
          )}
        </View>

        {/* Content */}
        <View style={styles.content}></View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    width: cardWidth,
    marginBottom: 16,
    marginHorizontal: 8,
    alignSelf: "flex-start",
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 2,
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.03,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
    }),
    borderWidth: 1,
    borderColor: "#eee",
    flex: 1,
    width: "100%",
  },
  selectedCard: {
    borderColor: "#3498db",
    borderWidth: 2,
    ...Platform.select({
      ios: {
        shadowColor: "#3498db",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  templateTag: {
    position: "absolute",
    top: 8,
    left: 0,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderTopRightRadius: 2,
    borderBottomRightRadius: 2,
    flexDirection: "row",
    alignItems: "center",
    zIndex: 10,
  },
  templateTagText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 10,
    marginLeft: 3,
  },
  previewContainer: {
    position: "relative",
    aspectRatio: 0.7, // Portrait aspect ratio for resume
    backgroundColor: "#f9f9f9",
    overflow: "hidden",
    width: "100%",
  },
  previewWrapper: {
    width: "100%",
    height: "100%",
    padding: 0,
    margin: 0,
    backgroundColor: "#f9f9f9",
  },
  selectedOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(52, 152, 219, 0.15)",
    justifyContent: "center",
    alignItems: "center",
  },
  checkmarkContainer: {
    width: 40,
    height: 40,
    borderRadius: 20, // Keep this circular for the checkmark
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    justifyContent: "center",
    alignItems: "center",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  content: {
    padding: 0,
  },

  buttonIcon: {
    marginLeft: 4,
  },
});

export default EnhancedTemplateCard;
