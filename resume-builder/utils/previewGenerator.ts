import { getDummyResumeData } from "./dummyData";
import { generateResumeHTML } from "./pdf";

// Helper function to extract the body content from HTML
const extractBodyContent = (html: string): string => {
  // Extract content between <body> and </body> tags
  const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
  if (bodyMatch && bodyMatch[1]) {
    return bodyMatch[1];
  }

  // If no body tags found, return the original HTML
  return html;
};

// Generate HTML preview for a template
export const generateTemplatePreview = (templateId: string): string => {
  try {
    console.log(`Getting dummy data for template: ${templateId}`);
    // Get dummy data with the specified template ID
    const dummyData = getDummyResumeData(templateId);

    console.log(`Generating HTML for template: ${templateId}`);
    // Generate HTML using the existing template generator
    const resumeHtml = generateResumeHTML(dummyData);
    console.log(`Resume HTML generated, length: ${resumeHtml}`);

    // Instead of extracting body content, use the full HTML
    // This avoids potential issues with HTML parsing

    // Use a simple approach with direct HTML
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <style>
          html, body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100%;
            width: 100%;
          }
          .preview-container {
            transform: scale(0.25);
            transform-origin: 0 0;
            width: 400%;
            height: 400%;
            position: absolute;
            top: 0;
            left: 0;
            overflow: visible;
          }
        </style>
      </head>
      <body>
        <div class="preview-container">
          ${resumeHtml}
        </div>
      </body>
      </html>
    `;

    console.log(`Final HTML generated, length: ${html.length}`);
    return html;
  } catch (error) {
    console.error("Error in generateTemplatePreview:", error);
    throw error;
  }
};
