import { ResumeData } from "../../types";
import { getOrderedSections } from "../sectionOrder";

// Minimal template
export const generateMinimalTemplate = (resume: ResumeData): string => {
  const { personalInfo, education, experience, skills, projects, certifications } = resume;

  // Get the ordered sections
  const orderedSections = getOrderedSections(resume);

  // Generate HTML sections
  const educationHTML = education
    .map(
      (edu) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${edu.institution}</h3>
        <p class="date">${edu.startDate} - ${edu.endDate}</p>
      </div>
      <p><strong>${edu.degree} in ${edu.fieldOfStudy}</strong></p>
      <p>${edu.location}</p>
      <p>${edu.description}</p>
    </div>
  `
    )
    .join("");

  const experienceHTML = experience
    .map(
      (exp) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${exp.company}</h3>
        <p class="date">${exp.startDate} - ${exp.isCurrent ? "Present" : exp.endDate}</p>
      </div>
      <p><strong>${exp.position}</strong></p>
      <p>${exp.location}</p>
      <p>${exp.description}</p>
      <ul>
        ${exp.highlights.map((highlight) => `<li>${highlight}</li>`).join("")}
      </ul>
    </div>
  `
    )
    .join("");

  const skillsHTML = skills
    .map(
      (skill) => `
    <span class="skill-tag">${skill.name}</span>
  `
    )
    .join("");

  const projectsHTML = projects
    .map(
      (project) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${project.name}</h3>
        <p class="date">${project.startDate} - ${project.endDate}</p>
      </div>
      <p>${project.description}</p>
      <p><strong>Technologies:</strong> ${project.technologies.join(", ")}</p>
      ${project.url ? `<p><a href="${project.url}">${project.url}</a></p>` : ""}
    </div>
  `
    )
    .join("");

  const certificationsHTML = certifications
    .map(
      (cert) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${cert.name}</h3>
        <p class="date">${cert.date}</p>
      </div>
      <p><strong>Issuer:</strong> ${cert.issuer}</p>
      ${cert.description ? `<p>${cert.description}</p>` : ""}
      ${cert.url ? `<p><a href="${cert.url}">${cert.url}</a></p>` : ""}
    </div>
  `
    )
    .join("");

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${resume.title}</title>
      <style>
        body {
          font-family: 'Helvetica Neue', Arial, sans-serif;
          margin: 0;
          padding: 0;
          color: #333;
          line-height: 1.5;
          background-color: #fff;
        }
        .container {
          max-width: 800px;
          margin: 0 auto;
          padding: 40px;
        }
        .header {
          margin-bottom: 30px;
          border-bottom: 1px solid #eee;
          padding-bottom: 20px;
        }
        .header h1 {
          font-size: 28px;
          margin-bottom: 5px;
          font-weight: 300;
          letter-spacing: 1px;
        }
        .header h2 {
          font-size: 18px;
          font-weight: 300;
          margin-top: 0;
          color: #666;
        }
        .contact-info {
          margin-bottom: 20px;
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
        }
        .contact-info p {
          margin: 0;
          font-size: 14px;
        }
        .section {
          margin-bottom: 25px;
        }
        .section h2 {
          font-size: 16px;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-bottom: 15px;
          font-weight: 400;
          color: #333;
        }
        .section-item {
          margin-bottom: 20px;
        }
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: baseline;
        }
        .item-header h3 {
          margin-bottom: 5px;
          margin-top: 0;
          font-weight: 500;
        }
        .date {
          color: #999;
          font-size: 14px;
        }
        .skill-tag {
          display: inline-block;
          padding: 3px 10px;
          margin-right: 10px;
          margin-bottom: 10px;
          border: 1px solid #ddd;
          border-radius: 3px;
          font-size: 14px;
        }
        ul {
          margin-top: 5px;
          padding-left: 20px;
        }
        li {
          margin-bottom: 5px;
        }
        a {
          color: #333;
          text-decoration: none;
          border-bottom: 1px dotted #999;
        }
        .skills-container {
          display: flex;
          flex-wrap: wrap;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${personalInfo.firstName} ${personalInfo.lastName}</h1>
          <h2>${personalInfo.title}</h2>

          <div class="contact-info">
            <p>${personalInfo.email}</p>
            <p>${personalInfo.phone}</p>
            <p>${personalInfo.city}, ${personalInfo.state}</p>
            ${personalInfo.linkedin ? `<p>LinkedIn: ${personalInfo.linkedin}</p>` : ""}
            ${personalInfo.github ? `<p>GitHub: ${personalInfo.github}</p>` : ""}
            ${personalInfo.website ? `<p>Website: ${personalInfo.website}</p>` : ""}
          </div>
        </div>

        <div class="section">
          <h2>Summary</h2>
          <p>${personalInfo.summary}</p>
        </div>

        ${orderedSections
          .map((section) => {
            let sectionHTML = "";
            let hasContent = false;

            switch (section.id) {
              case "education":
                sectionHTML = educationHTML;
                hasContent = education.length > 0;
                break;
              case "experience":
                sectionHTML = experienceHTML;
                hasContent = experience.length > 0;
                break;
              case "skills":
                sectionHTML = `<div class="skills-container">${skillsHTML}</div>`;
                hasContent = skills.length > 0;
                break;
              case "projects":
                sectionHTML = projectsHTML;
                hasContent = projects.length > 0;
                break;
              case "certifications":
                sectionHTML = certificationsHTML;
                hasContent = certifications.length > 0;
                break;
            }

            // Only render sections that have content
            return hasContent
              ? `
        <div class="section">
          <h2>${section.title}</h2>
          ${sectionHTML}
        </div>`
              : "";
          })
          .join("")}
      </div>
    </body>
    </html>
  `;
};
