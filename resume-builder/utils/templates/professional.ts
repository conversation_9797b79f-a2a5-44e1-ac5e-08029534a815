import { ResumeData } from "../../types";
import { getOrderedSections } from "../sectionOrder";

// Professional template
export const generateProfessionalTemplate = (resume: ResumeData): string => {
  const { personalInfo, education, experience, skills, projects, certifications } = resume;

  // Get the ordered sections
  const orderedSections = getOrderedSections(resume);

  // Generate HTML sections
  const educationHTML = education
    .map(
      (edu) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${edu.institution}</h3>
        <p class="date">${edu.startDate} - ${edu.endDate}</p>
      </div>
      <p><strong>${edu.degree} in ${edu.fieldOfStudy}</strong></p>
      <p>${edu.location}</p>
      <p>${edu.description}</p>
    </div>
  `
    )
    .join("");

  const experienceHTML = experience
    .map(
      (exp) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${exp.company}</h3>
        <p class="date">${exp.startDate} - ${exp.isCurrent ? "Present" : exp.endDate}</p>
      </div>
      <p><strong>${exp.position}</strong></p>
      <p>${exp.location}</p>
      <p>${exp.description}</p>
      <ul>
        ${exp.highlights.map((highlight) => `<li>${highlight}</li>`).join("")}
      </ul>
    </div>
  `
    )
    .join("");

  const skillsHTML = skills
    .map(
      (skill) => `
    <span class="skill-tag">${skill.name}</span>
  `
    )
    .join("");

  const projectsHTML = projects
    .map(
      (project) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${project.name}</h3>
        <p class="date">${project.startDate} - ${project.endDate}</p>
      </div>
      <p>${project.description}</p>
      <p><strong>Technologies:</strong> ${project.technologies.join(", ")}</p>
      ${project.url ? `<p><a href="${project.url}">${project.url}</a></p>` : ""}
    </div>
  `
    )
    .join("");

  const certificationsHTML = certifications
    .map(
      (cert) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${cert.name}</h3>
        <p class="date">${cert.date}</p>
      </div>
      <p><strong>Issuer:</strong> ${cert.issuer}</p>
      ${cert.description ? `<p>${cert.description}</p>` : ""}
      ${cert.url ? `<p><a href="${cert.url}">${cert.url}</a></p>` : ""}
    </div>
  `
    )
    .join("");

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${resume.title}</title>
      <style>
        body {
          font-family: 'Times New Roman', serif;
          margin: 0;
          padding: 0;
          color: #333;
          line-height: 1.5;
          background-color: #fff;
        }
        .container {
          max-width: 800px;
          margin: 0 auto;
          padding: 40px;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #000;
          padding-bottom: 20px;
        }
        .header h1 {
          font-size: 28px;
          margin-bottom: 5px;
          text-transform: uppercase;
          letter-spacing: 2px;
        }
        .header h2 {
          font-size: 18px;
          font-weight: normal;
          margin-top: 0;
          color: #666;
        }
        .contact-info {
          text-align: center;
          margin-bottom: 20px;
        }
        .contact-info p {
          margin: 5px 0;
        }
        .section {
          margin-bottom: 25px;
        }
        .section h2 {
          border-bottom: 1px solid #000;
          padding-bottom: 5px;
          margin-bottom: 15px;
          text-transform: uppercase;
          letter-spacing: 1px;
          font-size: 18px;
        }
        .section-item {
          margin-bottom: 20px;
        }
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: baseline;
        }
        .item-header h3 {
          margin-bottom: 5px;
          margin-top: 0;
          font-weight: bold;
        }
        .date {
          color: #666;
          font-style: italic;
        }
        .skill-tag {
          display: inline-block;
          padding: 3px 0;
          margin-right: 15px;
          font-style: italic;
        }
        ul {
          margin-top: 5px;
        }
        a {
          color: #000;
          text-decoration: none;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${personalInfo.firstName} ${personalInfo.lastName}</h1>
          <h2>${personalInfo.title}</h2>
        </div>

        <div class="contact-info">
          <p>${personalInfo.email} | ${personalInfo.phone}</p>
          <p>${personalInfo.address}, ${personalInfo.city}, ${personalInfo.state} ${
    personalInfo.zipCode
  }, ${personalInfo.country}</p>
          ${personalInfo.linkedin ? `<p>LinkedIn: ${personalInfo.linkedin}</p>` : ""}
          ${personalInfo.github ? `<p>GitHub: ${personalInfo.github}</p>` : ""}
          ${personalInfo.website ? `<p>Website: ${personalInfo.website}</p>` : ""}
        </div>

        <div class="section">
          <h2>Summary</h2>
          <p>${personalInfo.summary}</p>
        </div>

        ${orderedSections
          .map((section) => {
            let sectionHTML = "";
            let hasContent = false;

            switch (section.id) {
              case "education":
                sectionHTML = educationHTML;
                hasContent = education.length > 0;
                break;
              case "experience":
                sectionHTML = experienceHTML;
                hasContent = experience.length > 0;
                break;
              case "skills":
                sectionHTML = `<div>${skillsHTML}</div>`;
                hasContent = skills.length > 0;
                break;
              case "projects":
                sectionHTML = projectsHTML;
                hasContent = projects.length > 0;
                break;
              case "certifications":
                sectionHTML = certificationsHTML;
                hasContent = certifications.length > 0;
                break;
            }

            // Only render sections that have content
            return hasContent
              ? `
        <div class="section">
          <h2>${section.title}</h2>
          ${sectionHTML}
        </div>`
              : "";
          })
          .join("")}
      </div>
    </body>
    </html>
  `;
};
