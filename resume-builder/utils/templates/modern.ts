import { ResumeData } from "../../types";
import { getOrderedSections } from "../sectionOrder";

// Modern template
export const generateModernTemplate = (resume: ResumeData): string => {
  const { personalInfo, education, experience, skills, projects, certifications } = resume;

  // Get the ordered sections
  const orderedSections = getOrderedSections(resume);

  // Generate HTML sections
  const educationHTML = education
    .map(
      (edu) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${edu.institution}</h3>
        <p class="date">${edu.startDate} - ${edu.endDate}</p>
      </div>
      <p class="no-margin-bottom"><strong>${edu.degree} in ${edu.fieldOfStudy}</strong></p>
      <p class="no-margin-bottom no-margin-top">${edu.location}</p>
      <p class="no-margin-top">${edu.description}</p>
    </div>
  `
    )
    .join("");

  const experienceHTML = experience
    .map(
      (exp) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${exp.company}</h3>
        <p class="date">${exp.startDate} - ${exp.isCurrent ? "Present" : exp.endDate}</p>
      </div>
      <p class="no-margin-bottom"><strong>${exp.position}</strong></p>
      <p class="no-margin-bottom no-margin-top">${exp.location}</p>
      <p class="no-margin-top no-margin-bottom">${exp.description}</p>
      <ul>
        ${exp.highlights.map((highlight) => `<li>${highlight}</li>`).join("")}
      </ul>
    </div>
  `
    )
    .join("");

  const skillsHTML = skills
    .map(
      (skill) => `
    <span class="skill-tag">${skill.name}</span>
  `
    )
    .join("");

  const projectsHTML = projects
    .map(
      (project) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${project.name}</h3>
        <p class="date">${project.startDate} - ${project.endDate}</p>
      </div>
      <p class="no-margin-bottom">${project.description}</p>
      <p class="no-margin-bottom no-margin-top"><strong>Technologies:</strong> ${project.technologies.join(
        ", "
      )}</p>
      ${
        project.url
          ? `<p class="no-margin-top"><a href="${project.url}">${project.url}</a></p>`
          : ""
      }
    </div>
  `
    )
    .join("");

  const certificationsHTML = certifications
    .map(
      (cert) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${cert.name}</h3>
        <p class="date">${cert.date}</p>
      </div>
      <p class="no-margin-bottom"><strong>Issuer:</strong> ${cert.issuer}</p>
      ${cert.description ? `<p class="no-margin-bottom no-margin-top">${cert.description}</p>` : ""}
      ${cert.url ? `<p class="no-margin-top"><a href="${cert.url}">${cert.url}</a></p>` : ""}
    </div>
  `
    )
    .join("");

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${resume.title}</title>
      <style>
        body {
          font-family: 'Roboto', 'Arial', sans-serif;
          margin: 0;
          padding: 0;
          color: #333;
          line-height: 1.4;
        }
        p {
          margin-top: 0.5em;
          margin-bottom: 0.5em;
        }
        .container {
          display: grid;
          grid-template-columns: 30% 70%;
          max-width: 800px;
          margin: 0 auto;
        }
        .sidebar {
          background-color: #2c3e50;
          color: white;
          padding: 30px;
        }
        .main-content {
          padding: 30px;
        }
        .header h1 {
          font-size: 28px;
          margin-bottom: 5px;
          color: white;
        }
        .header h2 {
          font-size: 18px;
          font-weight: normal;
          margin-top: 0;
          color: #ecf0f1;
        }
        .contact-info {
          margin-top: 30px;
        }
        .contact-info p {
          margin: 3px 0;
          font-size: 14px;
        }
        .section {
          margin-bottom: 25px;
        }
        .section h2 {
          color: #2c3e50;
          border-bottom: 2px solid #3498db;
          padding-bottom: 5px;
          margin-bottom: 15px;
        }
        .sidebar .section h2 {
          color: #ecf0f1;
          border-bottom: 2px solid #3498db;
        }
        .section-item {
          margin-bottom: 20px;
        }
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: baseline;
        }
        .item-header h3 {
          margin-bottom: 5px;
          margin-top: 0;
          color: #2980b9;
        }
        .date {
          color: #7f8c8d;
          font-style: italic;
        }
        .skill-tag {
          display: inline-block;
          background-color: #3498db;
          color: white;
          padding: 5px 10px;
          margin: 5px;
          border-radius: 3px;
        }
        ul {
          margin-top: 5px;
          padding-left: 20px;
        }
        li {
          margin-bottom: 5px;
        }
        a {
          color: #3498db;
          text-decoration: none;
        }
        .no-margin-bottom {
          margin-bottom: 0;
        }
        .no-margin-top {
          margin-top: 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="sidebar">
          <div class="header">
            <h1>${personalInfo.firstName} ${personalInfo.lastName}</h1>
            <h2>${personalInfo.title}</h2>
          </div>

          <div class="contact-info">
            <p>${personalInfo.email}</p>
            <p>${personalInfo.phone}</p>
            <p>${personalInfo.address}, ${personalInfo.city}</p>
            <p>${personalInfo.state} ${personalInfo.zipCode}, ${personalInfo.country}</p>
            ${personalInfo.linkedin ? `<p>LinkedIn: ${personalInfo.linkedin}</p>` : ""}
            ${personalInfo.github ? `<p>GitHub: ${personalInfo.github}</p>` : ""}
            ${personalInfo.website ? `<p>Website: ${personalInfo.website}</p>` : ""}
          </div>

          ${
            skills.length > 0
              ? `
          <div class="section">
            <h2>Skills</h2>
            <div>
              ${skillsHTML}
            </div>
          </div>
          `
              : ""
          }
        </div>

        <div class="main-content">
          <div class="section">
            <h2>Summary</h2>
            <p>${personalInfo.summary}</p>
          </div>

          ${orderedSections
            .map((section) => {
              let sectionHTML = "";
              let hasContent = false;

              switch (section.id) {
                case "education":
                  sectionHTML = educationHTML;
                  hasContent = education.length > 0;
                  break;
                case "experience":
                  sectionHTML = experienceHTML;
                  hasContent = experience.length > 0;
                  break;
                case "projects":
                  sectionHTML = projectsHTML;
                  hasContent = projects.length > 0;
                  break;
                case "certifications":
                  sectionHTML = certificationsHTML;
                  hasContent = certifications.length > 0;
                  break;
                case "skills":
                  // Skills are already in the sidebar for this template
                  return "";
              }

              // Only render sections that have content
              return hasContent
                ? `
          <div class="section">
            <h2>${section.title}</h2>
            ${sectionHTML}
          </div>
            `
                : "";
            })
            .join("")}
        </div>
      </div>
    </body>
    </html>
  `;
};
