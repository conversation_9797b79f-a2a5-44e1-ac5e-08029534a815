import { ResumeData } from "../../types";
import { getOrderedSections } from "../sectionOrder";

// Executive template
export const generateExecutiveTemplate = (resume: ResumeData): string => {
  const { personalInfo, education, experience, skills, projects, certifications } = resume;

  // Get the ordered sections
  const orderedSections = getOrderedSections(resume);

  // Generate HTML sections
  const educationHTML = education
    .map(
      (edu) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${edu.institution}</h3>
        <p class="date">${edu.startDate} - ${edu.endDate}</p>
      </div>
      <p><strong>${edu.degree} in ${edu.fieldOfStudy}</strong></p>
      <p>${edu.location}</p>
      <p>${edu.description}</p>
    </div>
  `
    )
    .join("");

  const experienceHTML = experience
    .map(
      (exp) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${exp.company}</h3>
        <p class="date">${exp.startDate} - ${exp.isCurrent ? "Present" : exp.endDate}</p>
      </div>
      <p><strong>${exp.position}</strong></p>
      <p>${exp.location}</p>
      <p>${exp.description}</p>
      <ul>
        ${exp.highlights.map((highlight) => `<li>${highlight}</li>`).join("")}
      </ul>
    </div>
  `
    )
    .join("");

  const skillsHTML = skills
    .map(
      (skill) => `
    <span class="skill-tag">${skill.name}</span>
  `
    )
    .join("");

  const projectsHTML = projects
    .map(
      (project) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${project.name}</h3>
        <p class="date">${project.startDate} - ${project.endDate}</p>
      </div>
      <p>${project.description}</p>
      <p><strong>Technologies:</strong> ${project.technologies.join(", ")}</p>
      ${project.url ? `<p><a href="${project.url}">${project.url}</a></p>` : ""}
    </div>
  `
    )
    .join("");

  const certificationsHTML = certifications
    .map(
      (cert) => `
    <div class="section-item">
      <div class="item-header">
        <h3>${cert.name}</h3>
        <p class="date">${cert.date}</p>
      </div>
      <p><strong>Issuer:</strong> ${cert.issuer}</p>
      ${cert.description ? `<p>${cert.description}</p>` : ""}
      ${cert.url ? `<p><a href="${cert.url}">${cert.url}</a></p>` : ""}
    </div>
  `
    )
    .join("");

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${resume.title}</title>
      <style>
        body {
          font-family: 'Georgia', 'Times New Roman', serif;
          margin: 0;
          padding: 0;
          color: #333;
          line-height: 1.6;
          background-color: #f9f9f9;
        }
        .container {
          max-width: 800px;
          margin: 0 auto;
          padding: 40px;
          background-color: #fff;
          box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #1a3a5f;
          padding-bottom: 20px;
        }
        .header h1 {
          font-size: 32px;
          margin-bottom: 5px;
          color: #1a3a5f;
          letter-spacing: 1px;
        }
        .header h2 {
          font-size: 18px;
          font-weight: normal;
          margin-top: 0;
          color: #666;
          font-style: italic;
        }
        .contact-info {
          text-align: center;
          margin-bottom: 30px;
          font-size: 15px;
        }
        .contact-info p {
          margin: 5px 0;
        }
        .section {
          margin-bottom: 30px;
        }
        .section h2 {
          color: #1a3a5f;
          border-bottom: 1px solid #ddd;
          padding-bottom: 5px;
          margin-bottom: 20px;
          font-size: 22px;
        }
        .section-item {
          margin-bottom: 25px;
        }
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: baseline;
        }
        .item-header h3 {
          margin-bottom: 5px;
          margin-top: 0;
          font-weight: bold;
          color: #1a3a5f;
        }
        .date {
          color: #666;
          font-style: italic;
        }
        .skill-tag {
          display: inline-block;
          padding: 5px 12px;
          margin-right: 10px;
          margin-bottom: 10px;
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          border-radius: 3px;
          font-size: 14px;
        }
        ul {
          margin-top: 10px;
          padding-left: 20px;
        }
        li {
          margin-bottom: 8px;
        }
        a {
          color: #1a3a5f;
          text-decoration: none;
        }
        .skills-container {
          display: flex;
          flex-wrap: wrap;
        }
        .summary {
          font-style: italic;
          line-height: 1.8;
          color: #555;
          margin-bottom: 30px;
          padding: 0 20px;
        }
        .divider {
          height: 1px;
          background-color: #eee;
          margin: 30px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${personalInfo.firstName} ${personalInfo.lastName}</h1>
          <h2>${personalInfo.title}</h2>
        </div>

        <div class="contact-info">
          <p>${personalInfo.email} | ${personalInfo.phone}</p>
          <p>${personalInfo.address}, ${personalInfo.city}, ${personalInfo.state} ${
    personalInfo.zipCode
  }</p>
          ${personalInfo.linkedin ? `<p>LinkedIn: ${personalInfo.linkedin}</p>` : ""}
          ${personalInfo.github ? `<p>GitHub: ${personalInfo.github}</p>` : ""}
          ${personalInfo.website ? `<p>Website: ${personalInfo.website}</p>` : ""}
        </div>

        <div class="section">
          <h2>Professional Summary</h2>
          <p class="summary">${personalInfo.summary}</p>
        </div>

        <div class="divider"></div>

        ${orderedSections
          .map((section) => {
            let sectionHTML = "";
            let sectionTitle = "";
            let hasContent = false;

            switch (section.id) {
              case "education":
                sectionHTML = educationHTML;
                sectionTitle = "Education";
                hasContent = education.length > 0;
                break;
              case "experience":
                sectionHTML = experienceHTML;
                sectionTitle = "Professional Experience";
                hasContent = experience.length > 0;
                break;
              case "skills":
                sectionHTML = `<div class="skills-container">${skillsHTML}</div>`;
                sectionTitle = "Areas of Expertise";
                hasContent = skills.length > 0;
                break;
              case "projects":
                sectionHTML = projectsHTML;
                sectionTitle = "Notable Projects";
                hasContent = projects.length > 0;
                break;
              case "certifications":
                sectionHTML = certificationsHTML;
                sectionTitle = "Certifications & Credentials";
                hasContent = certifications.length > 0;
                break;
            }

            // Only render sections that have content
            return hasContent
              ? `
        <div class="section">
          <h2>${sectionTitle}</h2>
          ${sectionHTML}
        </div>`
              : "";
          })
          .join("")}
      </div>
    </body>
    </html>
  `;
};
