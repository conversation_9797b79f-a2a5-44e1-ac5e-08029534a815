import { ResumeData } from "../types";

// Default section order if none is specified
export const DEFAULT_SECTION_ORDER = [
  "education",
  "experience",
  "skills",
  "projects",
  "certifications",
];

// Get the ordered sections based on the resume's sectionOrder
export const getOrderedSections = (resume: ResumeData) => {
  // If no sectionOrder is specified, use the default order
  const sectionOrder = resume.sectionOrder || DEFAULT_SECTION_ORDER;

  // Create an object to hold the sections
  const sections: Record<string, any> = {
    education: {
      title: "Education",
      content: resume.education,
      html: "",
    },
    experience: {
      title: "Experience",
      content: resume.experience,
      html: "",
    },
    skills: {
      title: "Skills",
      content: resume.skills,
      html: "",
    },
    projects: {
      title: "Projects",
      content: resume.projects,
      html: "",
    },
    certifications: {
      title: "Certifications",
      content: resume.certifications,
      html: "",
    },
  };

  // Return the sections in the specified order
  return sectionOrder
    .map((id) => {
      // Make sure the section exists in our sections object
      if (!sections[id]) {
        console.warn(`Section with id ${id} not found in sections object`);
        return null;
      }
      return { id, ...sections[id] };
    })
    .filter((section) => section !== null);
};
