import * as FileSystem from "expo-file-system";
import * as Print from "expo-print";
import * as Sharing from "expo-sharing";
import { ResumeData } from "../types";
import { generateCreativeTemplate } from "./templates/creative";
import { generateExecutiveTemplate } from "./templates/executive";
import { generateMinimalTemplate } from "./templates/minimal";
import { generateModernTemplate } from "./templates/modern";
import { generateProfessionalTemplate } from "./templates/professional";

// Generate HTML for the resume
export const generateResumeHTML = (resume: ResumeData): string => {
  // Select template based on templateId
  switch (resume.templateId) {
    case "modern":
      return generateModernTemplate(resume);
    case "professional":
      return generateProfessionalTemplate(resume);
    case "creative":
      return generateCreativeTemplate(resume);
    case "minimal":
      return generateMinimalTemplate(resume);
    case "executive":
      return generateExecutiveTemplate(resume);
    default:
      return generateModernTemplate(resume);
  }
};

// Generate PDF from resume data
export const generatePDF = async (resume: ResumeData): Promise<string> => {
  try {
    const html = generateResumeHTML(resume);
    const { uri } = await Print.printToFileAsync({ html });

    // Create a more readable filename
    const fileName = `${resume.personalInfo.firstName}_${resume.personalInfo.lastName}_Resume.pdf`;
    const newUri = FileSystem.documentDirectory + fileName;

    // Copy the file to a location with a better name
    await FileSystem.copyAsync({
      from: uri,
      to: newUri,
    });

    // Delete the original file
    await FileSystem.deleteAsync(uri);

    return newUri;
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
};

// Share the generated PDF
export const sharePDF = async (uri: string): Promise<void> => {
  try {
    if (!(await Sharing.isAvailableAsync())) {
      throw new Error("Sharing is not available on this device");
    }

    await Sharing.shareAsync(uri, {
      mimeType: "application/pdf",
      dialogTitle: "Share your resume",
      UTI: "com.adobe.pdf",
    });
  } catch (error) {
    console.error("Error sharing PDF:", error);
    throw error;
  }
};
