import AsyncStorage from '@react-native-async-storage/async-storage';
import { ResumeData, ResumeHistory } from '../types';

// Storage keys
const RESUMES_STORAGE_KEY = 'resume_builder_resumes';
const HISTORY_STORAGE_KEY = 'resume_builder_history';

// Save a resume
export const saveResume = async (resume: ResumeData): Promise<void> => {
  try {
    // Get existing resumes
    const existingResumesJson = await AsyncStorage.getItem(RESUMES_STORAGE_KEY);
    const existingResumes: ResumeData[] = existingResumesJson ? JSON.parse(existingResumesJson) : [];
    
    // Check if resume already exists
    const index = existingResumes.findIndex(r => r.id === resume.id);
    
    if (index !== -1) {
      // Update existing resume
      existingResumes[index] = resume;
    } else {
      // Add new resume
      existingResumes.push(resume);
    }
    
    // Save back to storage
    await AsyncStorage.setItem(RESUMES_STORAGE_KEY, JSON.stringify(existingResumes));
    
    // Add to history
    await addToHistory(resume);
  } catch (error) {
    console.error('Error saving resume:', error);
    throw error;
  }
};

// Get all resumes
export const getResumes = async (): Promise<ResumeData[]> => {
  try {
    const resumesJson = await AsyncStorage.getItem(RESUMES_STORAGE_KEY);
    return resumesJson ? JSON.parse(resumesJson) : [];
  } catch (error) {
    console.error('Error getting resumes:', error);
    throw error;
  }
};

// Get a specific resume by ID
export const getResumeById = async (id: string): Promise<ResumeData | null> => {
  try {
    const resumes = await getResumes();
    return resumes.find(resume => resume.id === id) || null;
  } catch (error) {
    console.error('Error getting resume by ID:', error);
    throw error;
  }
};

// Delete a resume
export const deleteResume = async (id: string): Promise<void> => {
  try {
    const resumes = await getResumes();
    const updatedResumes = resumes.filter(resume => resume.id !== id);
    await AsyncStorage.setItem(RESUMES_STORAGE_KEY, JSON.stringify(updatedResumes));
  } catch (error) {
    console.error('Error deleting resume:', error);
    throw error;
  }
};

// Add a resume to history
export const addToHistory = async (resume: ResumeData): Promise<void> => {
  try {
    const historyJson = await AsyncStorage.getItem(HISTORY_STORAGE_KEY);
    const history: ResumeHistory[] = historyJson ? JSON.parse(historyJson) : [];
    
    const historyEntry: ResumeHistory = {
      id: `history_${Date.now()}`,
      resumeId: resume.id,
      title: resume.title,
      templateId: resume.templateId,
      timestamp: new Date().toISOString(),
      snapshot: { ...resume }
    };
    
    history.push(historyEntry);
    
    // Limit history to last 50 entries
    const limitedHistory = history.slice(-50);
    
    await AsyncStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(limitedHistory));
  } catch (error) {
    console.error('Error adding to history:', error);
    throw error;
  }
};

// Get resume history
export const getHistory = async (): Promise<ResumeHistory[]> => {
  try {
    const historyJson = await AsyncStorage.getItem(HISTORY_STORAGE_KEY);
    return historyJson ? JSON.parse(historyJson) : [];
  } catch (error) {
    console.error('Error getting history:', error);
    throw error;
  }
};

// Clear all history
export const clearHistory = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify([]));
  } catch (error) {
    console.error('Error clearing history:', error);
    throw error;
  }
};
