import { ResumeData } from "../types";

// Generate dummy resume data for template previews
export const getDummyResumeData = (templateId: string): ResumeData => {
  return {
    id: "preview",
    title: "Preview Resume",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    templateId,
    sectionOrder: ["education", "experience", "skills", "projects", "certifications"],
    personalInfo: {
      firstName: "Alex",
      lastName: "Johnson",
      email: "<EMAIL>",
      phone: "(*************",
      address: "123 Main Street",
      city: "San Francisco",
      state: "CA",
      zipCode: "94105",
      country: "USA",
      title: "Software Engineer",
      summary:
        "Experienced software engineer with expertise in web development, mobile applications, and cloud infrastructure.",
      linkedin: "linkedin.com/in/alexjohnson",
      github: "github.com/alexjohnson",
      website: "alexjohnson.dev",
    },
    education: [
      {
        id: "edu1",
        institution: "University of California",
        degree: "Bachelor of Science",
        fieldOfStudy: "Computer Science",
        startDate: "2015",
        endDate: "2019",
        location: "Berkeley, CA",
        description:
          "Graduated with honors. Specialized in software engineering and artificial intelligence.",
        gpa: "3.8",
      },
    ],
    experience: [
      {
        id: "exp1",
        company: "Tech Innovations Inc.",
        position: "Senior Software Engineer",
        startDate: "2020",
        endDate: "Present",
        location: "San Francisco, CA",
        description: "Lead developer for web and mobile applications.",
        highlights: [
          "Developed scalable backend services using Node.js and Express",
          "Implemented CI/CD pipelines reducing deployment time by 40%",
        ],
        isCurrent: true,
      },
      {
        id: "exp2",
        company: "Digital Solutions LLC",
        position: "Software Developer",
        startDate: "2019",
        endDate: "2020",
        location: "San Jose, CA",
        description: "Full-stack developer working on client projects.",
        highlights: [
          "Built responsive web applications using React",
          "Collaborated with design team to implement UI/UX improvements",
        ],
        isCurrent: false,
      },
    ],
    skills: [
      { id: "skill1", name: "JavaScript" },
      { id: "skill2", name: "React" },
      { id: "skill3", name: "Node.js" },
      { id: "skill4", name: "TypeScript" },
      { id: "skill5", name: "AWS" },
    ],
    projects: [
      {
        id: "proj1",
        name: "E-commerce Platform",
        description: "Built a full-stack e-commerce platform with React, Node.js, and MongoDB.",
        startDate: "2020",
        endDate: "2021",
        url: "github.com/alexjohnson/ecommerce",
        technologies: ["React", "Node.js", "MongoDB", "Express"],
      },
    ],
    certifications: [
      {
        id: "cert1",
        name: "AWS Certified Solutions Architect",
        issuer: "Amazon Web Services",
        date: "2021",
        url: "aws.amazon.com/certification",
        description: "Professional level certification for designing distributed systems on AWS.",
      },
    ],
  };
};
