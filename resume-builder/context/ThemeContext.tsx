import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { createContext, useContext, useEffect, useState } from "react";
import { useColorScheme } from "react-native";
import { colors, darkColors, lightColors } from "../constants/theme";

// Define theme types
export type ThemeType = "light" | "dark" | "system";

// Define the context shape
interface ThemeContextType {
  theme: ThemeType;
  isDark: boolean;
  colors: typeof lightColors;
  setTheme: (theme: ThemeType) => void;
  toggleTheme: () => void;
}

// Create the context with default values
const ThemeContext = createContext<ThemeContextType>({
  theme: "system",
  isDark: false,
  colors,
  setTheme: () => {},
  toggleTheme: () => {},
});

// Storage key for persisting theme preference
const THEME_STORAGE_KEY = "resume_builder_theme_preference";

// Theme provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get the device color scheme
  const deviceColorScheme = useColorScheme();
  
  // State for the current theme preference
  const [theme, setThemeState] = useState<ThemeType>("system");
  
  // Determine if dark mode is active based on theme preference and device settings
  const isDark = 
    theme === "dark" || (theme === "system" && deviceColorScheme === "dark");
  
  // Get the appropriate color set based on dark mode status
  const currentColors = isDark ? darkColors : lightColors;

  // Load saved theme preference on mount
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme && (savedTheme === "light" || savedTheme === "dark" || savedTheme === "system")) {
          setThemeState(savedTheme as ThemeType);
        }
      } catch (error) {
        console.error("Failed to load theme preference:", error);
      }
    };

    loadThemePreference();
  }, []);

  // Save theme preference when it changes
  useEffect(() => {
    const saveThemePreference = async () => {
      try {
        await AsyncStorage.setItem(THEME_STORAGE_KEY, theme);
      } catch (error) {
        console.error("Failed to save theme preference:", error);
      }
    };

    saveThemePreference();
  }, [theme]);

  // Function to set the theme
  const setTheme = (newTheme: ThemeType) => {
    setThemeState(newTheme);
  };

  // Function to toggle between light and dark themes
  const toggleTheme = () => {
    if (theme === "system") {
      setThemeState(isDark ? "light" : "dark");
    } else {
      setThemeState(theme === "light" ? "dark" : "light");
    }
  };

  // Context value
  const contextValue: ThemeContextType = {
    theme,
    isDark,
    colors: currentColors,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

export default ThemeContext;
