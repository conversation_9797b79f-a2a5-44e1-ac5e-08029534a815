const { getDefaultConfig } = require("expo/metro-config");

const config = getDefaultConfig(__dirname);

// Add support for SVG
config.resolver.assetExts = config.resolver.assetExts.filter((ext) => ext !== "svg");
config.resolver.sourceExts = [...config.resolver.sourceExts, "svg"];

// Use SVG transformer
config.transformer.babelTransformerPath = require.resolve("react-native-svg-transformer");

module.exports = config;
