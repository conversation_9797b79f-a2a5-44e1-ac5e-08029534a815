import {
  <PERSON>vas,
  Circle,
  Group,
  Path,
  Skia,
  Text as SkiaText,
  useFont,
  vec,
} from "@shopify/react-native-skia";
import React, { useEffect, useState } from "react";
import { Dimensions, StyleSheet, TouchableOpacity, View } from "react-native";
import {
  Easing,
  useDerivedValue,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from "react-native-reanimated";

// Types
type Player = "X" | "O" | null;
type GameStatus = "ongoing" | "win" | "draw";

const { width } = Dimensions.get("window");
const BOARD_SIZE = width * 0.9;
const CELL_SIZE = BOARD_SIZE / 3;
const STROKE_WIDTH = 8;
const SYMBOL_SIZE = CELL_SIZE * 0.6;

const TicTacToeGame = () => {
  const [board, setBoard] = useState<Player[]>(Array(9).fill(null));
  const [isXNext, setIsXNext] = useState(true);
  const [gameStatus, setGameStatus] = useState<GameStatus>("ongoing");
  const [winner, setWinner] = useState<Player>(null);
  const [winningLine, setWinningLine] = useState<number[]>([]);

  // Animation values
  const xOpacity = useSharedValue(1);
  const oOpacity = useSharedValue(1);
  const lineProgress = useSharedValue(0);
  const scale = useSharedValue(1);
  const pulse = useSharedValue(0);

  // Font loading
  const font = useFont(require("../assets/fonts/SpaceMono-Regular.ttf"), 24);

  // Pulse animation for current player indicator
  useEffect(() => {
    pulse.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0, { duration: 1000, easing: Easing.inOut(Easing.ease) })
      ),
      -1
    );
  }, []);

  const pulseScale = useDerivedValue(() => 1 + pulse.value * 0.05);

  // Check for winner after each move
  useEffect(() => {
    const result = calculateWinner(board);
    if (result) {
      setGameStatus("win");
      setWinner(result.winner);
      setWinningLine(result.line);
      animateWinningLine();
    } else if (board.every((cell) => cell !== null)) {
      setGameStatus("draw");
    }
  }, [board]);

  const animateWinningLine = () => {
    lineProgress.value = withTiming(1, {
      duration: 800,
      easing: Easing.inOut(Easing.ease),
    });
  };

  const handlePress = (index: number) => {
    if (board[index] || gameStatus !== "ongoing") return;

    // Animate symbol appearance
    if (isXNext) {
      xOpacity.value = withTiming(1, { duration: 300 });
    } else {
      oOpacity.value = withTiming(1, { duration: 300 });
    }

    // Board press animation
    scale.value = withSequence(
      withTiming(0.98, { duration: 100 }),
      withTiming(1, { duration: 200 })
    );

    // Update board state
    const newBoard = [...board];
    newBoard[index] = isXNext ? "X" : "O";
    setBoard(newBoard);
    setIsXNext(!isXNext);
  };

  const resetGame = () => {
    setBoard(Array(9).fill(null));
    setIsXNext(true);
    setGameStatus("ongoing");
    setWinner(null);
    setWinningLine([]);
    lineProgress.value = 0;
  };

  const renderSymbol = (symbol: Player, index: number) => {
    if (!symbol) return null;

    const row = Math.floor(index / 3);
    const col = index % 3;
    const x = col * CELL_SIZE + CELL_SIZE / 2;
    const y = row * CELL_SIZE + CELL_SIZE / 2;

    const isWinningSymbol = winningLine.includes(index);

    return (
      <Group opacity={symbol === "X" ? xOpacity : oOpacity}>
        {symbol === "X" ? (
          <XSymbol x={x} y={y} size={SYMBOL_SIZE} active={isWinningSymbol} />
        ) : (
          <OSymbol x={x} y={y} size={SYMBOL_SIZE} active={isWinningSymbol} />
        )}
      </Group>
    );
  };

  const renderWinningLine = () => {
    if (winningLine.length !== 3) return null;

    const positions = winningLine.map((index) => ({
      x: (index % 3) * CELL_SIZE + CELL_SIZE / 2,
      y: Math.floor(index / 3) * CELL_SIZE + CELL_SIZE / 2,
    }));

    const path = Skia.Path.Make();
    path.moveTo(positions[0].x, positions[0].y);
    path.lineTo(positions[2].x, positions[2].y);

    return (
      <Path
        path={path}
        color="#FF5252"
        style="stroke"
        strokeWidth={STROKE_WIDTH * 1.5}
        start={0}
        end={lineProgress.value}
      />
    );
  };

  return (
    <View style={styles.container}>
      <Canvas style={[styles.board, { transform: [{ scale: scale.value }] }]}>
        {/* Board grid */}
        <Path
          path={`M ${CELL_SIZE} 0 L ${CELL_SIZE} ${BOARD_SIZE}`}
          color="#333"
          style="stroke"
          strokeWidth={STROKE_WIDTH}
        />
        <Path
          path={`M ${CELL_SIZE * 2} 0 L ${CELL_SIZE * 2} ${BOARD_SIZE}`}
          color="#333"
          style="stroke"
          strokeWidth={STROKE_WIDTH}
        />
        <Path
          path={`M 0 ${CELL_SIZE} L ${BOARD_SIZE} ${CELL_SIZE}`}
          color="#333"
          style="stroke"
          strokeWidth={STROKE_WIDTH}
        />
        <Path
          path={`M 0 ${CELL_SIZE * 2} L ${BOARD_SIZE} ${CELL_SIZE * 2}`}
          color="#333"
          style="stroke"
          strokeWidth={STROKE_WIDTH}
        />

        {/* Game symbols */}
        {board.map((_, index) => renderSymbol(board[index], index))}

        {/* Winning line */}
        {renderWinningLine()}
      </Canvas>

      {/* Game status */}
      <View style={styles.statusContainer}>
        {gameStatus === "ongoing" ? (
          <Canvas style={styles.statusIndicator}>
            <Group transform={[{ scale: pulseScale }]} origin={vec(25, 25)}>
              {isXNext ? (
                <XSymbol x={25} y={25} size={40} color="#4CAF50" />
              ) : (
                <OSymbol x={25} y={25} size={40} color="#2196F3" />
              )}
            </Group>
            {font && (
              <SkiaText
                x={60}
                y={35}
                text={isXNext ? "Your turn (X)" : "Your turn (O)"}
                font={font}
                color="#333"
              />
            )}
          </Canvas>
        ) : (
          <View style={styles.resultContainer}>
            {font && (
              <>
                <SkiaText
                  x={20}
                  y={30}
                  text={gameStatus === "win" ? `Player ${winner} wins!` : "Game ended in draw!"}
                  font={font}
                  color={
                    gameStatus === "win" ? (winner === "X" ? "#4CAF50" : "#2196F3") : "#FF9800"
                  }
                />
                <TouchableOpacity onPress={resetGame} style={styles.resetButton}>
                  {font && <SkiaText x={20} y={30} text="Play Again" font={font} color="#FFFFFF" />}
                </TouchableOpacity>
              </>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

interface SymbolProps {
  x: number;
  y: number;
  size: number;
  color?: string;
  active?: boolean;
}

const XSymbol = ({ x, y, size, color = "#333", active = false }: SymbolProps) => {
  const rotation = useSharedValue(0);
  const halfSize = size / 2;

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(2 * Math.PI, { duration: 3000, easing: Easing.linear }),
      -1
    );
  }, []);

  const path = Skia.Path.Make();
  path.moveTo(x - halfSize, y - halfSize);
  path.lineTo(x + halfSize, y + halfSize);
  path.moveTo(x + halfSize, y - halfSize);
  path.lineTo(x - halfSize, y + halfSize);

  return (
    <Group transform={[{ rotate: rotation.value }]} origin={vec(x, y)}>
      <Path
        path={path}
        color={active ? "#FF5252" : color}
        style="stroke"
        strokeWidth={STROKE_WIDTH}
        strokeCap="round"
      />
    </Group>
  );
};

const OSymbol = ({ x, y, size, color = "#333", active = false }: SymbolProps) => {
  const radius = size / 2;
  const pulse = useSharedValue(0);

  useEffect(() => {
    if (active) {
      pulse.value = withRepeat(
        withTiming(1, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
        -1
      );
    }
  }, [active]);

  const pulseRadius = useDerivedValue(() => radius * (active ? 1 + pulse.value * 0.1 : 1));

  return (
    <Circle
      cx={x}
      cy={y}
      r={pulseRadius.value}
      color="transparent"
      style="stroke"
      strokeWidth={STROKE_WIDTH}
      color={active ? "#FF5252" : color}
    />
  );
};

const calculateWinner = (squares: Player[]) => {
  const lines = [
    [0, 1, 2],
    [3, 4, 5],
    [6, 7, 8], // rows
    [0, 3, 6],
    [1, 4, 7],
    [2, 5, 8], // columns
    [0, 4, 8],
    [2, 4, 6], // diagonals
  ];

  for (const [a, b, c] of lines) {
    if (squares[a] && squares[a] === squares[b] && squares[a] === squares[c]) {
      return { winner: squares[a], line: [a, b, c] };
    }
  }
  return null;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FAFAFA",
  },
  board: {
    width: BOARD_SIZE,
    height: BOARD_SIZE,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  statusContainer: {
    marginTop: 30,
    height: 80,
    width: "80%",
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    elevation: 3,
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  statusIndicator: {
    width: "100%",
    height: 50,
  },
  resultContainer: {
    width: "100%",
    height: 80,
    justifyContent: "center",
    alignItems: "center",
  },
  resetButton: {
    marginTop: 15,
    backgroundColor: "#4CAF50",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    elevation: 2,
  },
});

export default TicTacToeGame;
