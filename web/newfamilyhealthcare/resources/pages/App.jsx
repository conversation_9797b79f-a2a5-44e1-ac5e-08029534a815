import Header from "../js/components/Header";
import React from "react";
import ReactDOM from "react-dom/client";
import Hero from "../js/components/Hero";
import ManagementAndWhyUs from "../js/components/ManagementAndWhyUs";
import About from "../js/components/About";
import Gallery from "../js/components/Gallery";
import Welcome from "../js/components/Welcome";
import Treatments from "../js/components/Treatments";
import Testimonials from "../js/components/Testimonials";
import Contact from "../js/components/Contact";
import Footer from "../js/components/Footer";

function App() {
    return (
        <div className="font-sans">
            <Header />
            <Hero />
            <Welcome />
            <ManagementAndWhyUs />
            <About />
            <Treatments />
            <Gallery />
            <Testimonials />
            <Contact />
            <Footer />
        </div>
    );
}

export default App;

if (document.getElementById("app")) {
    const Index = ReactDOM.createRoot(document.getElementById("app"));

    Index.render(
        <React.StrictMode>
            <App />
        </React.StrictMode>
    );
}
