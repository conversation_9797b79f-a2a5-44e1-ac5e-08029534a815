import React from "react";

const Header = () => {
    return (
        <header className=" shadow z-50 sticky top-0">
            <div className=" bg-mint-800 text-white text-sm px-4 py-2 flex flex-wrap justify-center md:justify-between items-center">
                <div className="space-x-4 ">
                    <span><EMAIL></span>
                    <span>|</span>
                    <span>+91 9906535852, +91 7051567908</span>
                </div>
                <div className="hidden md:flex space-x-4">
                    <span>🛍 100% QUALITY PRODUCTS</span>
                </div>
            </div>
            <div className="container mx-auto flex justify-between items-center px-4 py-4">
                <div className="flex items-center space-x-2">
                    <img src="/logo.png" alt="Logo" className="w-10 h-10" />
                    <h1 className="text-2xl font-bold text-green-700">
                        Natural
                    </h1>
                </div>
                <nav className="space-x-4 text-green-700 font-medium">
                    <a href="#" className="hover:text-green-500">
                        Home
                    </a>
                    <a href="#about" className="hover:text-green-500">
                        About Us
                    </a>
                    <a href="#products" className="hover:text-green-500">
                        Products
                    </a>
                    <a href="#certification" className="hover:text-green-500">
                        Certification
                    </a>
                    <a href="#notifications" className="hover:text-green-500">
                        Notifications
                    </a>
                    <a href="#contact" className="hover:text-green-500">
                        Contact Us
                    </a>
                </nav>
            </div>
        </header>
    );
};

export default Header;
