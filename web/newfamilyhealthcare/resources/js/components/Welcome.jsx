// components/Welcome.jsx
const stores = [
  {
    name: "NATURAL LIFE CARE CENTRE PVT.LTD",
    address:
      "<PERSON><PERSON><PERSON><PERSON> COLONY BEMINA SRINAGAR, OPPOSITE JVC HOSPITAL BEMINA, NEAR TCT LAB BEMINA SRINAGAR",
    pin: "190018",
    contact: "7006847270",
  },
  {
    name: "Rainawari Srinagar",
    address: "MLA Syeed Akhoon Building 2nd Floor",
    contact: "7051567908",
  },
  {
    name: "Sofi Complex Behama ganderbal",
    contact: "7006756255",
  },
  {
    name: "Jammu, Srinagar, Dooda Kishtwar and Ramban",
    contacts: ["9622526691", "7006847270"],
  },
];

const Welcome = () => (
  <section className="bg-white py-12 px-4 md:px-8">
    <div className="max-w-7xl mx-auto grid md:grid-cols-3 gap-6">
      {/* Left side */}
      <div className="md:col-span-2 bg-mint-800 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-4 text-center">Welcome To Natural Life Care Center</h2>
        <p className="mb-4">Introducing Natural Life care centre Pvt Ltd</p>
        <p className="mb-4">Always at your service.</p>
        <p className="mb-4">
          Natural Life care is a private limited company owned by SAJAD AHMAD, UMAR BASHIR, AJAZ
          AHMED AND WASEEM AHMED. NATURAL LIFE CARE deals with all herbal products. Natural Life
          care are providing services in Almost every district of Jammu and Kashmir. Since our first
          day we have brought the best selection of products and merchandise to our customers. We do
          our best to ensure permanent variety of fantastic items along with unique limited edition
          and seasonal items to fit any budget. We want our customers to be satisfied with our work,
          which is why we provide open communication throughout the duration. all our products are
          tested and positively reviewed by the customers. We provide free scheduled delivery at
          your doorstep within just 48 hours with huge discounts on all our products. We also
          provide easy return.
        </p>
        <p className="mb-4">
          Natural Life care also provide jobs to the unemployed youth and helping our state to grow.
          We are committed to help our customers at every step of the way and provide useful
          services to take care of all their needs. We are constantly growing, expanding our service
          options so if you have something in mind that we don’t let provide, we’d love to hear
          about it.
        </p>
        <button className="bg-gray-800 text-white px-4 py-2 rounded hover:bg-gray-700">
          read more
        </button>
      </div>

      {/* Right side */}
      <div className="bg-mint-800 text-white p-6 rounded-lg">
        <h3 className="text-xl font-bold mb-4 text-center">Our Stores</h3>
        <div className="space-y-4">
          {stores.map((store, index) => (
            <div key={index} className="bg-mint-500 p-4 rounded shadow">
              <h4 className="font-bold text-sm mb-1">{store.name}</h4>
              {store.address && <p className="text-sm">{store.address}</p>}
              {store.pin && <p className="text-sm">PIN CODE: {store.pin}</p>}
              {store.contact && (
                <>
                  <p className="text-sm mt-1">Contact No: {store.contact}</p>
                  <a
                    href={`https://wa.me/${store.contact}`}
                    className="inline-block mt-1 px-3 py-1 bg-white text-green-700 rounded text-sm">
                    Message on Whatsapp
                  </a>
                </>
              )}
              {store.contacts &&
                store.contacts.map((num, i) => (
                  <div key={i} className="mt-1">
                    <p className="text-sm">Contact No: {num}</p>
                    <a
                      href={`https://wa.me/${num}`}
                      className="inline-block mt-1 px-3 py-1 bg-white text-green-700 rounded text-sm">
                      Message on Whatsapp
                    </a>
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>
);
export default Welcome;
