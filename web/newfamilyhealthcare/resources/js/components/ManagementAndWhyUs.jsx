const management = [
  { name: "<PERSON><PERSON><PERSON>", img: "/team/ajaz.jpg" },
  { name: "<PERSON><PERSON><PERSON>", img: "/team/waseem.jpg" },
  { name: "<PERSON><PERSON>", img: "/team/umar.jpg" },
  { name: "<PERSON><PERSON><PERSON>", img: "/team/sajad.jpg" },
];

const whyUs = [
  {
    title: "Heritage",
    description: "High Quality Standardized Natural.",
    icon: "/icons/heritage.png",
  },
  {
    title: "Affordability",
    description: "100% Natural Herbs",
    icon: "/icons/affordability.png",
  },
  {
    title: "Availability",
    description: "Products Are Clinically Proven",
    icon: "/icons/availability.png",
  },
  {
    title: "Quality",
    description: "cGMP, ISO 9001, ISO 22000",
    icon: "/icons/quality.png",
  },
];

const ManagementAndWhyUs = () => {
  return (
    <section className="bg-gray-100 py-12 px-4 md:px-8">
      {/* Management Section */}
      <div className="text-center mb-12">
        <h2 className="text-2xl md:text-3xl font-semibold mb-6">Management</h2>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 justify-center">
          {management.map((person, i) => (
            <div key={i}>
              <img
                src={person.img}
                alt={person.name}
                className="w-full h-48 object-cover rounded shadow"
              />
              <p className="mt-2 font-medium">{person.name}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Why Us Section */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-semibold mb-6">Why Us</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {whyUs.map((item, i) => (
            <div
              key={i}
              className="bg-white text-gray-800 p-6 rounded shadow flex flex-col items-center">
              <img src={item.icon} alt={item.title} className="w-12 h-12 mb-4" />
              <h3 className="text-lg font-semibold">{item.title}</h3>
              <p className="text-sm text-center mb-4">{item.description}</p>
              <button className="bg-gray-800 text-white px-3 py-1 text-sm rounded hover:bg-gray-700">
                read more
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ManagementAndWhyUs;
