import { useState, useEffect } from 'react';
import { useNavigation, useIsFocused } from '@react-navigation/native';

const useTabBarVisibility = (scrollY) => {
  const [isTabBarVisible, setIsTabBarVisible] = useState(true);
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  useEffect(() => {
    if (scrollY > 100) {
      setIsTabBarVisible(false);
    } else {
      setIsTabBarVisible(true);
    }
  }, [scrollY]);

  useEffect(() => {
    if (isFocused) {
      navigation.setOptions({
        tabBarStyle: isTabBarVisible ? {} : { display: 'none' },
      });
    }
  }, [isTabBarVisible, isFocused, navigation]);

  return isTabBarVisible;
};

export default useTabBarVisibility;
