import { View, Text, StyleSheet, ActivityIndicator, FlatList } from 'react-native'
import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { fetchUpdates } from "../store/updatesSlice"
import { SafeAreaView } from 'react-native-safe-area-context'
import MainHeader from '../../components/MainHeader'

const Profile = () => {
  const dispatch = useDispatch()
  const { updates, status, error } = useSelector(state => state.updates)
  console.log(updates);
  useEffect(() => {
    if (status === 'idle')
      dispatch(fetchUpdates())

  }, [status, dispatch]);

  if (status === 'loading')
    return <ActivityIndicator size="large" color="#0000ff" style={styles.container} />

  return <SafeAreaView style={styles.container}
    edges={["left", "right", "top"]}>
    <MainHeader title='Notifications' />

    <Text>Coming Soon</Text>

  </SafeAreaView>

}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    // backgroundColor: 'red'
  }
})
export default Profile