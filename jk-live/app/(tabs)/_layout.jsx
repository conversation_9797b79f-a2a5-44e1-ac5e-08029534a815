import React from 'react'
import { Tabs } from 'expo-router'
import { Ionicons } from '@expo/vector-icons'
import { StatusBar } from 'expo-status-bar'
import TabBar from '../../components/TabBar'
import { SafeAreaProvider } from 'react-native-safe-area-context'

const TabLayout = () => {
  return (
    <SafeAreaProvider>
      <Tabs

        // tabBar={props => <TabBar {...props} />}
        screenOptions={{
          tabBarActiveTintColor: '#000',


          headerShown: false,
          // tabBarActiveBackgroundColor: 'red'

        }}>
        <Tabs.Screen name='home'

          options={{

            tabBarLabel: 'Home',
            tabBarIcon: ({ color }) => <Ionicons name='home' size={24} color={color}

            />
          }} />
        <Tabs.Screen name='profile'

          options={{
            tabBarLabel: 'Profile',
            tabBarIcon: ({ color }) => <Ionicons name='people-circle' size={24} color={color} />

          }} />
        <Tabs.Screen name='settings'

          options={{
            tabBarLabel: 'Settings',
            tabBarIcon: ({ color }) => <Ionicons name='settings-outline' size={24} color={color} />

          }} />

      </Tabs>
      <StatusBar style="dark" />
    </SafeAreaProvider>
  )
}

export default TabLayout