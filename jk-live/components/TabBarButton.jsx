import { View, Text, Pressable, StyleSheet } from 'react-native'
import React, { useEffect } from 'react'
import { icons } from '../assets/icons';

const TabBarButton = (props) => {
  const { isFocused, label, routeName, color } = props;




  return (
    <Pressable {...props} style={styles.container}>
      <View>
        {
          icons[routeName]({
            color
          })
        }
      </View>

      {/* <Text style={{
        color,
        fontSize: 11
      }}>
        {label}
      </Text> */}
    </Pressable>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4
  }
})

export default TabBarButton