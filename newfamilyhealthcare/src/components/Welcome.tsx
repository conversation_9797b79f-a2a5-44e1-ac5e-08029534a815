import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faStore,
  faMapMarkerAlt,
  faPhone,
  faChevronRight,
} from "@fortawesome/free-solid-svg-icons";
import { faWhatsapp as faWhatsappBrand } from "@fortawesome/free-brands-svg-icons";

const stores = [
  {
    name: "NATURAL LIFE CARE CENTRE PVT.LTD",
    address:
      "BOATMEN COLONY BEMINA SRINAGAR, OPPOSITE JVC HOSPITAL BEMINA, NEAR TCT LAB BEMINA SRINAGAR",
    pin: "190018",
    contact: "7006847270",
    isPrimary: true,
  },
  {
    name: "Rainawari Srinagar",
    address: "MLA Syeed Akhoon Building 2nd Floor",
    contact: "7051567908",
  },
  {
    name: "Sofi Complex Behama Ganderbal",
    contact: "7006756255",
  },
  {
    name: "Jammu, Srinagar, Dooda Kishtwar and Ramban",
    contacts: ["9622526691", "7006847270"],
  },
];

const Welcome = () => (
  <section className="py-16 px-4 md:px-8 bg-gradient-to-b from-gray-50 to-white">
    <div className="max-w-7xl mx-auto grid md:grid-cols-3 gap-8">
      {/* Left side - Welcome Content */}
      <div className="md:col-span-2 bg-green-700 text-white p-8 rounded-xl shadow-lg">
        <div className="mb-2">
          <span className="inline-block bg-green-600 text-white text-xs px-3 py-1 rounded-full">
            About Us
          </span>
        </div>
        <h2 className="text-3xl font-bold mb-6">Welcome To Natural Life Care Center</h2>

        <div className="space-y-4 text-gray-100">
          <p>
            Natural Life Care Centre Pvt Ltd is a herbal products company owned by Sajad Ahmad, Umar
            Bashir, Ajaz Ahmed and Waseem Ahmed, providing services across Jammu and Kashmir.
          </p>

          <p>
            Since our inception, we've curated the finest selection of herbal products, offering
            permanent varieties along with seasonal specials to fit every budget. All products are
            rigorously tested and customer-approved.
          </p>

          <div className="bg-green-800 p-4 rounded-lg border-l-4 border-green-500">
            <p className="font-medium">Our Commitment:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Free scheduled doorstep delivery within 48 hours</li>
              <li>Generous discounts on all products</li>
              <li>Easy return policy</li>
              <li>Creating employment opportunities for local youth</li>
            </ul>
          </div>

          <p>
            We're constantly expanding our services. If you need something we don't currently offer,
            we'd love to hear your suggestions.
          </p>
        </div>

        <button className="mt-6 bg-white text-green-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center">
          Read More
          <FontAwesomeIcon icon={faChevronRight} className="ml-2" />
        </button>
      </div>

      {/* Right side - Stores */}
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
        <div className="flex items-center mb-6">
          <FontAwesomeIcon icon={faStore} className="text-green-600 text-xl mr-3" />
          <h3 className="text-2xl font-bold text-gray-800">Our Stores</h3>
        </div>

        <div className="space-y-5">
          {stores.map((store, index) => (
            <div
              key={index}
              className={`p-5 rounded-lg ${
                store.isPrimary ? "bg-green-50 border-2 border-green-200" : "bg-gray-50"
              }`}>
              <div className="flex items-start">
                <div
                  className={`flex-shrink-0 w-3 h-3 mt-1 rounded-full mr-3 ${
                    store.isPrimary ? "bg-green-500" : "bg-gray-400"
                  }`}></div>
                <div>
                  <h4 className="font-bold text-gray-800">{store.name}</h4>

                  {store.address && (
                    <div className="flex items-start mt-2 text-sm text-gray-600">
                      <FontAwesomeIcon
                        icon={faMapMarkerAlt}
                        className="mt-0.5 mr-2 text-green-600 flex-shrink-0"
                      />
                      <div>
                        <p>{store.address}</p>
                        {store.pin && <p className="mt-1">PIN: {store.pin}</p>}
                      </div>
                    </div>
                  )}

                  <div className="mt-3 space-y-2">
                    {store.contact ? (
                      <ContactItem number={store.contact} isPrimary={store.isPrimary} />
                    ) : (
                      store.contacts?.map((num, i) => (
                        <ContactItem key={i} number={num} isPrimary={store.isPrimary} />
                      ))
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>
);

const ContactItem = ({ number, isPrimary }) => (
  <div className="flex items-center">
    <FontAwesomeIcon
      icon={faPhone}
      className={`mr-2 ${isPrimary ? "text-green-600" : "text-gray-500"}`}
    />
    <span className="text-sm">{number}</span>
    <a
      href={`https://wa.me/${number}`}
      className="ml-3 px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded-full flex items-center transition-colors">
      <FontAwesomeIcon icon={faWhatsappBrand} className="mr-1" />
      WhatsApp
    </a>
  </div>
);

export default Welcome;
