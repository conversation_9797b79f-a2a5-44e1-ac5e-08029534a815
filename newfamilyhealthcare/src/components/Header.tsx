import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLeaf,
  faEnvelope,
  faPhone,
  faCertificate,
  faBell,
  faHome,
  faInfoCircle,
  faBoxOpen,
  faAddressCard,
} from "@fortawesome/free-solid-svg-icons";
import { faWhatsapp } from "@fortawesome/free-brands-svg-icons";

const Header = () => {
  return (
    <header className="shadow z-50 sticky top-0">
      <div className="bg-green-800 text-white text-sm px-4 py-2 flex flex-wrap justify-center md:justify-between items-center">
        <div className="space-x-4 flex items-center">
          <span className="flex items-center">
            <FontAwesomeIcon icon={faEnvelope} className="mr-1" />
            <EMAIL>
          </span>
          <span>|</span>
          <span className="flex items-center">
            <FontAwesomeIcon icon={faPhone} className="mr-1" />
            +91 9906535852
          </span>
          <span className="flex items-center">
            <FontAwesomeIcon icon={faWhatsapp} className="mr-1" />
            +91 7051567908
          </span>
        </div>
        <div className="hidden md:flex space-x-4 items-center">
          <FontAwesomeIcon icon={faLeaf} className="text-green-300" />
          <span>100% QUALITY PRODUCTS</span>
        </div>
      </div>
      <div className="container mx-auto flex justify-between items-center px-4 py-4">
        <div className="flex items-center space-x-2">
          <FontAwesomeIcon icon={faLeaf} className="w-10 h-10 text-green-700" />
          <h1 className="text-2xl font-bold text-green-700">Natural</h1>
        </div>
        <nav className="space-x-6 text-green-700 font-medium hidden md:flex">
          <a href="#" className="hover:text-green-500 flex items-center">
            <FontAwesomeIcon icon={faHome} className="mr-1" />
            Home
          </a>
          <a href="#about" className="hover:text-green-500 flex items-center">
            <FontAwesomeIcon icon={faInfoCircle} className="mr-1" />
            About Us
          </a>
          <a href="#products" className="hover:text-green-500 flex items-center">
            <FontAwesomeIcon icon={faBoxOpen} className="mr-1" />
            Products
          </a>
          <a href="#certification" className="hover:text-green-500 flex items-center">
            <FontAwesomeIcon icon={faCertificate} className="mr-1" />
            Certification
          </a>
          <a href="#notifications" className="hover:text-green-500 flex items-center">
            <FontAwesomeIcon icon={faBell} className="mr-1" />
            Notifications
          </a>
          <a href="#contact" className="hover:text-green-500 flex items-center">
            <FontAwesomeIcon icon={faAddressCard} className="mr-1" />
            Contact Us
          </a>
        </nav>
      </div>
    </header>
  );
};

export default Header;
