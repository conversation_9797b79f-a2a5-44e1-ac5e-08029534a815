const Testimonials = () => (
  <section id="testimonials" className="py-20 bg-green-100">
    <div className="container mx-auto px-6 text-center">
      <h3 className="text-4xl font-bold mb-10 text-green-700">What Our Clients Say</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        {[
          {
            quote:
              "I felt completely rejuvenated after my therapy sessions. The environment is so calming!",
            author: "<PERSON><PERSON>",
          },
          {
            quote: "Professional staff and holistic treatments helped me feel healthier than ever.",
            author: "Rajat Verma",
          },
        ].map((testimonial, index) => (
          <blockquote
            key={index}
            className="bg-white p-8 shadow-lg rounded-lg hover:shadow-xl transition">
            <p className="italic text-lg text-gray-700">{`“${testimonial.quote}”`}</p>
            <cite className="block mt-6 font-bold text-green-700">{`– ${testimonial.author}`}</cite>
          </blockquote>
        ))}
      </div>
    </div>
  </section>
);

export default Testimonials;
