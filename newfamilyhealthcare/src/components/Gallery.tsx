const Gallery = () => (
  <section id="gallery" className="py-20 bg-white">
    <div className="container mx-auto px-6 text-center">
      <h3 className="text-4xl font-bold mb-10 text-green-700">Gallery</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {[
          "https://source.unsplash.com/400x300/?nature,water",
          "https://source.unsplash.com/400x300/?health,wellness",
          "https://source.unsplash.com/400x300/?spa,relaxation",
          "https://source.unsplash.com/400x300/?therapy,healing",
          "https://source.unsplash.com/400x300/?yoga,meditation",
          "https://source.unsplash.com/400x300/?ayurveda,herbs",
          "https://source.unsplash.com/400x300/?detox,cleanse",
          "https://source.unsplash.com/400x300/?natural,beauty",
        ].map((src, index) => (
          <img
            key={index}
            src={src}
            alt={`Gallery ${index + 1}`}
            className="rounded-lg shadow-lg hover:shadow-xl transition"
          />
        ))}
      </div>
    </div>
  </section>
);

export default Gallery;
