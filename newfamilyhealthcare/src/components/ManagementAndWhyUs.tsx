import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUsers,
  faQuestionCircle,
  faHistory,
  faHandHoldingUsd,
  faBoxes,
  faAward,
} from "@fortawesome/free-solid-svg-icons";

const management = [
  { name: "<PERSON><PERSON><PERSON>", img: "/team/ajaz.jpg", role: "Founder & CEO" },
  { name: "<PERSON><PERSON><PERSON>", img: "/team/waseem.jpg", role: "Operations Manager" },
  { name: "<PERSON><PERSON> Bashir", img: "/team/umar.jpg", role: "Product Specialist" },
  { name: "<PERSON><PERSON><PERSON>", img: "/team/sajad.jpg", role: "Quality Control" },
];

const whyUs = [
  {
    title: "Heritage",
    description: "High Quality Standardized Natural Products",
    icon: faHistory,
    color: "text-amber-500",
  },
  {
    title: "Affordability",
    description: "100% Natural Herbs at Competitive Prices",
    icon: faHandHoldingUsd,
    color: "text-emerald-500",
  },
  {
    title: "Availability",
    description: "Products Are Clinically Proven & Readily Available",
    icon: faBoxes,
    color: "text-blue-500",
  },
  {
    title: "Quality",
    description: "cGMP, ISO 9001, ISO 22000 Certified",
    icon: faAward,
    color: "text-purple-500",
  },
];

const ManagementAndWhyUs = () => {
  return (
    <section className="py-20 px-4 md:px-8 bg-gradient-to-b from-white to-gray-50">
      {/* Management Team */}
      <div className="max-w-7xl mx-auto mb-24">
        <div className="text-center mb-14">
          <h2 className="text-3xl md:text-4xl font-extrabold text-gray-800 flex items-center justify-center gap-3">
            <FontAwesomeIcon icon={faUsers} className="text-green-600" />
            Our Management Team
          </h2>
          <p className="text-gray-600 mt-2 max-w-xl mx-auto">
            Meet the passionate team behind our natural care products
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10">
          {management.map((person, i) => (
            <div key={i} className="group text-center">
              <div className="relative h-64 w-full rounded-2xl overflow-hidden shadow-lg mb-4">
                <img
                  src={person.img}
                  alt={person.name}
                  className="w-full h-full object-cover transform group-hover:scale-105 transition duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                  <p className="text-white text-left font-medium">{person.role}</p>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-800">{person.name}</h3>
              <p className="text-green-600 text-sm">{person.role}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Why Choose Us */}
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-14">
          <h2 className="text-3xl md:text-4xl font-extrabold text-gray-800 flex items-center justify-center gap-3">
            <FontAwesomeIcon icon={faQuestionCircle} className="text-green-600" />
            Why Choose Us
          </h2>
          <p className="text-gray-600 mt-2 max-w-xl mx-auto">
            Discover what makes our natural products stand out
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {whyUs.map((item, i) => (
            <div
              key={i}
              className="bg-white p-6 rounded-2xl shadow-sm hover:shadow-md transition duration-300 border border-gray-100 hover:border-green-200 text-center">
              <div className={`text-4xl mb-5 ${item.color}`}>
                <FontAwesomeIcon icon={item.icon} />
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">{item.title}</h3>
              <p className="text-gray-600 mb-4 text-sm">{item.description}</p>
              <button className="text-green-600 hover:text-green-800 text-sm font-medium inline-flex items-center gap-1 transition">
                Read more
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ManagementAndWhyUs;
