const Hero = () => (
  <section className="relative h-[80vh] bg-[url('/assets/images/hero.jpg')] bg-cover bg-center">
    {/* Overlay */}
    <div className="absolute inset-0 bg-gradient-to-r from-green-900/60 to-green-700/40 backdrop-brightness-75"></div>

    {/* Content */}
    <div className="relative z-10 h-full flex items-center justify-center px-4 text-center">
      <div className="max-w-2xl text-white">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 leading-tight drop-shadow-lg">
          Healing Through Nature
        </h1>
        <p className="text-lg md:text-xl mb-6 text-gray-100">
          Experience authentic Naturopathy and Ayurvedic wellness
        </p>
        <a
          href="#contact"
          className="inline-block bg-white text-green-700 font-semibold py-3 px-6 rounded-full shadow-md hover:bg-green-100 hover:text-green-800 transition duration-300">
          Get Started
        </a>
      </div>
    </div>
  </section>
);

export default Hero;
