import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faStar,
  faFire,
  faPercent,
  faSearch,
  faFilter,
  faShoppingCart,
  faHeart,
} from "@fortawesome/free-solid-svg-icons";

const products = [
  {
    id: 1,
    name: "Organic Turmeric Powder",
    category: "Spices",
    price: 12.99,
    discountPrice: 11.69,
    rating: 4.8,
    image: "/products/turmeric.jpg",
    isNew: true,
    onDiscount: true,
    description: "100% pure organic turmeric with high curcumin content",
  },
  {
    id: 2,
    name: "Himalayan Shilajit",
    category: "Resins",
    price: 29.99,
    discountPrice: 26.99,
    rating: 4.9,
    image: "/products/shilajit.jpg",
    isNew: false,
    onDiscount: true,
    description: "Premium quality Himalayan shilajit resin",
  },
  {
    id: 3,
    name: "Neem Leaf Capsules",
    category: "Supplements",
    price: 18.5,
    discountPrice: 18.5,
    rating: 4.5,
    image: "/products/neem.jpg",
    isNew: true,
    onDiscount: false,
    description: "Pure neem leaf powder in easy-to-swallow capsules",
  },
  {
    id: 4,
    name: "Cold Pressed Coconut Oil",
    category: "Oils",
    price: 15.99,
    discountPrice: 14.39,
    rating: 4.7,
    image: "/products/coconut-oil.jpg",
    isNew: false,
    onDiscount: true,
    description: "Virgin coconut oil extracted without heat",
  },
  {
    id: 5,
    name: "Triphala Powder",
    category: "Ayurvedic",
    price: 9.99,
    discountPrice: 9.99,
    rating: 4.6,
    image: "/products/triphala.jpg",
    isNew: false,
    onDiscount: false,
    description: "Traditional Ayurvedic herbal formula",
  },
  {
    id: 6,
    name: "Ashwagandha Root",
    category: "Herbs",
    price: 22.99,
    discountPrice: 20.69,
    rating: 4.8,
    image: "/products/ashwagandha.jpg",
    isNew: true,
    onDiscount: true,
    description: "Premium quality ashwagandha root powder",
  },
  {
    id: 7,
    name: "Aloe Vera Gel",
    category: "Skincare",
    price: 8.99,
    discountPrice: 8.99,
    rating: 4.4,
    image: "/products/aloe-vera.jpg",
    isNew: true,
    onDiscount: false,
    description: "Pure cold-processed aloe vera gel",
  },
  {
    id: 8,
    name: "Moringa Leaf Powder",
    category: "Superfoods",
    price: 14.99,
    discountPrice: 13.49,
    rating: 4.7,
    image: "/products/moringa.jpg",
    isNew: false,
    onDiscount: true,
    description: "Nutrient-rich moringa leaf powder",
  },
];

const Products = () => {
  const recentProducts = products.filter((product) => product.isNew);
  const discountedProducts = products.filter((product) => product.onDiscount);

  return (
    <section className="py-16 px-4 md:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Our Natural Products
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover our range of 100% natural, organic, and Ayurvedic products
          </p>
        </div>

        {/* Recent Products */}
        <div className="mb-20">
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-semibold text-gray-800 flex items-center">
              <FontAwesomeIcon icon={faFire} className="mr-2 text-orange-500" />
              Recently Added
            </h3>
            <button className="text-green-600 hover:text-green-800 font-medium">View All</button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {recentProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>

        {/* Discounted Products */}
        <div className="mb-20">
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-semibold text-gray-800 flex items-center">
              <FontAwesomeIcon icon={faPercent} className="mr-2 text-red-500" />
              10% Off Products
            </h3>
            <button className="text-green-600 hover:text-green-800 font-medium">View All</button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {discountedProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>

        {/* Full Product List with Filter */}
        <div>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <h3 className="text-2xl font-semibold text-gray-800">All Products</h3>

            <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
              <div className="relative flex-grow">
                <FontAwesomeIcon
                  icon={faSearch}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search products..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              <div className="relative">
                <FontAwesomeIcon
                  icon={faFilter}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <select className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg appearance-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white">
                  <option>All Categories</option>
                  <option>Spices</option>
                  <option>Herbs</option>
                  <option>Oils</option>
                  <option>Supplements</option>
                  <option>Ayurvedic</option>
                  <option>Skincare</option>
                </select>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          <div className="mt-10 flex justify-center">
            <button className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
              Load More Products
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

const ProductCard = ({ product }) => {
  return (
    <div className="bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100 hover:scale-[1.02]">
      {/* Product Image */}
      <div className="relative group">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
        />

        {/* Badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          {product.isNew && (
            <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium shadow-sm">
              <FontAwesomeIcon icon={faFire} className="mr-1" />
              New
            </span>
          )}
          {product.onDiscount && (
            <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium shadow-sm">
              <FontAwesomeIcon icon={faPercent} className="mr-1" />
              10% Off
            </span>
          )}
        </div>

        {/* Action Buttons */}
        <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            aria-label="Add to Wishlist"
            className="bg-white p-2 rounded-full shadow hover:bg-gray-100 transition">
            <FontAwesomeIcon icon={faHeart} className="text-gray-500 hover:text-red-500" />
          </button>
          <button
            aria-label="Add to Cart"
            className="bg-white p-2 rounded-full shadow hover:bg-gray-100 transition">
            <FontAwesomeIcon icon={faShoppingCart} className="text-gray-500 hover:text-green-600" />
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        <span className="text-xs text-gray-400 uppercase tracking-wide">{product.category}</span>
        <h3 className="font-semibold text-lg text-gray-800 mt-1">{product.name}</h3>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>

        {/* Rating */}
        <div className="flex items-center text-sm mb-3">
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <FontAwesomeIcon
                key={i}
                icon={faStar}
                className={`${i < Math.floor(product.rating) ? "text-amber-400" : "text-gray-300"}`}
              />
            ))}
          </div>
          <span className="text-gray-500 ml-2">({product.rating})</span>
        </div>

        {/* Price + CTA */}
        <div className="flex items-center justify-between">
          <div className="text-gray-800 font-semibold">
            {product.onDiscount ? (
              <>
                <span className="text-lg">${product.discountPrice.toFixed(2)}</span>
                <span className="text-sm text-gray-500 line-through ml-2">
                  ${product.price.toFixed(2)}
                </span>
              </>
            ) : (
              <span className="text-lg">${product.price.toFixed(2)}</span>
            )}
          </div>
          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-1.5 text-sm rounded-full transition-colors">
            Add
          </button>
        </div>
      </div>
    </div>
  );
};

export default Products;
