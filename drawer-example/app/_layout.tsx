import { Drawer } from "expo-router/drawer";

export default function RootLayout() {
  return (
    <Drawer
      screenOptions={{
        drawerType: "slide",
        drawerStyle: {
          animationPlayState: "running",
          animationDuration: "0.3s",
          animationTimingFunction: "ease-in-out",
          backgroundColor: "#f6f6f6",
          width: 240,
        },
        drawerActiveTintColor: "#e91e63",
        drawerInactiveTintColor: "#333",
        drawerLabelStyle: { fontSize: 16, fontWeight: "bold" },
      }}>
      <Drawer.Screen name="index" options={{ title: "Home" }} />
      <Drawer.Screen
        name="(profile)/profile-details"
        options={{ title: "Profile Details", drawerItemStyle: { display: "none" } }}
      />
      {/* Add more screens as needed */}
    </Drawer>
  );
}
