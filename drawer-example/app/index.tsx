import React, { useState } from "react";
import { Dimensions, FlatList, StyleSheet, Text, View } from "react-native";
import { TabView } from "react-native-tab-view";

const SEGMENTS = ["Confirmed", "Unconfirmed", "Cancelled"];
const SCREEN_WIDTH = Dimensions.get("window").width;

type Appointment = {
  id: string;
  name: string;
  time: string;
  status: "confirmed" | "unconfirmed" | "cancelled";
};

const DUMMY_APPOINTMENTS: Appointment[] = [
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `c${i + 1}`,
    name: `Confirmed User ${i + 1}`,
    time: `${9 + (i % 10)}:00 AM`,
    status: "confirmed" as const,
  })),
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `u${i + 1}`,
    name: `Unconfirmed User ${i + 1}`,
    time: `${9 + (i % 10)}:30 AM`,
    status: "unconfirmed" as const,
  })),
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `x${i + 1}`,
    name: `Cancelled User ${i + 1}`,
    time: `${9 + (i % 10)}:45 AM`,
    status: "cancelled" as const,
  })),
];

function AppointmentList({ status }: { status: Appointment["status"] }) {
  const filtered = DUMMY_APPOINTMENTS.filter((a) => a.status === status);
  return (
    <FlatList
      data={filtered}
      keyExtractor={(item) => item.id}
      contentContainerStyle={{ flexGrow: 1, justifyContent: "center" }}
      ListEmptyComponent={<Text style={styles.screenText}>No appointments</Text>}
      renderItem={({ item }) => (
        <View style={styles.appointmentItemFull}>
          <Text style={styles.appointmentName}>{item.name}</Text>
          <Text style={styles.appointmentTime}>{item.time}</Text>
        </View>
      )}
    />
  );
}

function CustomTabBar(props: any) {
  const BUTTON_HEIGHT = 40;
  return (
    <View
      style={{
        backgroundColor: "#e3f0ff",
        height: BUTTON_HEIGHT,
        borderRadius: 32,
        // margin: 12,
        alignSelf: "center",
        // justifyContent: "center",
        // paddingHorizontal: 8,
      }}>
      <View
        style={{
          width: "100%",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          height: BUTTON_HEIGHT,
        }}>
        {props.navigationState.routes.map((route: any, i: number) => {
          const focused = props.navigationState.index === i;
          return (
            <Text
              key={route.key}
              onPress={() => props.jumpTo(route.key)}
              style={{
                flex: 1,
                backgroundColor: focused ? "#4287f5" : "transparent",
                color: focused ? "#fff" : "#222",
                borderRadius: 24,
                height: BUTTON_HEIGHT,
                lineHeight: BUTTON_HEIGHT,
                fontWeight: "200",
                fontSize: 14,
                overflow: "hidden",
                // minWidth: 80,
                textAlign: "center",
              }}>
              {route.title}
            </Text>
          );
        })}
      </View>
    </View>
  );
}

export default function Index() {
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    { key: "confirmed", title: "Confirmed" },
    { key: "unconfirmed", title: "Unconfirmed" },
    { key: "cancelled", title: "Cancelled" },
  ]);

  const renderScene = ({ route }: { route: { key: string } }) => {
    switch (route.key) {
      case "confirmed":
        return <AppointmentList status="confirmed" />;
      case "unconfirmed":
        return <AppointmentList status="unconfirmed" />;
      case "cancelled":
        return <AppointmentList status="cancelled" />;
      default:
        return null;
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: "#fff", padding: 20 }}>
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        // initialLayout={{ width: SCREEN_WIDTH }}
        renderTabBar={CustomTabBar}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  screenText: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 32,
  },
  appointmentItemFull: {
    padding: 16,
    marginVertical: 8,
    width: "100%",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    elevation: 2,
    alignSelf: "stretch",
  },
  appointmentName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#222",
  },
  appointmentTime: {
    fontSize: 16,
    color: "#666",
    marginTop: 4,
  },
});
