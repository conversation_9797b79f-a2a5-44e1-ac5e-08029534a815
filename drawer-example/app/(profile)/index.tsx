import { <PERSON> } from "expo-router";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

const index = () => {
  return (
    <View>
      <Link href="/(profile)/profile-details">
        <Text>Go to Profile Details</Text>
      </Link>
      <Text>Welcome to the Profile Page</Text>
      <Text>Click the link above to view profile details.</Text>
      <Text>Use the drawer to navigate to other sections.</Text>
      <Text>Enjoy exploring the app!</Text>
      <Text>Feel free to customize this page.</Text>
      <Text>Happy coding!</Text>
      <Text>Remember to check out the documentation for more features.</Text>
      <Text>Have a great day!</Text>
      <Text>Stay tuned for more updates.</Text>
      <Text>Thank you for using our app!</Text>
      <Text>We appreciate your support.</Text>
      <Text>Follow us on social media for the latest news.</Text>
      <Text>Join our community for discussions and feedback.</Text>
      <Text>Check out our blog for tips and tutorials.</Text>
      <Text>Explore the settings to customize your experience.</Text>
      <Text>Don't forget to leave a review!</Text>
      <Text>We value your input and suggestions.</Text>
    </View>
  );
};

export default index;

const styles = StyleSheet.create({});
