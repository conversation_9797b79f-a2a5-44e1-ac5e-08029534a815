/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */
const { faker } = require("@faker-js/faker");
const { Product } = require("../src/models");

//COMMAND TO RUN THIS SCRIPT
// node scripts/seedProducts.js or npm run seed:dev

const seedProducts = async () => {
  const products = [];

  for (let i = 0; i < 100; i++) {
    products.push({
      name: `${faker.commerce.productName()} ${faker.word.adjective()}`,
      description: faker.commerce.productDescription(),
      price: faker.commerce.price({ min: 50, max: 2000 }),
      userId: 3,
      status: faker.helpers.arrayElement(["pending", "approved", "rejected"]),
    });
  }

  try {
    await Product.bulkCreate(products);
    console.log("✅ 100 products created successfully!");
  } catch (error) {
    console.error("❌ Error seeding products:", error);
  }
};

seedProducts();
