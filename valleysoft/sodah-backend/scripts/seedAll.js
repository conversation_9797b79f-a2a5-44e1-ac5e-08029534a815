/**
 * <AUTHOR> <<EMAIL>>
 */
const { faker } = require("@faker-js/faker");
const { User, Product, Order } = require("../src/models");

// COMMAND TO RUN THIS SCRIPT
// node scripts/seedAll.js or npm run seed:all

const seedAll = async () => {
  try {
    console.log("🚀 Starting comprehensive seeding...");

    // Clear existing data (optional - uncomment if needed)
    // await Order.destroy({ where: {}, force: true });
    // await Product.destroy({ where: {}, force: true });
    // await User.destroy({ where: {}, force: true });

    // 1. Create Users
    console.log("👥 Creating users...");
    const users = [];

    // Create admin user
    users.push({
      name: "Admin User",
      userName: "admin",
      email: "<EMAIL>",
      password: "admin123456",
      role: "admin",
      phone: "+1234567890",
      status: "active",
    });

    // Create sellers
    for (let i = 0; i < 5; i++) {
      users.push({
        name: faker.person.fullName(),
        userName: `seller_${i + 1}`,
        email: `seller${i + 1}@sodah.com`,
        password: "seller123456",
        role: "seller",
        phone: faker.phone.number(),
        status: "active",
      });
    }

    // Create customers
    for (let i = 0; i < 15; i++) {
      users.push({
        name: faker.person.fullName(),
        userName: `customer_${i + 1}`,
        email: `customer${i + 1}@sodah.com`,
        password: "customer123456",
        role: "customer",
        phone: faker.phone.number(),
        status: "active",
      });
    }

    const createdUsers = await User.bulkCreate(users);
    console.log(`✅ Created ${createdUsers.length} users`);

    // 2. Create Products
    console.log("📦 Creating products...");
    const products = [];
    const sellers = createdUsers.filter((user) => user.role === "seller");
    const categories = ["Electronics", "Clothing", "Home & Kitchen", "Books", "Sports & Outdoors"];

    for (let i = 0; i < 50; i++) {
      const seller = faker.helpers.arrayElement(sellers);
      products.push({
        name: faker.commerce.productName(),
        description: faker.commerce.productDescription(),
        price: faker.commerce.price({ min: 10, max: 1000 }),
        stock: faker.number.int({ min: 0, max: 100 }),
        categoryName: faker.helpers.arrayElement(categories),
        images: [
          faker.image.url({ width: 400, height: 400 }),
          faker.image.url({ width: 400, height: 400 }),
        ],
        featured: faker.helpers.maybe(() => true, { probability: 0.2 }),
        status: faker.helpers.weightedArrayElement([
          { weight: 70, value: "approved" },
          { weight: 20, value: "pending" },
          { weight: 10, value: "rejected" },
        ]),
        userId: seller.id,
      });
    }

    const createdProducts = await Product.bulkCreate(products);
    console.log(`✅ Created ${createdProducts.length} products`);

    // 3. Create Orders
    console.log("🛒 Creating orders...");
    const orders = [];
    const customers = createdUsers.filter(
      (user) => user.role === "customer" || user.role === "admin"
    );
    const approvedProducts = createdProducts.filter(
      (product) => product.status === "approved" && product.stock > 0
    );
    const orderNumbers = new Set();

    // Generate unique order numbers
    const generateOrderNumber = () => {
      let orderNumber;
      do {
        const timestamp = Date.now().toString().slice(-8); // Last 8 digits
        const random = Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0");
        orderNumber = `ORD-${timestamp}-${random}`;
      } while (orderNumbers.has(orderNumber));
      orderNumbers.add(orderNumber);
      return orderNumber;
    };

    for (let i = 0; i < 75; i++) {
      const customer = faker.helpers.arrayElement(customers);
      const product = faker.helpers.arrayElement(approvedProducts);

      // Ensure customer is not ordering their own product
      if (customer.id === product.userId) {
        continue;
      }

      const quantity = faker.number.int({ min: 1, max: Math.min(3, product.stock) });
      const unitPrice = parseFloat(product.price);
      const totalAmount = unitPrice * quantity;

      const status = faker.helpers.weightedArrayElement([
        { weight: 15, value: "pending" },
        { weight: 20, value: "confirmed" },
        { weight: 20, value: "processing" },
        { weight: 15, value: "shipped" },
        { weight: 25, value: "delivered" },
        { weight: 5, value: "cancelled" },
      ]);

      const paymentMethod = faker.helpers.arrayElement([
        "cash_on_delivery",
        "credit_card",
        "debit_card",
        "bank_transfer",
        "digital_wallet",
      ]);

      let paymentStatus;
      if (status === "delivered") {
        paymentStatus = "paid";
      } else if (status === "cancelled") {
        paymentStatus = faker.helpers.arrayElement(["refunded", "failed"]);
      } else if (status === "pending") {
        paymentStatus = "pending";
      } else {
        paymentStatus = faker.helpers.weightedArrayElement([
          { weight: 80, value: "paid" },
          { weight: 20, value: "pending" },
        ]);
      }

      const shippingAddress = {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state({ abbreviated: true }),
        zipCode: faker.location.zipCode(),
        country: "USA",
      };

      const createdAt = faker.date.recent({ days: 30 });
      const maxUpdatedAt = new Date(createdAt.getTime() + 10 * 24 * 60 * 60 * 1000);
      const updatedAt = faker.date.between({
        from: createdAt,
        to: maxUpdatedAt > new Date() ? new Date() : maxUpdatedAt,
      });

      orders.push({
        orderNumber: generateOrderNumber(),
        userId: customer.id,
        productId: product.id,
        sellerId: product.userId,
        quantity: quantity,
        unitPrice: unitPrice,
        totalAmount: totalAmount,
        status: status,
        shippingAddress: shippingAddress,
        paymentMethod: paymentMethod,
        paymentStatus: paymentStatus,
        notes: faker.helpers.maybe(() => faker.lorem.sentence(), { probability: 0.3 }),
        trackingNumber:
          status === "shipped" || status === "delivered"
            ? `TRK${faker.string.alphanumeric(9).toUpperCase()}`
            : null,
        estimatedDelivery:
          status === "shipped" || status === "processing" ? faker.date.future({ days: 7 }) : null,
        deliveredAt:
          status === "delivered"
            ? new Date(
                updatedAt.getTime() + faker.number.int({ min: 0, max: 3 * 24 * 60 * 60 * 1000 })
              )
            : null,
        cancelledAt:
          status === "cancelled"
            ? new Date(
                createdAt.getTime() +
                  faker.number.int({
                    min: 0,
                    max: Math.max(1, updatedAt.getTime() - createdAt.getTime()),
                  })
              )
            : null,
        cancellationReason:
          status === "cancelled"
            ? faker.helpers.arrayElement([
                "Customer requested cancellation",
                "Product out of stock",
                "Payment failed",
                "Shipping address invalid",
              ])
            : null,
        createdAt: createdAt,
        updatedAt: updatedAt,
      });
    }

    const createdOrders = await Order.bulkCreate(orders);
    console.log(`✅ Created ${createdOrders.length} orders`);

    // Show summary statistics
    console.log("\n📊 Seeding Summary:");
    console.log(
      `👥 Users: ${createdUsers.length} (1 admin, ${sellers.length} sellers, ${
        customers.length - 1
      } customers)`
    );
    console.log(`📦 Products: ${createdProducts.length} (${approvedProducts.length} approved)`);
    console.log(`🛒 Orders: ${createdOrders.length}`);

    const totalRevenue = orders
      .filter((order) => order.paymentStatus === "paid")
      .reduce((sum, order) => sum + parseFloat(order.totalAmount), 0);

    console.log(`💰 Total revenue: $${totalRevenue.toFixed(2)}`);

    console.log("\n🔑 Login Credentials:");
    console.log("Admin: <EMAIL> / admin123456");
    console.log("Seller: <EMAIL> / seller123456");
    console.log("Customer: <EMAIL> / customer123456");
  } catch (error) {
    console.error("❌ Error during seeding:", error);
  }
};

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedAll()
    .then(() => {
      console.log("🎉 Seeding completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Seeding failed:", error);
      process.exit(1);
    });
}

module.exports = seedAll;
