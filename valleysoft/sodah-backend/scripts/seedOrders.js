/**
 * <AUTHOR> <<EMAIL>>
 */
const { faker } = require("@faker-js/faker");
const { Order, User, Product } = require("../src/models");

// COMMAND TO RUN THIS SCRIPT
// node scripts/seedOrders.js or npm run seed:orders

const seedOrders = async () => {
  try {
    console.log("🚀 Starting order seeding...");

    // Get existing users and products
    const customers = await User.findAll({
      where: { role: ["customer", "admin"] },
      attributes: ["id", "role"],
      limit: 20,
    });

    const products = await Product.findAll({
      where: { status: "approved" },
      attributes: ["id", "price", "userId", "stock", "name"],
      limit: 50,
    });

    if (customers.length === 0) {
      console.log("❌ No customers found. Please create some users first.");
      return;
    }

    if (products.length === 0) {
      console.log("❌ No approved products found. Please create some products first.");
      return;
    }

    console.log(`📊 Found ${customers.length} customers and ${products.length} products`);

    const orders = [];
    const orderNumbers = new Set();

    // Generate unique order numbers
    const generateOrderNumber = () => {
      let orderNumber;
      do {
        const timestamp = Date.now().toString().slice(-8); // Last 8 digits
        const random = Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0");
        orderNumber = `ORD-${timestamp}-${random}`;
      } while (orderNumbers.has(orderNumber));
      orderNumbers.add(orderNumber);
      return orderNumber;
    };

    // Create 100 sample orders
    for (let i = 0; i < 100; i++) {
      const customer = faker.helpers.arrayElement(customers);
      const product = faker.helpers.arrayElement(products);

      // Ensure customer is not ordering their own product
      if (customer.id === product.userId) {
        continue;
      }

      const quantity = faker.number.int({ min: 1, max: 5 });
      const unitPrice = parseFloat(product.price);
      const totalAmount = unitPrice * quantity;

      // Realistic status distribution
      const status = faker.helpers.weightedArrayElement([
        { weight: 20, value: "pending" },
        { weight: 25, value: "confirmed" },
        { weight: 20, value: "processing" },
        { weight: 15, value: "shipped" },
        { weight: 15, value: "delivered" },
        { weight: 5, value: "cancelled" },
      ]);

      const paymentMethod = faker.helpers.arrayElement([
        "cash_on_delivery",
        "credit_card",
        "debit_card",
        "bank_transfer",
        "digital_wallet",
      ]);

      // Set payment status based on order status
      let paymentStatus;
      if (status === "delivered") {
        paymentStatus = "paid";
      } else if (status === "cancelled") {
        paymentStatus = faker.helpers.arrayElement(["refunded", "failed"]);
      } else if (status === "pending") {
        paymentStatus = "pending";
      } else {
        paymentStatus = faker.helpers.weightedArrayElement([
          { weight: 70, value: "paid" },
          { weight: 30, value: "pending" },
        ]);
      }

      // Generate realistic shipping address
      const shippingAddress = {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state({ abbreviated: true }),
        zipCode: faker.location.zipCode(),
        country: "USA",
      };

      // Create realistic timestamps
      const createdAt = faker.date.recent({ days: 60 }); // Last 60 days
      const maxUpdatedAt = new Date(createdAt.getTime() + 14 * 24 * 60 * 60 * 1000); // Up to 14 days after creation
      const updatedAt = faker.date.between({
        from: createdAt,
        to: maxUpdatedAt > new Date() ? new Date() : maxUpdatedAt, // Ensure to date is not in the future
      });

      const order = {
        orderNumber: generateOrderNumber(),
        userId: customer.id,
        productId: product.id,
        sellerId: product.userId,
        quantity: quantity,
        unitPrice: unitPrice,
        totalAmount: totalAmount,
        status: status,
        shippingAddress: shippingAddress,
        paymentMethod: paymentMethod,
        paymentStatus: paymentStatus,
        notes: faker.helpers.maybe(() => faker.lorem.sentence(), { probability: 0.3 }),
        trackingNumber:
          status === "shipped" || status === "delivered"
            ? `TRK${faker.string.alphanumeric(9).toUpperCase()}`
            : null,
        estimatedDelivery:
          status === "shipped" || status === "processing" ? faker.date.future({ days: 7 }) : null,
        deliveredAt:
          status === "delivered"
            ? new Date(
                updatedAt.getTime() + faker.number.int({ min: 0, max: 3 * 24 * 60 * 60 * 1000 })
              ) // 0-3 days after updated
            : null,
        cancelledAt:
          status === "cancelled"
            ? new Date(
                createdAt.getTime() +
                  faker.number.int({
                    min: 0,
                    max: Math.max(1, updatedAt.getTime() - createdAt.getTime()),
                  })
              )
            : null,
        cancellationReason:
          status === "cancelled"
            ? faker.helpers.arrayElement([
                "Customer requested cancellation",
                "Product out of stock",
                "Payment failed",
                "Shipping address invalid",
                "Customer changed mind",
              ])
            : null,
        createdAt: createdAt,
        updatedAt: updatedAt,
      };

      orders.push(order);
    }

    if (orders.length === 0) {
      console.log("❌ No valid orders could be created");
      return;
    }

    // Bulk create orders
    await Order.bulkCreate(orders);

    console.log(`✅ ${orders.length} orders created successfully!`);

    // Show some statistics
    const statusCounts = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {});

    console.log("📈 Order status distribution:");
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`   ${status}: ${count}`);
    });

    const totalRevenue = orders
      .filter((order) => order.paymentStatus === "paid")
      .reduce((sum, order) => sum + parseFloat(order.totalAmount), 0);

    console.log(`💰 Total revenue from paid orders: $${totalRevenue.toFixed(2)}`);
  } catch (error) {
    console.error("❌ Error seeding orders:", error);
  }
};

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedOrders()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Seeding failed:", error);
      process.exit(1);
    });
}

module.exports = seedOrders;
