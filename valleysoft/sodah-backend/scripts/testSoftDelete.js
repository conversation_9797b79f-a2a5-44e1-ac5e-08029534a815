/**
 * <AUTHOR> <<EMAIL>>
 */
const { Product, User } = require("../src/models");
const productService = require("../src/services/productService");

// COMMAND TO RUN THIS SCRIPT
// node scripts/testSoftDelete.js

const testSoftDelete = async () => {
  try {
    console.log("🧪 Testing soft delete functionality...");

    // Get initial count of products
    const initialProducts = await productService.getPaginatedProducts(1, 100);
    console.log(`📊 Initial product count: ${initialProducts.count}`);

    if (initialProducts.count === 0) {
      console.log("❌ No products found. Please seed some products first.");
      return;
    }

    // Get the first product to test with
    const testProduct = initialProducts.rows[0];
    console.log(`🎯 Testing with product: "${testProduct.name}" (ID: ${testProduct.id})`);

    // Soft delete the product
    console.log("🗑️  Soft deleting the product...");
    const deleteResult = await Product.destroy({
      where: { id: testProduct.id }
    });

    if (deleteResult === 0) {
      console.log("❌ Failed to delete product");
      return;
    }

    console.log("✅ Product soft deleted successfully");

    // Check if the product is excluded from paginated results
    console.log("🔍 Checking if product is excluded from paginated results...");
    const afterDeleteProducts = await productService.getPaginatedProducts(1, 100);
    console.log(`📊 Product count after deletion: ${afterDeleteProducts.count}`);

    const deletedProductInResults = afterDeleteProducts.rows.find(p => p.id === testProduct.id);
    
    if (deletedProductInResults) {
      console.log("❌ FAILED: Deleted product still appears in paginated results!");
    } else {
      console.log("✅ SUCCESS: Deleted product is properly excluded from paginated results");
    }

    // Verify the product still exists but is soft deleted
    console.log("🔍 Checking if product exists in database with deletedAt...");
    const softDeletedProduct = await Product.findByPk(testProduct.id, { paranoid: false });
    
    if (!softDeletedProduct) {
      console.log("❌ Product not found even with paranoid: false");
    } else if (softDeletedProduct.deletedAt) {
      console.log("✅ Product exists with deletedAt timestamp:", softDeletedProduct.deletedAt);
    } else {
      console.log("❌ Product exists but deletedAt is null");
    }

    // Test other methods to ensure they also exclude deleted products
    console.log("🔍 Testing other product retrieval methods...");
    
    const allProducts = await productService.getAllProducts();
    const allProductsHasDeleted = allProducts.find(p => p.id === testProduct.id);
    console.log(`📊 getAllProducts excludes deleted: ${!allProductsHasDeleted ? '✅' : '❌'}`);

    const featuredProducts = await productService.getFeaturedProducts();
    const featuredHasDeleted = featuredProducts.find(p => p.id === testProduct.id);
    console.log(`📊 getFeaturedProducts excludes deleted: ${!featuredHasDeleted ? '✅' : '❌'}`);

    // Test getDeletedProducts to ensure it shows the deleted product
    console.log("🔍 Testing getDeletedProducts method...");
    const deletedProducts = await productService.getDeletedProducts(null, "admin");
    const foundInDeleted = deletedProducts.find(p => p.id === testProduct.id);
    console.log(`📊 getDeletedProducts includes deleted: ${foundInDeleted ? '✅' : '❌'}`);

    // Restore the product for cleanup
    console.log("🔄 Restoring product for cleanup...");
    const restoreResult = await Product.restore({
      where: { id: testProduct.id }
    });

    if (restoreResult > 0) {
      console.log("✅ Product restored successfully");
      
      // Verify it's back in paginated results
      const afterRestoreProducts = await productService.getPaginatedProducts(1, 100);
      const restoredProductInResults = afterRestoreProducts.rows.find(p => p.id === testProduct.id);
      console.log(`📊 Restored product back in results: ${restoredProductInResults ? '✅' : '❌'}`);
    } else {
      console.log("❌ Failed to restore product");
    }

    console.log("\n🎉 Soft delete test completed!");

  } catch (error) {
    console.error("❌ Error during soft delete test:", error);
  }
};

// Run the test if this file is executed directly
if (require.main === module) {
  testSoftDelete().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
}

module.exports = testSoftDelete;
