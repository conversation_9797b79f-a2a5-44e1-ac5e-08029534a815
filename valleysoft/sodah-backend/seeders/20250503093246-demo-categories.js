"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if Categories table exists
    try {
      await queryInterface.describeTable("Categories");
    } catch (error) {
      console.log("Categories table does not exist, skipping seed");
      return;
    }

    // Check if there are already categories
    const [categories] = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM Categories"
    );
    if (categories[0].count > 0) {
      console.log("Categories already exist, skipping seed");
      return;
    }

    // Add seed data
    await queryInterface.bulkInsert(
      "Categories",
      [
        {
          name: "Electronics",
          slug: "electronics",
          description: "Electronic devices and gadgets",
          isActive: true,
          displayOrder: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: "Clothing",
          slug: "clothing",
          description: "Apparel and fashion items",
          isActive: true,
          displayOrder: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: "Home & Kitchen",
          slug: "home-kitchen",
          description: "Items for your home and kitchen",
          isActive: true,
          displayOrder: 3,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: "Books",
          slug: "books",
          description: "Books and publications",
          isActive: true,
          displayOrder: 4,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: "Sports & Outdoors",
          slug: "sports-outdoors",
          description: "Sports equipment and outdoor gear",
          isActive: true,
          displayOrder: 5,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      {}
    );

    console.log("Categories seeded successfully");
  },

  async down(queryInterface, Sequelize) {
    // Remove seed data
    await queryInterface.bulkDelete("Categories", null, {});
  },
};
