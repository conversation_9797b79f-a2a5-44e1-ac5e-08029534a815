"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if Orders table exists
    try {
      await queryInterface.describeTable("Orders");
    } catch (error) {
      console.log("Orders table does not exist, skipping seed");
      return;
    }

    // Check if there are already orders
    const [orders] = await queryInterface.sequelize.query("SELECT COUNT(*) as count FROM Orders");
    if (orders[0].count > 0) {
      console.log("Orders already exist, skipping seed");
      return;
    }

    // Get existing users and products to create realistic orders
    const [users] = await queryInterface.sequelize.query(
      "SELECT id, role FROM Users WHERE role IN ('customer', 'admin') LIMIT 10"
    );

    const [products] = await queryInterface.sequelize.query(
      "SELECT id, price, userId, stock FROM Products WHERE status = 'approved' LIMIT 20"
    );

    if (users.length === 0 || products.length === 0) {
      console.log("No users or products found, cannot create orders");
      return;
    }

    const orderStatuses = [
      "pending",
      "confirmed",
      "processing",
      "shipped",
      "delivered",
      "cancelled",
    ];
    const paymentMethods = [
      "cash_on_delivery",
      "credit_card",
      "debit_card",
      "bank_transfer",
      "digital_wallet",
    ];
    const paymentStatuses = ["pending", "paid", "failed", "refunded"];

    const sampleAddresses = [
      {
        street: "123 Main Street",
        city: "New York",
        state: "NY",
        zipCode: "10001",
        country: "USA",
      },
      {
        street: "456 Oak Avenue",
        city: "Los Angeles",
        state: "CA",
        zipCode: "90210",
        country: "USA",
      },
      {
        street: "789 Pine Road",
        city: "Chicago",
        state: "IL",
        zipCode: "60601",
        country: "USA",
      },
      {
        street: "321 Elm Street",
        city: "Houston",
        state: "TX",
        zipCode: "77001",
        country: "USA",
      },
      {
        street: "654 Maple Drive",
        city: "Phoenix",
        state: "AZ",
        zipCode: "85001",
        country: "USA",
      },
    ];

    const ordersToCreate = [];
    const orderNumbers = new Set();

    // Generate unique order numbers
    const generateOrderNumber = () => {
      let orderNumber;
      do {
        const timestamp = Date.now().toString().slice(-8); // Last 8 digits
        const random = Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0");
        orderNumber = `ORD-${timestamp}-${random}`;
      } while (orderNumbers.has(orderNumber));
      orderNumbers.add(orderNumber);
      return orderNumber;
    };

    // Create 50 sample orders
    for (let i = 0; i < 50; i++) {
      const customer = users[Math.floor(Math.random() * users.length)];
      const product = products[Math.floor(Math.random() * products.length)];

      // Ensure customer is not ordering their own product
      if (customer.id === product.userId) {
        continue;
      }

      const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 items
      const unitPrice = parseFloat(product.price);
      const totalAmount = unitPrice * quantity;
      const status = orderStatuses[Math.floor(Math.random() * orderStatuses.length)];
      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];

      // Set payment status based on order status
      let paymentStatus;
      if (status === "delivered") {
        paymentStatus = "paid";
      } else if (status === "cancelled") {
        paymentStatus = Math.random() > 0.5 ? "refunded" : "failed";
      } else if (status === "pending") {
        paymentStatus = "pending";
      } else {
        paymentStatus = Math.random() > 0.3 ? "paid" : "pending";
      }

      const shippingAddress = sampleAddresses[Math.floor(Math.random() * sampleAddresses.length)];

      // Create dates that make sense
      const createdAt = new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)); // Last 30 days
      const updatedAt = new Date(
        createdAt.getTime() + Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)
      ); // Up to 7 days after creation

      const order = {
        orderNumber: generateOrderNumber(),
        userId: customer.id,
        productId: product.id,
        sellerId: product.userId,
        quantity: quantity,
        unitPrice: unitPrice,
        totalAmount: totalAmount,
        status: status,
        shippingAddress: JSON.stringify(shippingAddress),
        paymentMethod: paymentMethod,
        paymentStatus: paymentStatus,
        notes: Math.random() > 0.7 ? "Please handle with care" : null,
        trackingNumber:
          status === "shipped" || status === "delivered"
            ? `TRK${Math.random().toString(36).substr(2, 9).toUpperCase()}`
            : null,
        estimatedDelivery:
          status === "shipped" || status === "processing"
            ? new Date(updatedAt.getTime() + 3 * 24 * 60 * 60 * 1000)
            : null,
        deliveredAt:
          status === "delivered"
            ? new Date(updatedAt.getTime() + Math.floor(Math.random() * 2 * 24 * 60 * 60 * 1000))
            : null,
        cancelledAt:
          status === "cancelled"
            ? new Date(updatedAt.getTime() + Math.floor(Math.random() * 24 * 60 * 60 * 1000))
            : null,
        cancellationReason: status === "cancelled" ? "Customer requested cancellation" : null,
        createdAt: createdAt,
        updatedAt: updatedAt,
      };

      ordersToCreate.push(order);
    }

    if (ordersToCreate.length === 0) {
      console.log("No valid orders could be created");
      return;
    }

    // Insert orders
    await queryInterface.bulkInsert("Orders", ordersToCreate, {});

    console.log(`${ordersToCreate.length} orders seeded successfully`);
  },

  async down(queryInterface, Sequelize) {
    // Remove seed data
    await queryInterface.bulkDelete("Orders", null, {});
  },
};
