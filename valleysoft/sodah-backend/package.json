{"name": "server", "version": "1.0.0", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1", "seed": "node scripts/seedProducts.js", "seed:dev": "NODE_ENV=development node scripts/seedProducts.js", "seed:prod": "NODE_ENV=production node scripts/seedProducts.js", "seed:orders": "node scripts/seedOrders.js", "seed:orders:dev": "NODE_ENV=development node scripts/seedOrders.js", "seed:all": "node scripts/seedAll.js", "seed:all:dev": "NODE_ENV=development node scripts/seedAll.js", "test:soft-delete": "node scripts/testSoftDelete.js", "db:fix-indexes": "node src/scripts/fix-indexes.js", "db:create-migration": "npx sequelize-cli migration:generate --name", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:migrate:undo:all": "npx sequelize-cli db:migrate:undo:all", "db:seed:all": "npx sequelize-cli db:seed:all", "db:seed:undo:all": "npx sequelize-cli db:seed:undo:all", "db:create": "npx sequelize-cli db:create", "db:drop": "npx sequelize-cli db:drop", "db:sync": "ALTER_SYNC=true node src/app.js", "db:sync:force": "FORCE_SYNC=true node src/app.js"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.0", "sequelize": "^6.37.7"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "dotenv": "^16.5.0", "nodemon": "^3.1.9", "sequelize-cli": "^6.6.2"}}