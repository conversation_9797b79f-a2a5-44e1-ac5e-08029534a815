"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("Orders", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
        comment: "Unique identifier for the order",
      },
      orderNumber: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true,
        comment: "Unique order number for tracking",
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "Users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
        comment: "ID of the user who placed the order",
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "Products",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "RESTRICT",
        comment: "ID of the product being ordered",
      },
      sellerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "Users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "RESTRICT",
        comment: "ID of the seller",
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: "Quantity of products ordered",
      },
      unitPrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: "Price per unit at the time of order",
      },
      totalAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: "Total amount for the order",
      },
      status: {
        type: Sequelize.ENUM("pending", "confirmed", "processing", "shipped", "delivered", "cancelled"),
        defaultValue: "pending",
        allowNull: false,
        comment: "Current status of the order",
      },
      shippingAddress: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: "Shipping address information",
      },
      paymentMethod: {
        type: Sequelize.ENUM("cash_on_delivery", "credit_card", "debit_card", "bank_transfer", "digital_wallet"),
        allowNull: false,
        comment: "Payment method used for the order",
      },
      paymentStatus: {
        type: Sequelize.ENUM("pending", "paid", "failed", "refunded"),
        defaultValue: "pending",
        allowNull: false,
        comment: "Payment status of the order",
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Additional notes for the order",
      },
      trackingNumber: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: "Shipping tracking number",
      },
      estimatedDelivery: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Estimated delivery date",
      },
      deliveredAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Actual delivery date",
      },
      cancelledAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "When the order was cancelled",
      },
      cancellationReason: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: "Reason for order cancellation",
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: "When the order was created",
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: "When the order was last updated",
      },
    });

    await queryInterface.addIndex("Orders", ["orderNumber"], {
      unique: true,
      name: "orders_order_number_idx",
    });

    await queryInterface.addIndex("Orders", ["userId"], {
      name: "orders_user_id_idx",
    });

    await queryInterface.addIndex("Orders", ["sellerId"], {
      name: "orders_seller_id_idx",
    });

    await queryInterface.addIndex("Orders", ["status"], {
      name: "orders_status_idx",
    });

    await queryInterface.addIndex("Orders", ["paymentStatus"], {
      name: "orders_payment_status_idx",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("Orders");
  },
};
