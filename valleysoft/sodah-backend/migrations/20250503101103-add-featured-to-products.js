"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if featured column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.featured) {
      // Add featured column if it doesn't exist
      await queryInterface.addColumn("Products", "featured", {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether the product is featured on the homepage or in special sections",
      });

      // Add index for featured column for better performance on queries that filter by featured status
      await queryInterface.addIndex("Products", ["featured"], {
        name: "products_featured_idx",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    try {
      await queryInterface.removeIndex("Products", "products_featured_idx");
    } catch (error) {
      console.log("Index products_featured_idx might not exist, continuing...");
    }

    // Remove featured column
    return queryInterface.removeColumn("Products", "featured");
  },
};
