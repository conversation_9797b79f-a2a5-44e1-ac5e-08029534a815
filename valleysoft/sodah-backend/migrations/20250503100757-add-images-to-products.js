"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if images column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.images) {
      // Add images column if it doesn't exist
      await queryInterface.addColumn("Products", "images", {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: "[]",
        comment: "Array of image URLs for the product",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove images column
    return queryInterface.removeColumn("Products", "images");
  },
};
