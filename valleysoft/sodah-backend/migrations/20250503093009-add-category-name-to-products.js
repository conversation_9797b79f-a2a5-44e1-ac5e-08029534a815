"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if categoryName column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.categoryName) {
      // Add categoryName column if it doesn't exist
      await queryInterface.addColumn("Products", "categoryName", {
        type: Sequelize.STRING(50),
        allowNull: true,
        defaultValue: "uncategorized",
        comment: "Legacy category name (for backward compatibility)",
      });

      // If category column exists, migrate data from category to categoryName
      if (tableInfo.category) {
        await queryInterface.sequelize.query(`
          UPDATE Products
          SET categoryName = category
          WHERE category IS NOT NULL AND categoryName IS NULL
        `);

        // Remove the old category column
        await queryInterface.removeColumn("Products", "category");
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Add back the category column
    await queryInterface.addColumn("Products", "category", {
      type: Sequelize.STRING(50),
      allowNull: true,
      defaultValue: "uncategorized",
    });

    // Copy data from categoryName to category
    await queryInterface.sequelize.query(`
      UPDATE Products
      SET category = categoryName
      WHERE categoryName IS NOT NULL AND category IS NULL
    `);

    // Remove categoryName column
    return queryInterface.removeColumn("Products", "categoryName");
  },
};
