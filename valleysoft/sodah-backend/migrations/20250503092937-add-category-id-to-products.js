"use strict";
/**
 * <AUTHOR> <<EMAIL>>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if Categories table exists, create it if not
    const tables = await queryInterface.showAllTables();
    if (!tables.includes("Categories")) {
      await queryInterface.createTable("Categories", {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false,
        },
        name: {
          type: Sequelize.STRING(50),
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        },
      });
    }

    // Check if categoryId column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.categoryId) {
      // Add categoryId column if it doesn't exist
      await queryInterface.addColumn("Products", "categoryId", {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: "Categories",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "SET NULL",
        comment: "ID of the product category",
      });

      // Add index for categoryId
      await queryInterface.addIndex("Products", ["categoryId"], {
        name: "products_category_id_idx",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    try {
      await queryInterface.removeIndex("Products", "products_category_id_idx");
    } catch (error) {
      console.log("Index products_category_id_idx might not exist, continuing...");
    }

    // Remove column
    return queryInterface.removeColumn("Products", "categoryId");
  },
};
