"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if slug column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.slug) {
      // Add slug column if it doesn't exist
      await queryInterface.addColumn("Products", "slug", {
        type: Sequelize.STRING(120),
        allowNull: true,
        comment: "URL-friendly version of the product name",
      });

      // Generate slugs for existing products
      const [products] = await queryInterface.sequelize.query(
        "SELECT id, name FROM Products WHERE slug IS NULL"
      );

      for (const product of products) {
        // Generate slug from name
        let slug = product.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/(^-|-$)/g, "")
          .substring(0, 100);

        // Add a timestamp to ensure uniqueness
        slug = `${slug}-${Date.now().toString().substring(7)}`;

        // Update product with slug
        await queryInterface.sequelize.query("UPDATE Products SET slug = ? WHERE id = ?", {
          replacements: [slug, product.id],
        });
      }

      // Add unique constraint to slug column
      await queryInterface.addIndex("Products", ["slug"], {
        unique: true,
        name: "products_slug_idx",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    try {
      await queryInterface.removeIndex("Products", "products_slug_idx");
    } catch (error) {
      console.log("Index products_slug_idx might not exist, continuing...");
    }

    // Remove slug column
    return queryInterface.removeColumn("Products", "slug");
  },
};
