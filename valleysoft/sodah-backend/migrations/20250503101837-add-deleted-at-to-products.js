"use strict";
/**
 * <AUTHOR> <<EMAIL>>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if deletedAt column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.deletedAt) {
      // Add deletedAt column if it doesn't exist
      await queryInterface.addColumn("Products", "deletedAt", {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "When the product was soft-deleted",
      });

      // Add index for deletedAt column for better performance on queries that filter by deletion status
      await queryInterface.addIndex("Products", ["deletedAt"], {
        name: "products_deleted_at_idx",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    try {
      await queryInterface.removeIndex("Products", "products_deleted_at_idx");
    } catch (error) {
      console.log("Index products_deleted_at_idx might not exist, continuing...");
    }

    // Remove deletedAt column
    return queryInterface.removeColumn("Products", "deletedAt");
  },
};
