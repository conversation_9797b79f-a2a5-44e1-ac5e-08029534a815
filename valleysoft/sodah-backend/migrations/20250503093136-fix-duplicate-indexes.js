"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get all indexes on the Users table
    const [indexes] = await queryInterface.sequelize.query(
      'SHOW KEYS FROM Users WHERE Key_name != "PRIMARY"'
    );

    // Group indexes by column name
    const indexesByColumn = {};
    indexes.forEach((index) => {
      const column = index.Column_name;
      const keyName = index.Key_name;

      if (!indexesByColumn[column]) {
        indexesByColumn[column] = [];
      }

      indexesByColumn[column].push(keyName);
    });

    // For each column, keep only one index and drop the rest
    for (const column in indexesByColumn) {
      const keyNames = indexesByColumn[column];

      // Keep the first index and drop the rest
      if (keyNames.length > 1) {
        const keepIndex = keyNames[0];
        const dropIndexes = keyNames.slice(1);

        console.log(
          `Column ${column}: Keeping index ${keepIndex}, dropping ${dropIndexes.length} duplicate indexes`
        );

        // Drop duplicate indexes
        for (const keyName of dropIndexes) {
          try {
            await queryInterface.sequelize.query(`ALTER TABLE Users DROP INDEX ${keyName}`);
            console.log(`Dropped index ${keyName}`);
          } catch (error) {
            console.log(`Error dropping index ${keyName}: ${error.message}`);
          }
        }
      }
    }

    // Do the same for Products table
    const [productIndexes] = await queryInterface.sequelize.query(
      'SHOW KEYS FROM Products WHERE Key_name != "PRIMARY"'
    );

    // Group indexes by column name
    const productIndexesByColumn = {};
    productIndexes.forEach((index) => {
      const column = index.Column_name;
      const keyName = index.Key_name;

      if (!productIndexesByColumn[column]) {
        productIndexesByColumn[column] = [];
      }

      productIndexesByColumn[column].push(keyName);
    });

    // For each column, keep only one index and drop the rest
    for (const column in productIndexesByColumn) {
      const keyNames = productIndexesByColumn[column];

      // Keep the first index and drop the rest
      if (keyNames.length > 1) {
        const keepIndex = keyNames[0];
        const dropIndexes = keyNames.slice(1);

        console.log(
          `Column ${column}: Keeping index ${keepIndex}, dropping ${dropIndexes.length} duplicate indexes`
        );

        // Drop duplicate indexes
        for (const keyName of dropIndexes) {
          try {
            await queryInterface.sequelize.query(`ALTER TABLE Products DROP INDEX ${keyName}`);
            console.log(`Dropped index ${keyName}`);
          } catch (error) {
            console.log(`Error dropping index ${keyName}: ${error.message}`);
          }
        }
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // This migration cannot be undone
    console.log("This migration cannot be undone");
  },
};
