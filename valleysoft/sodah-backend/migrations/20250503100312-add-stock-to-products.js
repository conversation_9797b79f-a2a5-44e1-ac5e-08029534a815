"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if stock column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.stock) {
      // Add stock column if it doesn't exist
      await queryInterface.addColumn("Products", "stock", {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "Available quantity of the product",
      });

      // Add index for stock column for better performance on queries that filter by stock
      await queryInterface.addIndex("Products", ["stock"], {
        name: "products_stock_idx",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    try {
      await queryInterface.removeIndex("Products", "products_stock_idx");
    } catch (error) {
      console.log("Index products_stock_idx might not exist, continuing...");
    }

    // Remove stock column
    return queryInterface.removeColumn("Products", "stock");
  },
};
