"use strict";
/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Find users with empty usernames
    const [users] = await queryInterface.sequelize.query(
      'SELECT id FROM Users WHERE userName = "" OR userName IS NULL'
    );

    // Update each user with a unique username
    for (const user of users) {
      await queryInterface.sequelize.query("UPDATE Users SET userName = ? WHERE id = ?", {
        replacements: [`user_${user.id}_${Date.now()}`, user.id],
      });
    }

    // Add NOT NULL constraint to userName column
    await queryInterface.changeColumn("Users", "userName", {
      type: Sequelize.STRING(30),
      allowNull: false,
    });

    // Add unique index to userName column if it doesn't exist
    try {
      await queryInterface.addIndex("Users", ["userName"], {
        unique: true,
        name: "users_username_idx",
      });
    } catch (error) {
      console.log("Index users_username_idx might already exist, continuing...");
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove NOT NULL constraint from userName column
    await queryInterface.changeColumn("Users", "userName", {
      type: Sequelize.STRING(30),
      allowNull: true,
    });

    // Remove unique index
    try {
      await queryInterface.removeIndex("Users", "users_username_idx");
    } catch (error) {
      console.log("Index users_username_idx might not exist, continuing...");
    }
  },
};
