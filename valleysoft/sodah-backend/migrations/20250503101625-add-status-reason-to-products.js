"use strict";
/**
 * <AUTHOR> <<EMAIL>>
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if statusReason column already exists
    const tableInfo = await queryInterface.describeTable("Products");

    if (!tableInfo.statusReason) {
      // Add statusReason column if it doesn't exist
      await queryInterface.addColumn("Products", "statusReason", {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: "Reason for status change (especially for rejections)",
      });
    }

    // Check if statusUpdatedAt column already exists
    if (!tableInfo.statusUpdatedAt) {
      // Add statusUpdatedAt column if it doesn't exist
      await queryInterface.addColumn("Products", "statusUpdatedAt", {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "When the status was last updated",
      });
    }

    // Check if statusUpdatedBy column already exists
    if (!tableInfo.statusUpdatedBy) {
      // Add statusUpdatedBy column if it doesn't exist
      await queryInterface.addColumn("Products", "statusUpdatedBy", {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: "Users",
          key: "id",
        },
        comment: "ID of the admin who updated the status",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove columns in reverse order
    await queryInterface.removeColumn("Products", "statusUpdatedBy");
    await queryInterface.removeColumn("Products", "statusUpdatedAt");
    return queryInterface.removeColumn("Products", "statusReason");
  },
};
