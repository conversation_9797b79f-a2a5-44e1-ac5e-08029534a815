/**
 * <AUTHOR> <<EMAIL>>
 */
const jwt = require("jsonwebtoken");
const User = require("../models/user");
const jwtConfig = require("../config/jwtConfig");
const { AppError } = require("../errors/AppError");
const crypto = require("crypto");

/**
 * Authentication Service
 * Handles business logic for authentication operations
 */
const authService = {
  /**
   * Authenticate user and generate access token
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Object} Authentication result with token and user info
   */
  login: async (email, password) => {
    const normalizedEmail = email.toLowerCase().trim();

    // Use the withPassword scope to include the password field
    const user = await User.scope("withPassword").findOne({
      where: { email: normalizedEmail },
    });

    if (!user) {
      throw new AppError("Invalid credentials", 401);
    }

    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      throw new AppError("Invalid credentials", 401);
    }

    const accessToken = authService.generateAccessToken(user);

    // const refreshToken = authService.generateRefreshToken(user);

    return {
      token: accessToken,
      // refreshToken,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    };
  },

  /**
   * Generate JWT access token
   * @param {Object} user - User object
   * @returns {string} JWT token
   */
  generateAccessToken: (user) => {
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    return jwt.sign(payload, jwtConfig.secret, {
      // expiresIn: jwtConfig.expiresIn || "1h", // uncomment in production
      issuer: jwtConfig.issuer,
    });
  },

  /**
   * Generate refresh token with longer expiry
   * @param {Object} user - User object
   * @returns {string} Refresh token
   */
  generateRefreshToken: (user) => {
    const payload = {
      id: user.id,
      type: "refresh",
    };

    return jwt.sign(payload, jwtConfig.refreshSecret || jwtConfig.secret, {
      expiresIn: "7d",
      issuer: jwtConfig.issuer,
    });
  },

  /**
   * Verify JWT token
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  verifyToken: (token) => {
    try {
      return jwt.verify(token, jwtConfig.secret);
    } catch (error) {
      throw new AppError("Invalid or expired token", 401);
    }
  },

  /**
   * Refresh access token using refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Object} New access token
   */
  refreshAccessToken: async (refreshToken) => {
    try {
      const decoded = jwt.verify(refreshToken, jwtConfig.refreshSecret || jwtConfig.secret);

      if (decoded.type !== "refresh") {
        throw new AppError("Invalid token type", 401);
      }

      const user = await User.findByPk(decoded.id);
      if (!user) {
        throw new AppError("User not found", 404);
      }

      const accessToken = authService.generateAccessToken(user);

      return { token: accessToken };
    } catch (error) {
      throw new AppError("Invalid refresh token", 401);
    }
  },

  /**
   * Generate password reset token
   * @param {string} email - User email
   * @returns {Object} Reset token and expiry
   */
  generatePasswordResetToken: async (email) => {
    const user = await User.findOne({ where: { email: email.toLowerCase().trim() } });
    if (!user) {
      return {
        message: "If your email exists in our system, you will receive a password reset link",
      };
    }

    const resetToken = crypto.randomBytes(32).toString("hex");

    // In a real implementation, you would store the hashed token and expiry
    // const hashedToken = crypto.createHash("sha256").update(resetToken).digest("hex");
    // const tokenExpiry = Date.now() + 3600000;

    return {
      resetToken,
      message: "If your email exists in our system, you will receive a password reset link",
    };
  },

  /**
   * Reset password using token
   * @param {string} token - Reset token
   * @param {string} newPassword - New password
   * @returns {Object} Success message
   */
  resetPassword: async (token, newPassword) => {
    if (!token || !newPassword) {
      throw new AppError("Token and new password are required", 400);
    }

    // In a real implementation, you would find the user with this token
    // const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

    // For demo purposes, this is a placeholder
    const user = null;

    if (!user) {
      throw new AppError("Invalid or expired token", 400);
    }

    if (newPassword.length < 8) {
      throw new AppError("Password must be at least 8 characters long", 400);
    }

    user.password = newPassword;

    return { message: "Password has been reset successfully" };
  },
};

module.exports = authService;
