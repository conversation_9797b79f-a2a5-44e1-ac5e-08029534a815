/**
 * <AUTHOR> <<EMAIL>>
 */
const Order = require("../models/order");
const Product = require("../models/product");
const User = require("../models/user");
const { AppError } = require("../errors/AppError");
const { Op } = require("sequelize");

/**
 * Order Service
 * Handles business logic for order operations
 */
const orderService = {
  /**
   * Create a new order
   * @param {Object} orderData - Order data
   * @param {number} userId - ID of the user placing the order
   * @returns {Object} Created order
   */
  createOrder: async (orderData, userId) => {
    const product = await Product.findByPk(orderData.productId, {
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email"],
        },
      ],
    });

    if (!product) {
      throw new AppError("Product not found", 404);
    }

    if (product.status !== "approved") {
      throw new AppError("Product is not available for purchase", 400);
    }

    if (product.stock < orderData.quantity) {
      throw new AppError(`Insufficient stock. Only ${product.stock} items available`, 400);
    }

    if (product.userId === userId) {
      throw new AppError("You cannot order your own product", 400);
    }

    const totalAmount = parseFloat(product.price) * parseInt(orderData.quantity);

    const order = await Order.create({
      userId: userId,
      productId: orderData.productId,
      sellerId: product.userId,
      quantity: orderData.quantity,
      unitPrice: product.price,
      totalAmount: totalAmount,
      shippingAddress: orderData.shippingAddress,
      paymentMethod: orderData.paymentMethod,
      notes: orderData.notes,
    });

    await product.update({
      stock: product.stock - orderData.quantity,
    });

    return await orderService.getOrderById(order.id);
  },

  /**
   * Get all orders with filtering and pagination
   * @param {Object} options - Query options
   * @returns {Object} Orders with pagination info
   */
  getAllOrders: async (options = {}) => {
    const page = options.page || 1;
    const limit = options.limit || 10;
    const offset = (page - 1) * limit;

    const whereClause = {};
    
    if (options.status) {
      whereClause.status = options.status;
    }
    
    if (options.paymentStatus) {
      whereClause.paymentStatus = options.paymentStatus;
    }
    
    if (options.userId) {
      whereClause.userId = options.userId;
    }
    
    if (options.sellerId) {
      whereClause.sellerId = options.sellerId;
    }

    const { count, rows } = await Order.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: "customer",
          attributes: ["id", "name", "email"],
        },
        {
          model: User,
          as: "seller",
          attributes: ["id", "name", "email"],
        },
        {
          model: Product,
          as: "product",
          attributes: ["id", "name", "price", "images"],
        },
      ],
      limit: limit,
      offset: offset,
      order: [["createdAt", "DESC"]],
      distinct: true,
    });

    return {
      orders: rows,
      pagination: {
        currentPage: page,
        pageSize: limit,
        totalItems: count,
        totalPages: Math.ceil(count / limit),
        hasNextPage: page < Math.ceil(count / limit),
        hasPrevPage: page > 1,
      },
    };
  },

  /**
   * Get orders for a specific user
   * @param {number} userId - User ID
   * @param {Object} options - Query options
   * @returns {Array} User's orders
   */
  getUserOrders: async (userId, options = {}) => {
    const whereClause = { userId };
    
    if (options.status) {
      whereClause.status = options.status;
    }

    return await Order.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: "seller",
          attributes: ["id", "name", "email"],
        },
        {
          model: Product,
          as: "product",
          attributes: ["id", "name", "price", "images"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });
  },

  /**
   * Get orders for a specific seller
   * @param {number} sellerId - Seller ID
   * @param {Object} options - Query options
   * @returns {Array} Seller's orders
   */
  getSellerOrders: async (sellerId, options = {}) => {
    const whereClause = { sellerId };
    
    if (options.status) {
      whereClause.status = options.status;
    }

    return await Order.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: "customer",
          attributes: ["id", "name", "email"],
        },
        {
          model: Product,
          as: "product",
          attributes: ["id", "name", "price", "images"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });
  },

  /**
   * Get order by ID
   * @param {number} id - Order ID
   * @returns {Object} Order object
   */
  getOrderById: async (id) => {
    if (!id) {
      throw new AppError("Order ID is required", 400);
    }

    const order = await Order.findByPk(id, {
      include: [
        {
          model: User,
          as: "customer",
          attributes: ["id", "name", "email", "phone"],
        },
        {
          model: User,
          as: "seller",
          attributes: ["id", "name", "email", "phone"],
        },
        {
          model: Product,
          as: "product",
          attributes: ["id", "name", "description", "price", "images", "categoryName"],
        },
      ],
    });

    if (!order) {
      throw new AppError("Order not found", 404);
    }

    return order;
  },

  /**
   * Update order status
   * @param {number} id - Order ID
   * @param {Object} statusData - Status update data
   * @param {number} updatedBy - ID of user updating the status
   * @returns {Object} Updated order
   */
  updateOrderStatus: async (id, statusData, updatedBy) => {
    const order = await Order.findByPk(id);

    if (!order) {
      throw new AppError("Order not found", 404);
    }

    const updateData = {
      status: statusData.status,
    };

    if (statusData.status === "cancelled") {
      updateData.cancellationReason = statusData.cancellationReason;
      updateData.cancelledAt = new Date();
      
      const product = await Product.findByPk(order.productId);
      if (product) {
        await product.update({
          stock: product.stock + order.quantity,
        });
      }
    }

    if (statusData.status === "shipped") {
      updateData.trackingNumber = statusData.trackingNumber;
    }

    if (statusData.status === "delivered") {
      updateData.deliveredAt = new Date();
    }

    if (statusData.estimatedDelivery) {
      updateData.estimatedDelivery = new Date(statusData.estimatedDelivery);
    }

    await order.update(updateData);

    return await orderService.getOrderById(id);
  },

  /**
   * Update payment status
   * @param {number} id - Order ID
   * @param {Object} paymentData - Payment status data
   * @returns {Object} Updated order
   */
  updatePaymentStatus: async (id, paymentData) => {
    const order = await Order.findByPk(id);

    if (!order) {
      throw new AppError("Order not found", 404);
    }

    await order.update({
      paymentStatus: paymentData.paymentStatus,
    });

    return await orderService.getOrderById(id);
  },

  /**
   * Cancel order
   * @param {number} id - Order ID
   * @param {string} reason - Cancellation reason
   * @param {number} userId - ID of user cancelling the order
   * @returns {Object} Updated order
   */
  cancelOrder: async (id, reason, userId) => {
    const order = await Order.findByPk(id);

    if (!order) {
      throw new AppError("Order not found", 404);
    }

    if (!order.canBeCancelled()) {
      throw new AppError("Order cannot be cancelled in its current status", 400);
    }

    return await orderService.updateOrderStatus(id, {
      status: "cancelled",
      cancellationReason: reason,
    }, userId);
  },

  /**
   * Get order statistics
   * @param {Object} filters - Filter options
   * @returns {Object} Order statistics
   */
  getOrderStatistics: async (filters = {}) => {
    const whereClause = {};
    
    if (filters.sellerId) {
      whereClause.sellerId = filters.sellerId;
    }
    
    if (filters.userId) {
      whereClause.userId = filters.userId;
    }

    const totalOrders = await Order.count({ where: whereClause });
    
    const statusCounts = await Order.findAll({
      where: whereClause,
      attributes: [
        "status",
        [Order.sequelize.fn("COUNT", Order.sequelize.col("id")), "count"],
      ],
      group: ["status"],
    });

    const paymentStatusCounts = await Order.findAll({
      where: whereClause,
      attributes: [
        "paymentStatus",
        [Order.sequelize.fn("COUNT", Order.sequelize.col("id")), "count"],
      ],
      group: ["paymentStatus"],
    });

    const totalRevenue = await Order.sum("totalAmount", {
      where: {
        ...whereClause,
        paymentStatus: "paid",
      },
    });

    return {
      totalOrders,
      statusBreakdown: statusCounts.reduce((acc, item) => {
        acc[item.status] = parseInt(item.get("count"));
        return acc;
      }, {}),
      paymentStatusBreakdown: paymentStatusCounts.reduce((acc, item) => {
        acc[item.paymentStatus] = parseInt(item.get("count"));
        return acc;
      }, {}),
      totalRevenue: totalRevenue || 0,
    };
  },
};

module.exports = orderService;
