/**
 * <AUTHOR> <<EMAIL>>
 */
const { Op } = require("sequelize");
const Product = require("../models/product");
const User = require("../models/user");
const { AppError } = require("../errors/AppError");

const productService = {
  createProduct: async (productData, userId) => {
    const product = await Product.create({
      name: productData.name,
      description: productData.description,
      price: productData.price,
      stock: productData.stock || 0,
      images: productData.images || [],
      featured: productData.featured || false,
      categoryName: productData.categoryName || "uncategorized",
      categoryId: productData.categoryId, // Optional category ID
      status: productData.status, // Set by controller
      userId: userId,
    });

    return product;
  },

  /**
   * Get all products (excludes soft-deleted products)
   */
  getAllProducts: async () => {
    return await Product.findAll({
      include: [
        {
          model: User,
          attributes: ["id", "name", "email"],
          as: "user",
        },
      ],
      order: [["createdAt", "DESC"]],
    });
  },

  /**
   * Get products by user (excludes soft-deleted products)
   */
  getUserProducts: async (userId) => {
    return await Product.findAll({
      where: { userId },
      order: [["createdAt", "DESC"]],
    });
  },

  getProductById: async (id) => {
    try {
      if (!id) {
        throw new Error("Product ID is required");
      }

      const product = await Product.findByPk(id, {
        include: [
          {
            model: User,
            as: "user",
            attributes: ["id", "name", "email"],
            required: false,
          },
          {
            model: require("../models/category"),
            as: "productCategory",
            attributes: ["id", "name", "slug"],
            required: false,
          },
        ],
      });

      if (!product) {
        console.warn(`Product with ID ${id} not found`);
      }

      return product;
    } catch (error) {
      console.error("Error fetching product by ID:", error.message);
      throw error;
    }
  },

  /**
   * Get paginated products with advanced filtering and sorting
   * Note: Automatically excludes soft-deleted products due to paranoid mode
   * @param {number} page - Page number
   * @param {number} pageSize - Number of items per page
   * @param {Object} filters - Filter criteria
   * @param {string} sortBy - Field to sort by
   * @param {string} sortOrder - Sort direction (ASC or DESC)
   * @returns {Object} Object with count and rows properties
   */
  getPaginatedProducts: async (
    page = 1,
    pageSize = 10,
    filters = {},
    sortBy = "createdAt",
    sortOrder = "DESC"
  ) => {
    const offset = (page - 1) * pageSize;

    const validSortFields = ["id", "name", "price", "createdAt", "updatedAt", "status"];
    const validSortOrders = ["ASC", "DESC"];

    if (!validSortFields.includes(sortBy)) {
      sortBy = "createdAt";
    }

    if (!validSortOrders.includes(sortOrder)) {
      sortOrder = "DESC";
    }

    const whereClause = { ...filters };

    if (filters.search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${filters.search}%` } },
        { description: { [Op.like]: `%${filters.search}%` } },
      ];
      delete whereClause.search; // Remove search from where clause
    }

    return await Product.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email"],
        },
      ],
      attributes: [
        "id",
        "name",
        "description",
        "price",
        "stock",
        "images",
        "featured",
        "status",
        "categoryName",
        "createdAt",
        "updatedAt",
      ],
      limit: pageSize,
      offset: offset,
      order: [[sortBy, sortOrder]],
      distinct: true, // Ensure correct count with associations
    });
  },

  updateProduct: async (id, updateData) => {
    const [updated] = await Product.update(updateData, {
      where: { id },
      returning: true,
    });

    if (updated === 0) {
      return null;
    }

    return await Product.findByPk(id);
  },

  deleteProduct: async (id) => {
    const deleted = await Product.destroy({
      where: { id },
    });
    return deleted !== 0;
  },

  /**
   * Soft delete multiple products
   * @param {Array} productIds - Array of product IDs to delete
   * @param {number} userId - ID of the user performing the deletion
   * @param {string} userRole - Role of the user performing the deletion
   * @returns {Object} Deletion result with success and failure counts
   */
  deleteMultipleProducts: async (productIds, userId, userRole) => {
    const results = {
      success: [],
      failed: [],
      totalRequested: productIds.length,
      successCount: 0,
      failedCount: 0,
    };

    for (const productId of productIds) {
      try {
        // Get product to check ownership/permissions
        const product = await Product.findByPk(productId);

        if (!product) {
          results.failed.push({
            id: productId,
            reason: "Product not found",
          });
          continue;
        }

        // Check authorization
        if (userRole !== "admin" && product.userId !== userId) {
          results.failed.push({
            id: productId,
            reason: "Not authorized to delete this product",
          });
          continue;
        }

        // Perform soft delete
        const deleted = await Product.destroy({
          where: { id: productId },
        });

        if (deleted > 0) {
          results.success.push({
            id: productId,
            name: product.name,
          });
        } else {
          results.failed.push({
            id: productId,
            reason: "Failed to delete product",
          });
        }
      } catch (error) {
        results.failed.push({
          id: productId,
          reason: error.message || "Unknown error occurred",
        });
      }
    }

    results.successCount = results.success.length;
    results.failedCount = results.failed.length;

    return results;
  },

  /**
   * Restore soft deleted product
   * @param {number} id - Product ID to restore
   * @returns {boolean} Whether the product was restored
   */
  restoreProduct: async (id) => {
    const restored = await Product.restore({
      where: { id },
    });
    return restored !== 0;
  },

  /**
   * Restore multiple soft deleted products
   * @param {Array} productIds - Array of product IDs to restore
   * @param {number} userId - ID of the user performing the restoration
   * @param {string} userRole - Role of the user performing the restoration
   * @returns {Object} Restoration result with success and failure counts
   */
  restoreMultipleProducts: async (productIds, userId, userRole) => {
    const results = {
      success: [],
      failed: [],
      totalRequested: productIds.length,
      successCount: 0,
      failedCount: 0,
    };

    for (const productId of productIds) {
      try {
        // Get product including soft deleted ones to check ownership/permissions
        const product = await Product.findByPk(productId, { paranoid: false });

        if (!product) {
          results.failed.push({
            id: productId,
            reason: "Product not found",
          });
          continue;
        }

        if (!product.deletedAt) {
          results.failed.push({
            id: productId,
            reason: "Product is not deleted",
          });
          continue;
        }

        // Check authorization
        if (userRole !== "admin" && product.userId !== userId) {
          results.failed.push({
            id: productId,
            reason: "Not authorized to restore this product",
          });
          continue;
        }

        // Perform restore
        const restored = await Product.restore({
          where: { id: productId },
        });

        if (restored > 0) {
          results.success.push({
            id: productId,
            name: product.name,
          });
        } else {
          results.failed.push({
            id: productId,
            reason: "Failed to restore product",
          });
        }
      } catch (error) {
        results.failed.push({
          id: productId,
          reason: error.message || "Unknown error occurred",
        });
      }
    }

    results.successCount = results.success.length;
    results.failedCount = results.failed.length;

    return results;
  },

  /**
   * Get soft deleted products
   * @param {number} userId - User ID (optional, for filtering by user)
   * @param {string} userRole - User role
   * @returns {Array} Soft deleted products
   */
  getDeletedProducts: async (userId, userRole) => {
    const whereClause = {};

    // If not admin, only show user's own deleted products
    if (userRole !== "admin") {
      whereClause.userId = userId;
    }

    return await Product.findAll({
      where: whereClause,
      paranoid: false, // Include soft deleted records
      include: [
        {
          model: User,
          attributes: ["id", "name", "email"],
          as: "user",
        },
      ],
      order: [["deletedAt", "DESC"]],
    }).then((products) => products.filter((product) => product.deletedAt !== null));
  },

  /**
   * Update product status (admin only)
   * @param {string} productId - Product ID
   * @param {string} status - New status
   * @param {string} adminId - Admin user ID
   * @param {string} reason - Reason for status change (optional)
   * @returns {Object} Updated product
   */
  updateProductStatus: async (productId, status, adminId, reason = null) => {
    const updateData = {
      status,
      statusUpdatedAt: new Date(),
      statusUpdatedBy: adminId,
    };

    if (reason) {
      updateData.statusReason = reason;
    }

    const [updated] = await Product.update(updateData, {
      where: { id: productId },
      returning: true,
    });

    if (updated === 0) {
      return null;
    }

    return await Product.findByPk(productId, {
      include: [
        {
          model: User,
          attributes: ["id", "name", "email"],
          as: "user",
        },
      ],
    });
  },

  /**
   * Get products by status
   * @param {string} status - Product status to filter by
   * @returns {Array} Products with the specified status
   */
  getProductsByStatus: async (status) => {
    return await Product.findAll({
      where: { status },
      include: [
        {
          model: User,
          attributes: ["id", "name", "email"],
          as: "user",
        },
      ],
      attributes: [
        "id",
        "name",
        "description",
        "price",
        "stock",
        "images",
        "featured",
        "status",
        "categoryName",
        "statusReason",
        "statusUpdatedAt",
        "statusUpdatedBy",
        "createdAt",
        "updatedAt",
      ],
      order: [["statusUpdatedAt", "DESC"]],
    });
  },

  /**
   * Get filtered products with sorting
   * @param {Object} filters - Filter criteria
   * @param {string} sortBy - Field to sort by
   * @param {string} sortOrder - Sort direction (ASC or DESC)
   * @returns {Array} Filtered and sorted products
   */
  getFilteredProducts: async (filters = {}, sortBy = "createdAt", sortOrder = "DESC") => {
    const validSortFields = ["id", "name", "price", "createdAt", "updatedAt", "status"];
    const validSortOrders = ["ASC", "DESC"];

    if (!validSortFields.includes(sortBy)) {
      sortBy = "createdAt";
    }

    if (!validSortOrders.includes(sortOrder)) {
      sortOrder = "DESC";
    }

    return await Product.findAll({
      where: filters,
      include: [
        {
          model: User,
          attributes: ["id", "name", "email"],
          as: "user",
        },
      ],
      order: [[sortBy, sortOrder]],
    });
  },

  /**
   * Search products by keyword
   * @param {string} query - Search query
   * @returns {Array} Matching products
   */
  searchProducts: async (query) => {
    return await Product.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${query}%` } },
          { description: { [Op.like]: `%${query}%` } },
        ],
        status: "approved", // Only return approved products in search
      },
      include: [
        {
          model: User,
          attributes: ["id", "name"],
          as: "user",
        },
      ],
      order: [["createdAt", "DESC"]],
    });
  },

  getProductsByUser: async (userId) => {
    return await Product.findAll({
      where: { userId },
      order: [["createdAt", "DESC"]],
    });
  },

  countProductsByStatus: async () => {
    const results = await Product.findAll({
      attributes: ["status", [sequelize.fn("COUNT", sequelize.col("id")), "count"]],
      group: ["status"],
    });

    return results.reduce(
      (acc, item) => {
        acc[item.status] = item.get("count");
        return acc;
      },
      { pending: 0, approved: 0, rejected: 0 }
    );
  },

  /**
   * Get featured products
   * @param {number} limit - Maximum number of products to return
   * @returns {Array} Featured products
   */
  getFeaturedProducts: async (limit = 10) => {
    return await Product.findAll({
      where: {
        featured: true,
        status: "approved", // Only return approved products
      },
      include: [
        {
          model: User,
          attributes: ["id", "name"],
          as: "user",
        },
      ],
      attributes: [
        "id",
        "name",
        "description",
        "price",
        "stock",
        "images",
        "featured",
        "categoryName",
        "createdAt",
      ],
      limit: limit,
      order: [["createdAt", "DESC"]],
    });
  },
};

module.exports = productService;
