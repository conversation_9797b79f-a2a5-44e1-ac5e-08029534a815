/**
 * <AUTHOR> <<EMAIL>>
 */
const User = require("../models/user");
const { Op } = require("sequelize");
const { AppError } = require("../errors/AppError");

/**
 * User Service
 * Handles business logic for user operations
 */
const userService = {
  /**
   * Get all users with pagination and filtering
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Search term for name or email
   * @param {string} options.role - Filter by role
   * @param {string} options.sortBy - Field to sort by
   * @param {string} options.sortOrder - Sort direction (ASC or DESC)
   * @returns {Object} Paginated users with count
   */
  getAllUsers: async (options = {}) => {
    const page = options.page || 1;
    const limit = options.limit || 10;
    const offset = (page - 1) * limit;


    const whereClause = {};


    if (options.search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${options.search}%` } },
        { email: { [Op.like]: `%${options.search}%` } },
        { userName: { [Op.like]: `%${options.search}%` } },
      ];
    }


    if (options.role && ["admin", "seller", "customer"].includes(options.role)) {
      whereClause.role = options.role;
    }


    const validSortFields = ["id", "name", "email", "role", "createdAt", "updatedAt"];
    const validSortOrders = ["ASC", "DESC"];

    let sortBy = options.sortBy || "createdAt";
    let sortOrder = options.sortOrder || "DESC";


    if (!validSortFields.includes(sortBy)) {
      sortBy = "createdAt";
    }


    if (!validSortOrders.includes(sortOrder)) {
      sortOrder = "DESC";
    }


    const { count, rows: users } = await User.findAndCountAll({
      where: whereClause,
      attributes: { exclude: ["password"] }, // Never return passwords
      limit,
      offset,
      order: [[sortBy, sortOrder]],
      distinct: true,
    });


    const totalPages = Math.ceil(count / limit);

    return {
      users,
      pagination: {
        total: count,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  },

  /**
   * Get user by ID
   * @param {number} id - User ID
   * @returns {Object} User object
   * @throws {AppError} If user not found
   */
  getUserById: async (id) => {
    if (!id) {
      throw new AppError("User ID is required", 400);
    }

    const user = await User.findByPk(id, {
      attributes: { exclude: ["password"] }, // Never return password
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    return user;
  },

  /**
   * Get user by email
   * @param {string} email - User email
   * @returns {Object} User object
   */
  getUserByEmail: async (email) => {
    if (!email) {
      throw new AppError("Email is required", 400);
    }

    return await User.findOne({
      where: { email: email.toLowerCase().trim() },
      attributes: { exclude: ["password"] }, // Never return password
    });
  },

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Object} Created user
   * @throws {AppError} If email already exists
   */
  createUser: async (userData) => {

    const existingUser = await User.findOne({
      where: { email: userData.email.toLowerCase().trim() },
    });

    if (existingUser) {
      throw new AppError("Email already in use", 409);
    }


    const existingUsername = await User.findOne({
      where: { userName: userData.userName },
    });

    if (existingUsername) {
      throw new AppError("Username already in use", 409);
    }


    const newUser = await User.create({
      name: userData.name,
      userName: userData.userName,
      email: userData.email.toLowerCase().trim(),
      password: userData.password,
      role: userData.role || "customer",
    });


    const userWithoutPassword = newUser.toJSON();
    delete userWithoutPassword.password;

    return userWithoutPassword;
  },

  /**
   * Update user
   * @param {number} id - User ID
   * @param {Object} userData - User data to update
   * @returns {Object} Updated user
   * @throws {AppError} If user not found or email already exists
   */
  updateUser: async (id, userData) => {

    const user = await User.findByPk(id);
    if (!user) {
      throw new AppError("User not found", 404);
    }


    if (userData.email && userData.email !== user.email) {
      const existingUser = await User.findOne({
        where: {
          email: userData.email.toLowerCase().trim(),
          id: { [Op.ne]: id }, // Not this user
        },
      });

      if (existingUser) {
        throw new AppError("Email already in use", 409);
      }


      userData.email = userData.email.toLowerCase().trim();
    }


    if (userData.userName && userData.userName !== user.userName) {
      const existingUsername = await User.findOne({
        where: {
          userName: userData.userName,
          id: { [Op.ne]: id }, // Not this user
        },
      });

      if (existingUsername) {
        throw new AppError("Username already in use", 409);
      }
    }


    await user.update(userData);


    const updatedUser = await User.findByPk(id, {
      attributes: { exclude: ["password"] },
    });

    return updatedUser;
  },

  /**
   * Delete user
   * @param {number} id - User ID
   * @returns {boolean} Success status
   * @throws {AppError} If user not found
   */
  deleteUser: async (id) => {

    const user = await User.findByPk(id);
    if (!user) {
      throw new AppError("User not found", 404);
    }


    await User.destroy({
      where: { id },
    });

    return true;
  },

  /**
   * Change user password
   * @param {number} id - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {boolean} Success status
   * @throws {AppError} If user not found or current password is incorrect
   */
  changePassword: async (id, currentPassword, newPassword) => {

    const user = await User.findByPk(id);
    if (!user) {
      throw new AppError("User not found", 404);
    }


    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      throw new AppError("Current password is incorrect", 401);
    }


    user.password = newPassword;
    await user.save();

    return true;
  },

  /**
   * Get user profile
   * @param {number} id - User ID
   * @returns {Object} User profile
   * @throws {AppError} If user not found
   */
  getUserProfile: async (id) => {
    const user = await User.findByPk(id, {
      attributes: { exclude: ["password"] },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    return user;
  },
};

module.exports = userService;
