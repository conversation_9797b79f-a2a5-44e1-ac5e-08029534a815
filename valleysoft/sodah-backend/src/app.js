/**
 * <AUTHOR> <<EMAIL>>
 */
const express = require("express");
const bodyParser = require("body-parser");
const cors = require("cors");
const sequelize = require("./config/database");
const authRoutes = require("./routes/authRoutes");
const productRoutes = require("./routes/productRoutes");
const userRoutes = require("./routes/userRoutes");
const orderRoutes = require("./routes/orderRoutes");
const errorHandler = require("./middlewares/errorMiddleware");

const app = express();

app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

sequelize
  .authenticate()
  .then(() => {
    const models = require("./models");
    models.setup();

    console.log("Database connection established. Using migrations for schema management.");

    if (process.env.NODE_ENV !== "production" && process.env.USE_SYNC === "true") {
      if (process.env.FORCE_SYNC === "true") {
        console.log("WARNING: Forcing database sync. This will drop all tables!");
        return sequelize.sync({ force: true });
      } else if (process.env.ALTER_SYNC === "true") {
        console.log("Altering database tables to match models...");
        return sequelize.sync({ alter: true });
      } else {
        console.log("Checking database tables...");
        return sequelize.sync({ alter: false });
      }
    }

    return Promise.resolve();
  })
  .then(() => {
    console.log("Database synchronized successfully.");
  })
  .catch((err) => {
    console.error("Unable to connect to the database:", err);
  });

app.use("/api/auth", authRoutes);
app.use("/api/products", productRoutes);
app.use("/api/users", userRoutes);
app.use("/api/orders", orderRoutes);

app.use(errorHandler);

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
