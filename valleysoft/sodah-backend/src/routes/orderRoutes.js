/**
 * <AUTHOR> <<EMAIL>>
 */
const express = require("express");
const router = express.Router();
const orderController = require("../controllers/orderController");
const authMiddleware = require("../middlewares/authMiddleware");
const roleMiddleware = require("../middlewares/roleMiddleware");

router.use(authMiddleware.authenticate);

router.post("/", roleMiddleware.requireRole(["customer"]), orderController.createOrder);

router.get("/", orderController.getOrders);

router.get("/seller", roleMiddleware.requireRole(["seller", "admin"]), orderController.getSellerOrders);

router.get("/statistics", orderController.getOrderStatistics);

router.get("/:id", orderController.getOrderById);

router.put("/:id/status", roleMiddleware.requireRole(["seller", "admin"]), orderController.updateOrderStatus);

router.put("/:id/payment", roleMiddleware.requireRole(["admin"]), orderController.updatePaymentStatus);

router.put("/:id/cancel", orderController.cancelOrder);

module.exports = router;
