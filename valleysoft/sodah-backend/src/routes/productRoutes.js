/**
 * <AUTHOR> <<EMAIL>>
 */
const express = require("express");
const router = express.Router();
const productController = require("../controllers/productController");
const authMiddleware = require("../middlewares/authMiddleware");
const roleMiddleware = require("../middlewares/roleMiddleware");

router.use(authMiddleware.authenticate);

router.post("/", roleMiddleware.requireRole(["admin", "seller"]), productController.createProduct);

router.get("/", productController.getProducts);
router.get("/paginated", productController.getPaginatedProducts);
router.get("/featured", productController.getFeaturedProducts);
router.get("/:id", productController.getProductById);

router.put("/:id", productController.updateProduct);
router.delete("/:id", productController.deleteProduct);

// Bulk delete operations
router.delete("/", productController.deleteMultipleProducts);

// Soft delete management
router.get("/deleted/list", productController.getDeletedProducts);
router.put("/:id/restore", productController.restoreProduct);
router.put("/restore/multiple", productController.restoreMultipleProducts);

router.put(
  "/:id/status",
  roleMiddleware.requireRole(["admin"]),
  productController.updateProductStatus
);

router.get(
  "/status/:status",
  roleMiddleware.requireRole(["admin"]),
  productController.getProductsByStatus
);

module.exports = router;
