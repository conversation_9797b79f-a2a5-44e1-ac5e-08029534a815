/**
 * Migration: Add Slug to Products
 * Created: 2024-06-01
 */
/**
 * <AUTHOR> <<EMAIL>>
 */

const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {

    await queryInterface.addColumn('Products', 'slug', {
      type: DataTypes.STRING(120),
      allowNull: true,
      comment: 'URL-friendly version of the product name'
    });
    

    const [products] = await queryInterface.sequelize.query(
      'SELECT id, name FROM Products'
    );
    
    for (const product of products) {

      let slug = product.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
        .substring(0, 100);
      

      slug = `${slug}-${Date.now().toString().substring(7)}`;
      

      await queryInterface.sequelize.query(
        'UPDATE Products SET slug = ? WHERE id = ?',
        {
          replacements: [slug, product.id]
        }
      );
    }
    

    await queryInterface.addIndex('Products', ['slug'], {
      unique: true,
      name: 'products_slug_idx'
    });
  },

  down: async (queryInterface, Sequelize) => {

    await queryInterface.removeColumn('Products', 'slug');
  }
};
