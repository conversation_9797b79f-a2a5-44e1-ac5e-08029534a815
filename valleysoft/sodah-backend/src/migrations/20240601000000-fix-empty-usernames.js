/**
 * Migration: Fix Empty Usernames
 * Created: 2024-06-01
 */
/**
 * <AUTHOR> <<EMAIL>>
 */

const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const [users] = await queryInterface.sequelize.query(
      'SELECT id FROM Users WHERE userName = "" OR userName IS NULL'
    );
    

    for (const user of users) {
      await queryInterface.sequelize.query(
        'UPDATE Users SET userName = ? WHERE id = ?',
        {
          replacements: [`user_${user.id}_${Date.now()}`, user.id]
        }
      );
    }
    

    await queryInterface.changeColumn('Users', 'userName', {
      type: DataTypes.STRING(30),
      allowNull: false
    });
  },

  down: async (queryInterface, Sequelize) => {

    await queryInterface.changeColumn('Users', 'userName', {
      type: DataTypes.STRING(30),
      allowNull: true
    });
  }
};
