/**
 * <AUTHOR> <<EMAIL>>
 */
const authService = require("../services/authService");

const authMiddleware = {
  authenticate: (req, res, next) => {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      return res.status(401).json({ message: "user not authenticated" });
    }

    try {
      const decoded = authService.verifyToken(token);
      req.user = decoded;
      next();
    } catch (error) {
      return res.status(403).json({ message: "Invalid or expired token" });
    }
  },
};

module.exports = authMiddleware;
