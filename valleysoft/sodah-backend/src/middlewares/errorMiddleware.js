/**
 * <AUTHOR> <<EMAIL>>
 */


const { AppError } = require("../errors/AppError");

const errorHandler = (err, req, res, next) => {
  if (err instanceof AppError) {
    return res.status(err.statusCode).json({
      message: err.message,
    });
  }

  console.error("Unexpected error:", err);

  const message = err.message || "Something went wrong!";
  const statusCode = err.statusCode || 500;

  res.status(statusCode).json({
    message,
  });
};

module.exports = errorHandler;
