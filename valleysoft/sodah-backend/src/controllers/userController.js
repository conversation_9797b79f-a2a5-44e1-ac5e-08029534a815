/**
 * <AUTHOR> <<EMAIL>>
 */
const userService = require("../services/userService");
const { AppError } = require("../errors/AppError");
const { validateUser } = require("../validators/userValidator");

/**
 * User Controller
 * Handles HTTP requests related to users
 */
const userController = {
  /**
   * Get all users with pagination and filtering
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getAllUsers: async (req, res, next) => {
    try {

      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        search: req.query.search || "",
        role: req.query.role,
        sortBy: req.query.sortBy || "createdAt",
        sortOrder: req.query.sortOrder || "DESC",
      };


      if (options.page < 1) {
        throw new AppError("Page number must be at least 1", 400);
      }

      if (options.limit < 1 || options.limit > 100) {
        throw new AppError("Limit must be between 1 and 100", 400);
      }


      const result = await userService.getAllUsers(options);


      res.json({
        success: true,
        data: result.users,
        pagination: result.pagination,
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching users", 500));
      }
    }
  },

  /**
   * Get user by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getUserById: async (req, res, next) => {
    try {
      const userId = req.params.id;


      const user = await userService.getUserById(userId);


      res.json({
        success: true,
        data: user,
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching user", 500));
      }
    }
  },

  /**
   * Create a new user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  createUser: async (req, res, next) => {
    try {

      const validation = validateUser(req.body);
      if (!validation.isValid) {
        throw new AppError("Validation error: " + validation.errors.join(", "), 400);
      }


      const newUser = await userService.createUser(req.body);


      res.status(201).json({
        success: true,
        data: newUser,
        message: "User created successfully",
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error creating user", 500));
      }
    }
  },

  /**
   * Update user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateUser: async (req, res, next) => {
    try {
      const userId = req.params.id;


      if (req.user && req.user.id !== parseInt(userId) && req.user.role !== "admin") {
        throw new AppError("You can only update your own profile", 403);
      }


      if (req.body.role && (!req.user || req.user.role !== "admin")) {
        throw new AppError("Only administrators can update roles", 403);
      }


      if (Object.keys(req.body).length === 0) {
        throw new AppError("No update data provided", 400);
      }


      if (req.body.name || req.body.email || req.body.userName) {

        const currentUser = await userService.getUserById(userId);

        const dataToValidate = {
          name: req.body.name || currentUser.name,
          email: req.body.email || currentUser.email,
          userName: req.body.userName || currentUser.userName,

          ...(req.body.password && { password: req.body.password }),
        };

        const validation = validateUser(dataToValidate);
        if (!validation.isValid) {
          throw new AppError("Validation error: " + validation.errors.join(", "), 400);
        }
      }


      const updatedUser = await userService.updateUser(userId, req.body);


      res.json({
        success: true,
        data: updatedUser,
        message: "User updated successfully",
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error updating user", 500));
      }
    }
  },

  /**
   * Delete user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  deleteUser: async (req, res, next) => {
    try {
      const userId = req.params.id;


      if (req.user && req.user.id !== parseInt(userId) && req.user.role !== "admin") {
        throw new AppError("You can only delete your own account", 403);
      }


      await userService.deleteUser(userId);


      res.json({
        success: true,
        message: "User deleted successfully",
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error deleting user", 500));
      }
    }
  },

  /**
   * Get current user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getProfile: async (req, res, next) => {
    try {

      const userId = req.user.id;


      const userProfile = await userService.getUserProfile(userId);


      res.json({
        success: true,
        data: userProfile,
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching profile", 500));
      }
    }
  },

  /**
   * Update current user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateProfile: async (req, res, next) => {
    try {

      const userId = req.user.id;


      if (req.body.role) {
        throw new AppError("Role cannot be updated through profile endpoint", 403);
      }


      if (Object.keys(req.body).length === 0) {
        throw new AppError("No update data provided", 400);
      }


      const currentUser = await userService.getUserById(userId);

      const dataToValidate = {
        name: req.body.name || currentUser.name,
        email: req.body.email || currentUser.email,
        userName: req.body.userName || currentUser.userName,
      };

      const validation = validateUser(dataToValidate);
      if (!validation.isValid) {
        throw new AppError("Validation error: " + validation.errors.join(", "), 400);
      }


      const updatedProfile = await userService.updateUser(userId, req.body);


      res.json({
        success: true,
        data: updatedProfile,
        message: "Profile updated successfully",
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error updating profile", 500));
      }
    }
  },

  /**
   * Change user password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  changePassword: async (req, res, next) => {
    try {

      const userId = req.user.id;


      const { currentPassword, newPassword } = req.body;


      if (!currentPassword || !newPassword) {
        throw new AppError("Current password and new password are required", 400);
      }


      if (newPassword.length < 8) {
        throw new AppError("New password must be at least 8 characters long", 400);
      }

      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
        throw new AppError(
          "New password must contain at least one uppercase letter, one lowercase letter, and one number",
          400
        );
      }


      await userService.changePassword(userId, currentPassword, newPassword);


      res.json({
        success: true,
        message: "Password changed successfully",
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error changing password", 500));
      }
    }
  },
};

module.exports = userController;
