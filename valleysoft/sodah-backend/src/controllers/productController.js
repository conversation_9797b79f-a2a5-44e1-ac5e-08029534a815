/**
 * <AUTHOR> <<EMAIL>>
 */
const productService = require("../services/productService");
const { AppError } = require("../errors/AppError");
const { validateProduct, validateProductIds } = require("../validators/productValidator");
const Product = require("../models/product");

/**
 * Product Controller
 * Handles all product-related operations including CRUD and status management
 */
const productController = {
  /**
   * Create a new product
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  createProduct: async (req, res, next) => {
    try {
      const validation = validateProduct(req.body);
      if (!validation.isValid) {
        throw new AppError("Validation error: " + validation.errors.join(", "), 400);
      }

      const productData = {
        ...req.body,
        status: req.user.role === "admin" ? "approved" : "pending",
      };

      if (!productData.categoryName) {
        productData.categoryName = "uncategorized";
      }

      const product = await productService.createProduct(productData, req.user.id);

      res.status(201).json({
        success: true,
        data: {
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          stock: product.stock || 0,
          category: product.categoryName, // Use categoryName instead of category
          images: product.images,
          status: product.status,
          createdAt: product.createdAt,
        },
        message: "Product created successfully",
      });
    } catch (error) {
      if (error.name === "SequelizeValidationError") {
        const validationErrors = error.errors.map((err) => err.message);
        next(new AppError("Validation error: " + validationErrors.join(", "), 400));
      } else if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error creating product", 500));
      }
    }
  },

  /**
   * Get all products (admin sees all, others see only their own)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getProducts: async (req, res, next) => {
    try {
      const { category, status, sortBy = "createdAt", sortOrder = "DESC" } = req.query;

      const filters = {};
      if (category) filters.categoryName = category;
      if (status) filters.status = status;

      if (req.user.role !== "admin") {
        filters.userId = req.user.id;
      }

      // const products = await productService.getFilteredProducts(filters, sortBy, sortOrder);
      let products;
      if (req.user.role === "admin") {
        products = await productService.getAllProducts();
      } else {
        products = await productService.getUserProducts(req.user.id);
      }

      res.json({
        success: true,
        count: products.length,
        data: products.map((product) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          category: product.categoryName,
          status: product.status,
          createdAt: product.createdAt,
          ...(product.user && { seller: { id: product.user.id, name: product.user.name } }),
        })),
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching products", 500));
      }
    }
  },

  /**
   * Get single product by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getProductById: async (req, res, next) => {
    try {
      const productId = req.params.id;

      if (!productId) {
        throw new AppError("Product ID is required", 400);
      }

      const product = await productService.getProductById(productId);

      if (!product) {
        throw new AppError("Product not found", 404);
      }

      if (product.userId !== req.user.id && req.user.role !== "admin") {
        throw new AppError("Not authorized to view this product", 403);
      }

      res.json({
        success: true,
        data: {
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          stock: product.stock || 0,
          featured: product.featured || false,
          category: product.categoryName,
          productCategory: product.productCategory
            ? {
                id: product.productCategory.id,
                name: product.productCategory.name,
                slug: product.productCategory.slug,
              }
            : null,
          images: product.images || [],
          status: product.status,
          seller: {
            id: product.user.id,
            name: product.user.name,
            email: product.user.email,
          },
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching product", 500));
      }
    }
  },

  /**
   * Get paginated products with filtering options
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getPaginatedProducts: async (req, res, next) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;

      if (page < 1) {
        throw new AppError("Page number must be at least 1", 400);
      }

      if (pageSize < 1 || pageSize > 100) {
        throw new AppError("Page size must be between 1 and 100", 400);
      }

      const filters = {};

      if (req.query.status) {
        if (!["pending", "approved", "rejected"].includes(req.query.status)) {
          throw new AppError("Invalid status value", 400);
        }
        filters.status = req.query.status;
      }

      if (req.query.category) {
        filters.categoryName = req.query.category;
      }

      if (req.query.minPrice) {
        filters.price = { ...filters.price, [Op.gte]: parseFloat(req.query.minPrice) };
      }

      if (req.query.maxPrice) {
        filters.price = { ...filters.price, [Op.lte]: parseFloat(req.query.maxPrice) };
      }

      if (req.user.role !== "admin") {
        filters.userId = req.user.id;
      }

      const sortBy = req.query.sortBy || "createdAt";
      const sortOrder = req.query.sortOrder || "DESC";

      const { count, rows: products } = await productService.getPaginatedProducts(
        page,
        pageSize,
        filters,
        sortBy,
        sortOrder
      );

      const totalPages = Math.ceil(count / pageSize);

      res.json({
        success: true,
        data: products.map((product) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          stock: product.stock || 0,
          images: product.images || [],
          featured: product.featured || false,
          category: product.categoryName,
          status: product.status,
          createdAt: product.createdAt,
          ...(product.user && { seller: { id: product.user.id, name: product.user.name } }),
        })),
        pagination: {
          currentPage: page,
          pageSize,
          totalItems: count,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching paginated products", 500));
      }
    }
  },
  /**
   * Update product details
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateProduct: async (req, res, next) => {
    try {
      const productId = req.params.id;

      if (!productId) {
        throw new AppError("Product ID is required", 400);
      }

      if (Object.keys(req.body).length === 0) {
        throw new AppError("No update data provided", 400);
      }

      const product = await productService.getProductById(productId);

      if (!product) {
        throw new AppError("Product not found", 404);
      }

      if (product.userId !== req.user.id && req.user.role !== "admin") {
        throw new AppError("Not authorized to update this product", 403);
      }

      if (req.body.name || req.body.price || req.body.description) {
        const dataToValidate = {
          name: req.body.name || product.name,
          price: req.body.price || product.price,
          description: req.body.description || product.description,
        };

        const validation = validateProduct(dataToValidate);
        if (!validation.isValid) {
          throw new AppError("Validation error: " + validation.errors.join(", "), 400);
        }
      }

      if (req.body.status && req.user.role !== "admin") {
        delete req.body.status;
      }

      const updatedProduct = await productService.updateProduct(productId, req.body);

      res.json({
        success: true,
        data: {
          id: updatedProduct.id,
          name: updatedProduct.name,
          description: updatedProduct.description,
          price: updatedProduct.price,
          stock: updatedProduct.stock || 0,
          featured: updatedProduct.featured || false,
          category: updatedProduct.categoryName,
          images: updatedProduct.images || [],
          status: updatedProduct.status,
          updatedAt: updatedProduct.updatedAt,
        },
        message: "Product updated successfully",
      });
    } catch (error) {
      if (error.name === "SequelizeValidationError") {
        const validationErrors = error.errors.map((err) => err.message);
        next(new AppError("Validation error: " + validationErrors.join(", "), 400));
      } else if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error updating product", 500));
      }
    }
  },

  /**
   * Delete product
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  deleteProduct: async (req, res, next) => {
    try {
      const productId = req.params.id;

      if (!productId) {
        throw new AppError("Product ID is required", 400);
      }

      const product = await productService.getProductById(productId);

      if (!product) {
        throw new AppError("Product not found", 404);
      }

      if (product.userId !== req.user.id && req.user.role !== "admin") {
        throw new AppError("Not authorized to delete this product", 403);
      }

      const deleted = await productService.deleteProduct(productId);

      if (!deleted) {
        throw new AppError("Failed to delete product", 500);
      }

      res.json({
        success: true,
        message: "Product deleted successfully",
        data: {
          id: productId,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error deleting product", 500));
      }
    }
  },

  /**
   * Soft delete multiple products
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  deleteMultipleProducts: async (req, res, next) => {
    try {
      const { productIds } = req.body;

      const validation = validateProductIds(productIds);
      if (!validation.isValid) {
        return next(new AppError("Validation error: " + validation.errors.join(", "), 400));
      }

      // Convert to integers and remove duplicates
      const uniqueProductIds = [...new Set(productIds.map((id) => parseInt(id)))];

      const results = await productService.deleteMultipleProducts(
        uniqueProductIds,
        req.user.id,
        req.user.role
      );

      const statusCode = results.failedCount > 0 ? 207 : 200; // 207 Multi-Status if some failed

      res.status(statusCode).json({
        success: results.successCount > 0,
        message: `${results.successCount} product(s) deleted successfully${
          results.failedCount > 0 ? `, ${results.failedCount} failed` : ""
        }`,
        data: {
          summary: {
            totalRequested: results.totalRequested,
            successCount: results.successCount,
            failedCount: results.failedCount,
          },
          successful: results.success,
          failed: results.failed,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error deleting products", 500));
      }
    }
  },

  /**
   * Restore soft deleted product
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  restoreProduct: async (req, res, next) => {
    try {
      const productId = req.params.id;

      if (!productId) {
        return next(new AppError("Product ID is required", 400));
      }

      // Check if product exists (including soft deleted)
      const product = await productService.getProductById(productId);
      if (!product) {
        // Try to find in soft deleted products
        const deletedProduct = await Product.findByPk(productId, { paranoid: false });
        if (!deletedProduct) {
          return next(new AppError("Product not found", 404));
        }
        if (!deletedProduct.deletedAt) {
          return next(new AppError("Product is not deleted", 400));
        }

        // Check authorization for soft deleted product
        if (deletedProduct.userId !== req.user.id && req.user.role !== "admin") {
          return next(new AppError("Not authorized to restore this product", 403));
        }
      } else {
        return next(new AppError("Product is not deleted", 400));
      }

      const restored = await productService.restoreProduct(productId);

      if (!restored) {
        return next(new AppError("Failed to restore product", 500));
      }

      res.json({
        success: true,
        message: "Product restored successfully",
        data: {
          id: productId,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error restoring product", 500));
      }
    }
  },

  /**
   * Restore multiple soft deleted products
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  restoreMultipleProducts: async (req, res, next) => {
    try {
      const { productIds } = req.body;

      const validation = validateProductIds(productIds);
      if (!validation.isValid) {
        return next(new AppError("Validation error: " + validation.errors.join(", "), 400));
      }

      // Convert to integers and remove duplicates
      const uniqueProductIds = [...new Set(productIds.map((id) => parseInt(id)))];

      const results = await productService.restoreMultipleProducts(
        uniqueProductIds,
        req.user.id,
        req.user.role
      );

      const statusCode = results.failedCount > 0 ? 207 : 200; // 207 Multi-Status if some failed

      res.status(statusCode).json({
        success: results.successCount > 0,
        message: `${results.successCount} product(s) restored successfully${
          results.failedCount > 0 ? `, ${results.failedCount} failed` : ""
        }`,
        data: {
          summary: {
            totalRequested: results.totalRequested,
            successCount: results.successCount,
            failedCount: results.failedCount,
          },
          successful: results.success,
          failed: results.failed,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error restoring products", 500));
      }
    }
  },

  /**
   * Get soft deleted products
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getDeletedProducts: async (req, res, next) => {
    try {
      const products = await productService.getDeletedProducts(req.user.id, req.user.role);

      res.json({
        success: true,
        count: products.length,
        data: products.map((product) => ({
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          stock: product.stock,
          images: product.images,
          featured: product.featured,
          category: product.categoryName,
          status: product.status,
          seller: product.user
            ? {
                id: product.user.id,
                name: product.user.name,
                email: product.user.email,
              }
            : null,
          deletedAt: product.deletedAt,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        })),
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching deleted products", 500));
      }
    }
  },

  /**
   * Update product status (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateProductStatus: async (req, res, next) => {
    try {
      if (req.user.role !== "admin") {
        throw new AppError("Only administrators can update product status", 403);
      }

      const productId = req.params.id;
      const { status, reason } = req.body;

      if (!["pending", "approved", "rejected"].includes(status)) {
        throw new AppError("Invalid status value. Must be pending, approved, or rejected", 400);
      }

      if (status === "rejected" && !reason) {
        throw new AppError("Reason is required when rejecting a product", 400);
      }

      const product = await productService.getProductById(productId);
      if (!product) {
        throw new AppError("Product not found", 404);
      }

      if (product.status === status) {
        res.json({
          success: true,
          message: `Product is already ${status}`,
          data: {
            id: product.id,
            name: product.name,
            status: product.status,
            statusUpdatedAt: product.statusUpdatedAt,
          },
        });
        return; // Exit early
      }

      const updatedProduct = await productService.updateProductStatus(
        productId,
        status,
        req.user.id,
        reason
      );

      res.json({
        success: true,
        message: `Product status updated to ${status}`,
        data: {
          id: updatedProduct.id,
          name: updatedProduct.name,
          status: updatedProduct.status,
          statusUpdatedAt: updatedProduct.statusUpdatedAt,
          statusUpdatedBy: req.user.id,
          ...(reason && { reason }),
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error updating product status", 500));
      }
    }
  },

  /**
   * Get products by status (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getProductsByStatus: async (req, res, next) => {
    try {
      if (req.user.role !== "admin") {
        throw new AppError("Only administrators can access this endpoint", 403);
      }

      const status = req.params.status;

      if (!["pending", "approved", "rejected"].includes(status)) {
        throw new AppError("Invalid status value. Must be pending, approved, or rejected", 400);
      }

      const products = await productService.getProductsByStatus(status);

      res.json({
        success: true,
        count: products.length,
        data: products.map((product) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          stock: product.stock || 0,
          images: product.images || [],
          featured: product.featured || false,
          category: product.categoryName,
          status: product.status,
          seller: {
            id: product.user.id,
            name: product.user.name,
            email: product.user.email,
          },
          statusReason: product.statusReason,
          statusUpdatedAt: product.statusUpdatedAt,
          createdAt: product.createdAt,
        })),
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching products by status", 500));
      }
    }
  },

  /**
   * Search products
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  searchProducts: async (req, res, next) => {
    try {
      const { query } = req.query;

      if (!query || query.trim().length < 2) {
        throw new AppError("Search query must be at least 2 characters", 400);
      }

      const products = await productService.searchProducts(query);

      res.json({
        success: true,
        count: products.length,
        data: products.map((product) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          stock: product.stock || 0,
          images: product.images || [],
          featured: product.featured || false,
          category: product.categoryName,
          ...(product.user && { seller: { id: product.user.id, name: product.user.name } }),
        })),
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error searching products", 500));
      }
    }
  },

  /**
   * Get featured products
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Object} Featured products
   */
  getFeaturedProducts: async (req, res, next) => {
    try {
      const limit = parseInt(req.query.limit) || 10;

      const products = await productService.getFeaturedProducts(limit);

      res.json({
        success: true,
        count: products.length,
        data: products.map((product) => ({
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          stock: product.stock || 0,
          images: product.images || [],
          featured: true,
          category: product.categoryName,
          ...(product.user && { seller: { id: product.user.id, name: product.user.name } }),
        })),
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching featured products", 500));
      }
    }
  },
};

module.exports = productController;
