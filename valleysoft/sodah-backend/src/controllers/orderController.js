/**
 * <AUTHOR> <<EMAIL>>
 */
const orderService = require("../services/orderService");
const { AppError } = require("../errors/AppError");
const {
  validateOrder,
  validateOrderStatusUpdate,
  validatePaymentStatusUpdate,
} = require("../validators/orderValidator");

/**
 * Order Controller
 * Handles all order-related HTTP requests
 */
const orderController = {
  /**
   * Create a new order
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  createOrder: async (req, res, next) => {
    try {
      const validation = validateOrder(req.body);
      if (!validation.isValid) {
        return next(new AppError("Validation error: " + validation.errors.join(", "), 400));
      }

      const order = await orderService.createOrder(req.body, req.user.id);

      res.status(201).json({
        success: true,
        message: "Order created successfully",
        data: {
          id: order.id,
          orderNumber: order.orderNumber,
          product: {
            id: order.product.id,
            name: order.product.name,
            price: order.product.price,
            images: order.product.images,
          },
          seller: {
            id: order.seller.id,
            name: order.seller.name,
            email: order.seller.email,
          },
          quantity: order.quantity,
          unitPrice: order.unitPrice,
          totalAmount: order.totalAmount,
          status: order.status,
          paymentMethod: order.paymentMethod,
          paymentStatus: order.paymentStatus,
          shippingAddress: order.shippingAddress,
          createdAt: order.createdAt,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error creating order", 500));
      }
    }
  },

  /**
   * Get all orders (admin only) or user's orders
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getOrders: async (req, res, next) => {
    try {
      const { page, pageSize, status, paymentStatus } = req.query;

      let orders;

      if (req.user.role === "admin") {
        const options = {
          page: parseInt(page) || 1,
          limit: parseInt(pageSize) || 10,
          status,
          paymentStatus,
        };

        const result = await orderService.getAllOrders(options);
        orders = result.orders;

        res.json({
          success: true,
          count: orders.length,
          data: orders.map((order) => ({
            id: order.id,
            orderNumber: order.orderNumber,
            customer: {
              id: order.customer.id,
              name: order.customer.name,
              email: order.customer.email,
            },
            seller: {
              id: order.seller.id,
              name: order.seller.name,
              email: order.seller.email,
            },
            product: {
              id: order.product.id,
              name: order.product.name,
              price: order.product.price,
            },
            quantity: order.quantity,
            totalAmount: order.totalAmount,
            status: order.status,
            paymentStatus: order.paymentStatus,
            createdAt: order.createdAt,
          })),
          pagination: result.pagination,
        });
      } else {
        orders = await orderService.getUserOrders(req.user.id, { status });

        res.json({
          success: true,
          count: orders.length,
          data: orders.map((order) => ({
            id: order.id,
            orderNumber: order.orderNumber,
            seller: {
              id: order.seller.id,
              name: order.seller.name,
              email: order.seller.email,
            },
            product: {
              id: order.product.id,
              name: order.product.name,
              price: order.product.price,
              images: order.product.images,
            },
            quantity: order.quantity,
            unitPrice: order.unitPrice,
            totalAmount: order.totalAmount,
            status: order.status,
            paymentStatus: order.paymentStatus,
            trackingNumber: order.trackingNumber,
            estimatedDelivery: order.estimatedDelivery,
            createdAt: order.createdAt,
          })),
        });
      }
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching orders", 500));
      }
    }
  },

  /**
   * Get seller's orders
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getSellerOrders: async (req, res, next) => {
    try {
      const { status } = req.query;

      const orders = await orderService.getSellerOrders(req.user.id, { status });

      res.json({
        success: true,
        count: orders.length,
        data: orders.map((order) => ({
          id: order.id,
          orderNumber: order.orderNumber,
          customer: {
            id: order.customer.id,
            name: order.customer.name,
            email: order.customer.email,
          },
          product: {
            id: order.product.id,
            name: order.product.name,
            price: order.product.price,
            images: order.product.images,
          },
          quantity: order.quantity,
          unitPrice: order.unitPrice,
          totalAmount: order.totalAmount,
          status: order.status,
          paymentStatus: order.paymentStatus,
          shippingAddress: order.shippingAddress,
          trackingNumber: order.trackingNumber,
          estimatedDelivery: order.estimatedDelivery,
          createdAt: order.createdAt,
        })),
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching seller orders", 500));
      }
    }
  },

  /**
   * Get single order by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getOrderById: async (req, res, next) => {
    try {
      const orderId = req.params.id;

      if (!orderId) {
        return next(new AppError("Order ID is required", 400));
      }

      const order = await orderService.getOrderById(orderId);

      if (
        req.user.role !== "admin" &&
        order.userId !== req.user.id &&
        order.sellerId !== req.user.id
      ) {
        return next(new AppError("Not authorized to view this order", 403));
      }

      res.json({
        success: true,
        data: {
          id: order.id,
          orderNumber: order.orderNumber,
          customer: {
            id: order.customer.id,
            name: order.customer.name,
            email: order.customer.email,
            phone: order.customer.phone,
          },
          seller: {
            id: order.seller.id,
            name: order.seller.name,
            email: order.seller.email,
            phone: order.seller.phone,
          },
          product: {
            id: order.product.id,
            name: order.product.name,
            description: order.product.description,
            price: order.product.price,
            images: order.product.images,
            category: order.product.categoryName,
          },
          quantity: order.quantity,
          unitPrice: order.unitPrice,
          totalAmount: order.totalAmount,
          status: order.status,
          paymentMethod: order.paymentMethod,
          paymentStatus: order.paymentStatus,
          shippingAddress: order.shippingAddress,
          notes: order.notes,
          trackingNumber: order.trackingNumber,
          estimatedDelivery: order.estimatedDelivery,
          deliveredAt: order.deliveredAt,
          cancelledAt: order.cancelledAt,
          cancellationReason: order.cancellationReason,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching order", 500));
      }
    }
  },

  /**
   * Update order status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateOrderStatus: async (req, res, next) => {
    try {
      const orderId = req.params.id;

      const validation = validateOrderStatusUpdate(req.body);
      if (!validation.isValid) {
        return next(new AppError("Validation error: " + validation.errors.join(", "), 400));
      }

      const order = await orderService.getOrderById(orderId);

      if (req.user.role !== "admin" && order.sellerId !== req.user.id) {
        return next(new AppError("Not authorized to update this order", 403));
      }

      const updatedOrder = await orderService.updateOrderStatus(orderId, req.body, req.user.id);

      res.json({
        success: true,
        message: "Order status updated successfully",
        data: {
          id: updatedOrder.id,
          orderNumber: updatedOrder.orderNumber,
          status: updatedOrder.status,
          trackingNumber: updatedOrder.trackingNumber,
          estimatedDelivery: updatedOrder.estimatedDelivery,
          deliveredAt: updatedOrder.deliveredAt,
          cancelledAt: updatedOrder.cancelledAt,
          cancellationReason: updatedOrder.cancellationReason,
          updatedAt: updatedOrder.updatedAt,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error updating order status", 500));
      }
    }
  },

  /**
   * Update payment status (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updatePaymentStatus: async (req, res, next) => {
    try {
      const orderId = req.params.id;

      const validation = validatePaymentStatusUpdate(req.body);
      if (!validation.isValid) {
        return next(new AppError("Validation error: " + validation.errors.join(", "), 400));
      }

      const updatedOrder = await orderService.updatePaymentStatus(orderId, req.body);

      res.json({
        success: true,
        message: "Payment status updated successfully",
        data: {
          id: updatedOrder.id,
          orderNumber: updatedOrder.orderNumber,
          paymentStatus: updatedOrder.paymentStatus,
          updatedAt: updatedOrder.updatedAt,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error updating payment status", 500));
      }
    }
  },

  /**
   * Cancel order
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  cancelOrder: async (req, res, next) => {
    try {
      const orderId = req.params.id;
      const { reason } = req.body;

      if (!reason) {
        return next(new AppError("Cancellation reason is required", 400));
      }

      const order = await orderService.getOrderById(orderId);

      if (
        req.user.role !== "admin" &&
        order.userId !== req.user.id &&
        order.sellerId !== req.user.id
      ) {
        return next(new AppError("Not authorized to cancel this order", 403));
      }

      const cancelledOrder = await orderService.cancelOrder(orderId, reason, req.user.id);

      res.json({
        success: true,
        message: "Order cancelled successfully",
        data: {
          id: cancelledOrder.id,
          orderNumber: cancelledOrder.orderNumber,
          status: cancelledOrder.status,
          cancellationReason: cancelledOrder.cancellationReason,
          cancelledAt: cancelledOrder.cancelledAt,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error cancelling order", 500));
      }
    }
  },

  /**
   * Get order statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getOrderStatistics: async (req, res, next) => {
    try {
      const filters = {};

      if (req.user.role === "seller") {
        filters.sellerId = req.user.id;
      } else if (req.user.role === "customer") {
        filters.userId = req.user.id;
      }

      const statistics = await orderService.getOrderStatistics(filters);

      res.json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        next(new AppError(error.message || "Error fetching order statistics", 500));
      }
    }
  },
};

module.exports = orderController;
