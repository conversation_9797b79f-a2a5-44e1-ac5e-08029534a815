/**
 * <AUTHOR> <<EMAIL>>
 */
const authService = require("../services/authService");
const User = require("../models/user");
const { AppError } = require("../errors/AppError");

/**
 * Authentication Controller
 * Handles user authentication operations like login and registration
 */
const authController = {
  /**
   * User login
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  login: async (req, res, next) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        throw new AppError("Email and password are required", 400);
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new AppError("Invalid email format", 400);
      }

      const result = await authService.login(email, password);
      console.log("=========================");

      console.log("after login");

      if (process.env.NODE_ENV === "production") {
        res.cookie("token", result.token, {
          httpOnly: true,
          secure: true,
          sameSite: "strict",
          maxAge: 24 * 60 * 60 * 1000, // 1 day
        });
      }

      res.json(result);
    } catch (error) {
      next(new AppError(error.message || "Authentication failed", error.statusCode || 500));
    }
  },

  /**
   * User registration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  register: async (req, res, next) => {
    try {
      const { name, email, password, role = "customer", userName } = req.body;

      if (!name || !email || !password) {
        throw new AppError("Name, email, and password are required", 400);
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new AppError("Invalid email format", 400);
      }

      if (password.length < 8) {
        throw new AppError("Password must be at least 8 characters long", 400);
      }

      if (role === "admin" && (!req.user || req.user.role !== "admin")) {
        throw new AppError("Only admins can create other admins", 403);
      }

      const user = await User.create({
        name,
        email: email.toLowerCase().trim(),
        password,
        role,
        userName,
      });

      res.status(201).json({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        userName: user.userName,
      });
    } catch (error) {
      if (error.name === "SequelizeUniqueConstraintError") {
        return next(new AppError("Email already exists", 409));
      }
      next(new AppError(error.message || "Failed to register user", error.statusCode || 500));
    }
  },

  /**
   * Logout user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  logout: async (req, res) => {
    res.clearCookie("token");
    res.status(200).json({ message: "Logged out successfully" });
  },

  /**
   * Request password reset
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  requestPasswordReset: async (req, res, next) => {
    try {
      const { email } = req.body;

      if (!email) {
        throw new AppError("Email is required", 400);
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new AppError("Invalid email format", 400);
      }

      const result = await authService.generatePasswordResetToken(email);

      res.status(200).json({
        message: result.message,

        resetToken: process.env.NODE_ENV !== "production" ? result.resetToken : undefined,
      });
    } catch (error) {
      next(
        new AppError(error.message || "Failed to request password reset", error.statusCode || 500)
      );
    }
  },

  /**
   * Reset password with token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  resetPassword: async (req, res, next) => {
    try {
      const { token, newPassword } = req.body;

      if (!token || !newPassword) {
        throw new AppError("Token and new password are required", 400);
      }

      const result = await authService.resetPassword(token, newPassword);

      res.status(200).json(result);
    } catch (error) {
      next(new AppError(error.message || "Failed to reset password", error.statusCode || 500));
    }
  },

  /**
   * Refresh access token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  refreshToken: async (req, res, next) => {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        throw new AppError("Refresh token is required", 400);
      }

      const result = await authService.refreshAccessToken(refreshToken);

      res.json(result);
    } catch (error) {
      next(new AppError(error.message || "Failed to refresh token", error.statusCode || 500));
    }
  },
};

module.exports = authController;
