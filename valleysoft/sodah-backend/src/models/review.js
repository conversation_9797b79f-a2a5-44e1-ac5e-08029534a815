/**
 * <AUTHOR> <<EMAIL>>
 */
const { DataTypes, Model } = require("sequelize");
const sequelize = require("../config/database");

/**
 * Review Model
 * Represents product reviews
 * @class Review
 * @extends Model
 */
class Review extends Model {
  /**
   * Check if review is verified (user purchased the product)
   * @returns {boolean} Whether the review is verified
   */
  isVerified() {
    return this.isVerifiedPurchase;
  }
}

Review.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: "Unique identifier for the review",
    },
    productId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Products",
        key: "id",
      },
      comment: "ID of the product being reviewed",
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Users",
        key: "id",
      },
      comment: "ID of the user who wrote the review",
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: {
          args: [1],
          msg: "Rating must be at least 1",
        },
        max: {
          args: [5],
          msg: "Rating cannot be more than 5",
        },
      },
      comment: "Rating from 1 to 5",
    },
    title: {
      type: DataTypes.STRING(100),
      comment: "Review title",
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Review comment cannot be empty",
        },
        len: {
          args: [10, 1000],
          msg: "Review comment must be between 10 and 1000 characters",
        },
      },
      comment: "Review comment text",
    },
    isVerifiedPurchase: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: "Whether the reviewer purchased the product",
    },
    status: {
      type: DataTypes.ENUM("pending", "approved", "rejected"),
      defaultValue: "pending",
      comment: "Review approval status",
    },
    helpfulCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Number of users who found this review helpful",
    },
    images: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: "Array of image URLs for the review",
      get() {
        const rawValue = this.getDataValue("images");
        return rawValue ? (typeof rawValue === "string" ? JSON.parse(rawValue) : rawValue) : [];
      },
      set(value) {
        this.setDataValue("images", Array.isArray(value) ? value : []);
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the review was created",
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the review was last updated",
    },
    deletedAt: {
      type: DataTypes.DATE,
      comment: "When the review was soft-deleted",
    },
  },
  {
    sequelize,
    modelName: "Review",
    tableName: "Reviews",
    paranoid: true, // Enable soft deletes
    indexes: [

      {
        name: "reviews_product_user_idx",
        fields: ["productId", "userId"],
      },

    ],
  }
);

module.exports = Review;
