/**
 * <AUTHOR> <<EMAIL>>
 */
const { DataTypes, Model } = require("sequelize");
const sequelize = require("../config/database");

/**
 * Order Model
 * Represents orders in the marketplace
 * @class Order
 * @extends Model
 */
class Order extends Model {
  /**
   * Get formatted total with currency symbol
   * @param {string} currencySymbol - Currency symbol to use
   * @returns {string} Formatted total
   */
  getFormattedTotal(currencySymbol = "$") {
    return `${currencySymbol}${parseFloat(this.totalAmount).toFixed(2)}`;
  }

  /**
   * Check if order can be cancelled
   * @returns {boolean} Whether order can be cancelled
   */
  canBeCancelled() {
    return ["pending", "confirmed"].includes(this.status);
  }

  /**
   * Check if order is completed
   * @returns {boolean} Whether order is completed
   */
  isCompleted() {
    return this.status === "delivered";
  }
}

Order.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: "Unique identifier for the order",
    },
    orderNumber: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      comment: "Unique order number for tracking",
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Users",
        key: "id",
      },
      comment: "ID of the user who placed the order",
    },
    productId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Products",
        key: "id",
      },
      comment: "ID of the product being ordered",
    },
    sellerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Users",
        key: "id",
      },
      comment: "ID of the seller",
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        isInt: {
          msg: "Quantity must be a valid integer",
        },
        min: {
          args: [1],
          msg: "Quantity must be at least 1",
        },
      },
      comment: "Quantity of products ordered",
    },
    unitPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: "Unit price must be a valid decimal number",
        },
        min: {
          args: [0.01],
          msg: "Unit price must be greater than zero",
        },
      },
      comment: "Price per unit at the time of order",
    },
    totalAmount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: "Total amount must be a valid decimal number",
        },
        min: {
          args: [0.01],
          msg: "Total amount must be greater than zero",
        },
      },
      comment: "Total amount for the order",
    },
    status: {
      type: DataTypes.ENUM(
        "pending",
        "confirmed",
        "processing",
        "shipped",
        "delivered",
        "cancelled"
      ),
      defaultValue: "pending",
      allowNull: false,
      comment: "Current status of the order",
    },
    shippingAddress: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: "Shipping address information",
      get() {
        const rawValue = this.getDataValue("shippingAddress");
        return rawValue ? (typeof rawValue === "string" ? JSON.parse(rawValue) : rawValue) : null;
      },
      set(value) {
        this.setDataValue("shippingAddress", value);
      },
    },
    paymentMethod: {
      type: DataTypes.ENUM(
        "cash_on_delivery",
        "credit_card",
        "debit_card",
        "bank_transfer",
        "digital_wallet"
      ),
      allowNull: false,
      comment: "Payment method used for the order",
    },
    paymentStatus: {
      type: DataTypes.ENUM("pending", "paid", "failed", "refunded"),
      defaultValue: "pending",
      allowNull: false,
      comment: "Payment status of the order",
    },
    notes: {
      type: DataTypes.TEXT,
      comment: "Additional notes for the order",
    },
    trackingNumber: {
      type: DataTypes.STRING(50),
      comment: "Shipping tracking number",
    },
    estimatedDelivery: {
      type: DataTypes.DATE,
      comment: "Estimated delivery date",
    },
    deliveredAt: {
      type: DataTypes.DATE,
      comment: "Actual delivery date",
    },
    cancelledAt: {
      type: DataTypes.DATE,
      comment: "When the order was cancelled",
    },
    cancellationReason: {
      type: DataTypes.STRING(255),
      comment: "Reason for order cancellation",
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the order was created",
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the order was last updated",
    },
  },
  {
    sequelize,
    modelName: "Order",
    tableName: "Orders",
    indexes: [
      {
        name: "orders_order_number_idx",
        unique: true,
        fields: ["orderNumber"],
      },
      {
        name: "orders_user_id_idx",
        fields: ["userId"],
      },
      {
        name: "orders_seller_id_idx",
        fields: ["sellerId"],
      },
      {
        name: "orders_status_idx",
        fields: ["status"],
      },
      {
        name: "orders_payment_status_idx",
        fields: ["paymentStatus"],
      },
    ],
    hooks: {
      beforeCreate: (order) => {
        if (!order.orderNumber) {
          const timestamp = Date.now().toString().slice(-8); // Last 8 digits
          const random = Math.floor(Math.random() * 1000)
            .toString()
            .padStart(3, "0");
          order.orderNumber = `ORD-${timestamp}-${random}`;
        }

        if (!order.totalAmount && order.unitPrice && order.quantity) {
          order.totalAmount = parseFloat(order.unitPrice) * parseInt(order.quantity);
        }
      },
      beforeUpdate: (order) => {
        if (order.changed("unitPrice") || order.changed("quantity")) {
          order.totalAmount = parseFloat(order.unitPrice) * parseInt(order.quantity);
        }

        if (order.changed("status") && order.status === "delivered" && !order.deliveredAt) {
          order.deliveredAt = new Date();
        }

        if (order.changed("status") && order.status === "cancelled" && !order.cancelledAt) {
          order.cancelledAt = new Date();
        }
      },
    },
  }
);

module.exports = Order;
