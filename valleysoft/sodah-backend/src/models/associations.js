/**
 * Model Associations
 * Defines relationships between models
 */
/**
 * <AUTHOR> <<EMAIL>>
 */

const Product = require("./product");
const User = require("./user");
const Category = require("./category");
const Review = require("./review");
const Order = require("./order");

/**
 * Setup all model associations
 */
const setupAssociations = () => {
  User.hasMany(Product, {
    foreignKey: "userId",
    as: "products",
    onDelete: "CASCADE",
  });

  Product.belongsTo(User, {
    foreignKey: "userId",
    as: "user",
  });

  User.hasMany(Product, {
    foreignKey: "statusUpdatedBy",
    as: "reviewedProducts",
  });

  Product.belongsTo(User, {
    foreignKey: "statusUpdatedBy",
    as: "reviewer",
    constraints: false,
  });

  Category.hasMany(Product, {
    foreignKey: "categoryId",
    as: "products",
  });

  Product.belongsTo(Category, {
    foreignKey: "categoryId",
    as: "productCategory",
  });

  Category.hasMany(Category, {
    as: "children",
    foreignKey: "parentId",
  });

  Category.belongsTo(Category, {
    as: "parent",
    foreignKey: "parentId",
  });

  Product.hasMany(Review, {
    foreignKey: "productId",
    as: "reviews",
  });

  Review.belongsTo(Product, {
    foreignKey: "productId",
    as: "product",
  });

  User.hasMany(Review, {
    foreignKey: "userId",
    as: "userReviews",
  });

  Review.belongsTo(User, {
    foreignKey: "userId",
    as: "user",
  });

  User.hasMany(Order, {
    foreignKey: "userId",
    as: "orders",
    onDelete: "CASCADE",
  });

  Order.belongsTo(User, {
    foreignKey: "userId",
    as: "customer",
  });

  User.hasMany(Order, {
    foreignKey: "sellerId",
    as: "sellerOrders",
    onDelete: "CASCADE",
  });

  Order.belongsTo(User, {
    foreignKey: "sellerId",
    as: "seller",
  });

  Product.hasMany(Order, {
    foreignKey: "productId",
    as: "orders",
  });

  Order.belongsTo(Product, {
    foreignKey: "productId",
    as: "product",
  });

  console.log("Associations set up successfully");
};

module.exports = {
  setupAssociations,
};
