/**
 * <AUTHOR> <<EMAIL>>
 */
const { DataTypes, Model } = require("sequelize");
const sequelize = require("../config/database");

/**
 * Product Model
 * Represents products in the marketplace
 * @class Product
 * @extends Model
 */
class Product extends Model {
  /**
   * Get formatted price with currency symbol
   * @param {string} currencySymbol - Currency symbol to use
   * @returns {string} Formatted price
   */
  getFormattedPrice(currencySymbol = "$") {
    return `${currencySymbol}${parseFloat(this.price).toFixed(2)}`;
  }

  /**
   * Check if product is available for purchase
   * @returns {boolean} Whether product is available
   */
  isAvailable() {
    return this.status === "approved" && this.stock > 0;
  }
}

Product.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: "Unique identifier for the product",
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Product name cannot be empty",
        },
        len: {
          args: [3, 100],
          msg: "Product name must be between 3 and 100 characters",
        },
      },
      comment: "Name of the product",
    },
    slug: {
      type: DataTypes.STRING(120),
      unique: true,
      comment: "URL-friendly version of the product name",
    },
    description: {
      type: DataTypes.TEXT,
      validate: {
        len: {
          args: [0, 2000],
          msg: "Description cannot exceed 2000 characters",
        },
      },
      comment: "Detailed description of the product",
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: "Price must be a valid decimal number",
        },
        min: {
          args: [0.01],
          msg: "Price must be greater than zero",
        },
      },
      comment: "Price of the product",
    },
    stock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        isInt: {
          msg: "Stock must be a valid integer",
        },
        min: {
          args: [0],
          msg: "Stock cannot be negative",
        },
      },
      comment: "Available quantity of the product",
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: true, // Make it optional for backward compatibility
      references: {
        model: "Categories",
        key: "id",
      },
      comment: "ID of the product category",
    },
    categoryName: {
      type: DataTypes.STRING(50),
      defaultValue: "uncategorized",
      comment: "Legacy category name (for backward compatibility)",
    },
    images: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: "Array of image URLs",
      get() {
        const rawValue = this.getDataValue("images");
        return rawValue ? (typeof rawValue === "string" ? JSON.parse(rawValue) : rawValue) : [];
      },
      set(value) {
        this.setDataValue("images", Array.isArray(value) ? value : []);
      },
    },
    featured: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: "Whether the product is featured",
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "Users",
        key: "id",
      },
      comment: "ID of the user who created the product",
    },
    status: {
      type: DataTypes.ENUM("pending", "approved", "rejected"),
      defaultValue: "pending",
      allowNull: false,
      comment: "Approval status of the product",
    },
    statusReason: {
      type: DataTypes.STRING(255),
      comment: "Reason for status change (especially for rejections)",
    },
    statusUpdatedAt: {
      type: DataTypes.DATE,
      comment: "When the status was last updated",
    },
    statusUpdatedBy: {
      type: DataTypes.INTEGER,
      references: {
        model: "Users",
        key: "id",
      },
      comment: "ID of the admin who updated the status",
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the product was created",
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the product was last updated",
    },
    deletedAt: {
      type: DataTypes.DATE,
      comment: "When the product was soft-deleted",
    },
  },
  {
    sequelize,
    modelName: "Product",
    tableName: "Products",
    paranoid: true, // Enable soft deletes
    indexes: [

      {
        name: "products_slug_idx",
        unique: true,
        fields: ["slug"],
      },
      {
        name: "products_status_user_idx",
        fields: ["status", "userId"],
      },

    ],
    hooks: {
      beforeValidate: (product) => {

        if (product.name && (!product.slug || product.changed("name"))) {
          product.slug = product.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, "-")
            .replace(/(^-|-$)/g, "")
            .substring(0, 100);


          product.slug = `${product.slug}-${Date.now().toString().substring(7)}`;
        }
      },
      beforeUpdate: (product) => {

        if (product.changed("status")) {
          product.statusUpdatedAt = new Date();
        }
      },
    },
  }
);

module.exports = Product;
