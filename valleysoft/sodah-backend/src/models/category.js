/**
 * <AUTHOR> <<EMAIL>>
 */
const { DataTypes, Model } = require("sequelize");
const sequelize = require("../config/database");

/**
 * Category Model
 * Represents product categories
 * @class Category
 * @extends Model
 */
class Category extends Model {
  /**
   * Get full category path (including parent categories)
   * @returns {Promise<string>} Full category path
   */
  async getFullPath() {
    let path = this.name;
    let currentCategory = this;

    while (currentCategory.parentId) {
      const parent = await Category.findByPk(currentCategory.parentId);
      if (!parent) break;

      path = `${parent.name} > ${path}`;
      currentCategory = parent;
    }

    return path;
  }
}

Category.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: "Unique identifier for the category",
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Category name cannot be empty",
        },
        len: {
          args: [2, 50],
          msg: "Category name must be between 2 and 50 characters",
        },
      },
      comment: "Name of the category",
    },
    slug: {
      type: DataTypes.STRING(60),
      allowNull: false,
      unique: true,
      comment: "URL-friendly version of the category name",
    },
    description: {
      type: DataTypes.TEXT,
      comment: "Description of the category",
    },
    parentId: {
      type: DataTypes.INTEGER,
      references: {
        model: "Categories",
        key: "id",
      },
      comment: "Parent category ID for hierarchical categories",
    },
    image: {
      type: DataTypes.STRING(255),
      comment: "URL to category image",
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: "Whether the category is active",
    },
    displayOrder: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: "Order for displaying categories",
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the category was created",
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the category was last updated",
    },
    deletedAt: {
      type: DataTypes.DATE,
      comment: "When the category was soft-deleted",
    },
  },
  {
    sequelize,
    modelName: "Category",
    tableName: "Categories",
    paranoid: true, // Enable soft deletes
    indexes: [

      {
        name: "categories_slug_idx",
        unique: true,
        fields: ["slug"],
      },

    ],
    hooks: {
      beforeValidate: (category) => {

        if (category.name && (!category.slug || category.changed("name"))) {
          category.slug = category.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, "-")
            .replace(/(^-|-$)/g, "")
            .substring(0, 50);
        }
      },
    },
  }
);


module.exports = Category;
