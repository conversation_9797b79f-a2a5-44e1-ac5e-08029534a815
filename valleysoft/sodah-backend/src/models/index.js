/**
 * Models Index
 * Exports all models and sets up associations
 */
/**
 * <AUTHOR> <<EMAIL>>
 */

const Product = require("./product");
const User = require("./user");
const Category = require("./category");
const Review = require("./review");
const Order = require("./order");

const { setupAssociations } = require("./associations");

/**
 * Database Models
 * @module Models
 */
module.exports = {
  /**
   * Product model
   * @type {import('./product')}
   */
  Product,

  /**
   * User model
   * @type {import('./user')}
   */
  User,

  /**
   * Category model
   * @type {import('./category')}
   */
  Category,

  /**
   * Review model
   * @type {import('./review')}
   */
  Review,

  /**
   * Order model
   * @type {import('./order')}
   */
  Order,

  /**
   * Setup function to initialize all models and associations
   * @function
   */
  setup: () => {
    setupAssociations();
    console.log("Models initialized successfully");
  },
};
