/**
 * <AUTHOR> <<EMAIL>>
 */
const { DataTypes, Model } = require("sequelize");
const sequelize = require("../config/database");
const bcrypt = require("bcryptjs");
const crypto = require("crypto");

/**
 * User Model
 * Represents users in the system
 * @class User
 * @extends Model
 */
class User extends Model {
  /**
   * Compare a candidate password with the user's password
   * @param {string} candidatePassword - Password to compare
   * @returns {Promise<boolean>} Whether the password matches
   */
  async comparePassword(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
  }

  /**
   * Get user's full name
   * @returns {string} Full name
   */
  getFullName() {
    return `${this.firstName || ""} ${this.lastName || ""}`.trim() || this.name;
  }

  /**
   * Check if user is an admin
   * @returns {boolean} Whether user is an admin
   */
  isAdmin() {
    return this.role === "admin";
  }

  /**
   * Check if user is a seller
   * @returns {boolean} Whether user is a seller
   */
  isSeller() {
    return this.role === "seller";
  }
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: "Unique identifier for the user",
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Name cannot be empty",
        },
        len: {
          args: [2, 100],
          msg: "Name must be between 2 and 100 characters",
        },
      },
      comment: "Full name of the user",
    },
    firstName: {
      type: DataTypes.STRING(50),
      comment: "First name of the user",
    },
    lastName: {
      type: DataTypes.STRING(50),
      comment: "Last name of the user",
    },
    userName: {
      type: DataTypes.STRING(30),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: "Username cannot be empty",
        },
        len: {
          args: [3, 30],
          msg: "Username must be between 3 and 30 characters",
        },
        is: {
          args: /^[a-zA-Z0-9_]+$/,
          msg: "Username can only contain letters, numbers, and underscores",
        },
      },
      comment: "Unique username for the user",
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: {
          msg: "Invalid email format",
        },
        notEmpty: {
          msg: "Email cannot be empty",
        },
      },
      comment: "Email address of the user",
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Password cannot be empty",
        },
        len: {
          args: [8, 100],
          msg: "Password must be at least 8 characters",
        },
      },
      comment: "Hashed password",
    },
    role: {
      type: DataTypes.ENUM("admin", "seller", "customer"),
      defaultValue: "customer",
      allowNull: false,
      comment: "User role for authorization",
    },
    avatar: {
      type: DataTypes.STRING(255),
      comment: "URL to user's profile picture",
    },
    bio: {
      type: DataTypes.TEXT,
      comment: "User's biography or description",
    },
    phone: {
      type: DataTypes.STRING(20),
      validate: {
        is: {
          args: /^[0-9+\-\s()]*$/,
          msg: "Invalid phone number format",
        },
      },
      comment: "User's phone number",
    },
    address: {
      type: DataTypes.JSON,
      comment: "User's address information",
      get() {
        const rawValue = this.getDataValue("address");
        return rawValue ? (typeof rawValue === "string" ? JSON.parse(rawValue) : rawValue) : null;
      },
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: "Whether the user's email is verified",
    },
    verificationToken: {
      type: DataTypes.STRING,
      comment: "Token for email verification",
    },
    resetPasswordToken: {
      type: DataTypes.STRING,
      comment: "Token for password reset",
    },
    resetPasswordExpires: {
      type: DataTypes.DATE,
      comment: "Expiration date for password reset token",
    },
    lastLogin: {
      type: DataTypes.DATE,
      comment: "Last login timestamp",
    },
    status: {
      type: DataTypes.ENUM("active", "inactive", "suspended"),
      defaultValue: "active",
      comment: "User account status",
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the user was created",
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: "When the user was last updated",
    },
    deletedAt: {
      type: DataTypes.DATE,
      comment: "When the user was soft-deleted",
    },
  },
  {
    sequelize,
    modelName: "User",
    tableName: "Users",
    paranoid: true, // Enable soft deletes
    indexes: [
      {
        name: "users_email_idx",
        unique: true,
        fields: ["email"],
      },
      {
        name: "users_username_idx",
        unique: true,
        fields: ["userName"],
      },

      {
        name: "users_role_status_idx",
        fields: ["role", "status"],
      },
    ],
    hooks: {
      beforeSave: async (user) => {
        if (user.changed("password")) {
          const salt = await bcrypt.genSalt(10);
          user.password = await bcrypt.hash(user.password, salt);
        }

        if (user.changed("email")) {
          user.email = user.email.toLowerCase().trim();
        }

        if (user.changed("name") && (!user.firstName || !user.lastName)) {
          const nameParts = user.name.trim().split(/\s+/);
          if (nameParts.length > 0 && !user.firstName) {
            user.firstName = nameParts[0];
          }
          if (nameParts.length > 1 && !user.lastName) {
            user.lastName = nameParts.slice(1).join(" ");
          }
        }
      },
      beforeCreate: (user) => {
        user.verificationToken = crypto.randomBytes(20).toString("hex");
      },
    },
    defaultScope: {
      attributes: { exclude: ["password", "verificationToken", "resetPasswordToken"] },
    },
    scopes: {
      withPassword: {
        attributes: { include: ["password"] },
      },
      active: {
        where: { status: "active" },
      },
    },
  }
);

module.exports = User;
