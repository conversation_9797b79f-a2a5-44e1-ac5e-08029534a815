/**
 * Product Validator
 * Validates product data before processing
 */
/**
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Validates product data
 * @param {Object} productData - The product data to validate
 * @returns {Object} - Validation result with isValid and errors properties
 */
const validateProduct = (productData) => {
  const errors = [];

  if (!productData.name) {
    errors.push("Product name is required");
  } else if (productData.name.length < 3 || productData.name.length > 100) {
    errors.push("Product name must be between 3 and 100 characters");
  }

  if (!productData.price) {
    errors.push("Product price is required");
  } else if (isNaN(parseFloat(productData.price)) || parseFloat(productData.price) <= 0) {
    errors.push("Product price must be a positive number");
  }

  if (productData.description && productData.description.length > 1000) {
    errors.push("Product description cannot exceed 1000 characters");
  }

  if (productData.category && typeof productData.category !== "string") {
    errors.push("Product category must be a string");
  }

  if (productData.images) {
    if (!Array.isArray(productData.images)) {
      errors.push("Product images must be an array");
    } else {
      productData.images.forEach((image, index) => {
        if (typeof image !== "string" || !isValidUrl(image)) {
          errors.push(`Image at index ${index} is not a valid URL`);
        }
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validates if a string is a valid URL
 * @param {string} url - The URL to validate
 * @returns {boolean} - Whether the URL is valid
 */
const isValidUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Validates product IDs for bulk operations
 * @param {Array} productIds - Array of product IDs
 * @returns {Object} - Validation result with isValid and errors properties
 */
const validateProductIds = (productIds) => {
  const errors = [];

  if (!productIds) {
    errors.push("Product IDs are required");
    return { isValid: false, errors };
  }

  if (!Array.isArray(productIds)) {
    errors.push("Product IDs must be an array");
    return { isValid: false, errors };
  }

  if (productIds.length === 0) {
    errors.push("At least one product ID is required");
    return { isValid: false, errors };
  }

  if (productIds.length > 50) {
    errors.push("Cannot process more than 50 products at once");
  }

  // Check for valid integers
  const invalidIds = productIds.filter((id) => {
    const parsed = parseInt(id);
    return !Number.isInteger(parsed) || parsed <= 0;
  });

  if (invalidIds.length > 0) {
    errors.push(`Invalid product IDs: ${invalidIds.join(", ")}`);
  }

  // Check for duplicates
  const uniqueIds = [...new Set(productIds)];
  if (uniqueIds.length !== productIds.length) {
    errors.push("Duplicate product IDs found");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

module.exports = {
  validateProduct,
  validateProductIds,
};
