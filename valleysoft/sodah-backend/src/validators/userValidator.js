/**
 * User Validator
 * Validates user data before processing
 */
/**
 * <AUTHOR> <<EMAIL>>
 */


/**
 * Validates user data
 * @param {Object} userData - The user data to validate
 * @returns {Object} - Validation result with isValid and errors properties
 */
const validateUser = (userData) => {
  const errors = [];


  if (!userData.name) {
    errors.push("Name is required");
  } else if (userData.name.length < 2 || userData.name.length > 50) {
    errors.push("Name must be between 2 and 50 characters");
  }


  if (!userData.userName) {
    errors.push("Username is required");
  } else if (userData.userName.length < 3 || userData.userName.length > 30) {
    errors.push("Username must be between 3 and 30 characters");
  } else if (!/^[a-zA-Z0-9_]+$/.test(userData.userName)) {
    errors.push("Username can only contain letters, numbers, and underscores");
  }


  if (!userData.email) {
    errors.push("Email is required");
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      errors.push("Invalid email format");
    }
  }


  if (userData.password !== undefined) {
    if (!userData.password) {
      errors.push("Password is required");
    } else if (userData.password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(userData.password)) {
      errors.push("Password must contain at least one uppercase letter, one lowercase letter, and one number");
    }
  }


  if (userData.role && !["admin", "seller", "customer"].includes(userData.role)) {
    errors.push("Role must be admin, seller, or customer");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = {
  validateUser
};
