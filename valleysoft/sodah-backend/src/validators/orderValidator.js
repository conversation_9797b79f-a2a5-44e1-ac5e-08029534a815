/**
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Order Validator
 * Validates order data before processing
 */

/**
 * Validates order data
 * @param {Object} orderData - The order data to validate
 * @returns {Object} - Validation result with isValid and errors properties
 */
const validateOrder = (orderData) => {
  const errors = [];

  if (!orderData.productId) {
    errors.push("Product ID is required");
  } else if (!Number.isInteger(parseInt(orderData.productId)) || parseInt(orderData.productId) <= 0) {
    errors.push("Product ID must be a positive integer");
  }

  if (!orderData.quantity) {
    errors.push("Quantity is required");
  } else if (!Number.isInteger(parseInt(orderData.quantity)) || parseInt(orderData.quantity) <= 0) {
    errors.push("Quantity must be a positive integer");
  }

  if (!orderData.shippingAddress) {
    errors.push("Shipping address is required");
  } else if (typeof orderData.shippingAddress !== 'object') {
    errors.push("Shipping address must be an object");
  } else {
    const address = orderData.shippingAddress;
    
    if (!address.street) {
      errors.push("Street address is required");
    } else if (typeof address.street !== 'string' || address.street.trim().length < 5) {
      errors.push("Street address must be at least 5 characters long");
    }
    
    if (!address.city) {
      errors.push("City is required");
    } else if (typeof address.city !== 'string' || address.city.trim().length < 2) {
      errors.push("City must be at least 2 characters long");
    }
    
    if (!address.state) {
      errors.push("State is required");
    } else if (typeof address.state !== 'string' || address.state.trim().length < 2) {
      errors.push("State must be at least 2 characters long");
    }
    
    if (!address.zipCode) {
      errors.push("Zip code is required");
    } else if (typeof address.zipCode !== 'string' || !/^[0-9]{5,10}$/.test(address.zipCode)) {
      errors.push("Zip code must be 5-10 digits");
    }
    
    if (!address.country) {
      errors.push("Country is required");
    } else if (typeof address.country !== 'string' || address.country.trim().length < 2) {
      errors.push("Country must be at least 2 characters long");
    }
  }

  if (!orderData.paymentMethod) {
    errors.push("Payment method is required");
  } else if (!["cash_on_delivery", "credit_card", "debit_card", "bank_transfer", "digital_wallet"].includes(orderData.paymentMethod)) {
    errors.push("Invalid payment method");
  }

  if (orderData.notes && typeof orderData.notes !== 'string') {
    errors.push("Notes must be a string");
  } else if (orderData.notes && orderData.notes.length > 500) {
    errors.push("Notes cannot exceed 500 characters");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validates order status update data
 * @param {Object} statusData - The status update data to validate
 * @returns {Object} - Validation result with isValid and errors properties
 */
const validateOrderStatusUpdate = (statusData) => {
  const errors = [];
  const validStatuses = ["pending", "confirmed", "processing", "shipped", "delivered", "cancelled"];

  if (!statusData.status) {
    errors.push("Status is required");
  } else if (!validStatuses.includes(statusData.status)) {
    errors.push("Invalid status. Must be one of: " + validStatuses.join(", "));
  }

  if (statusData.status === "cancelled" && !statusData.cancellationReason) {
    errors.push("Cancellation reason is required when cancelling an order");
  }

  if (statusData.status === "shipped" && !statusData.trackingNumber) {
    errors.push("Tracking number is required when marking order as shipped");
  }

  if (statusData.cancellationReason && typeof statusData.cancellationReason !== 'string') {
    errors.push("Cancellation reason must be a string");
  } else if (statusData.cancellationReason && statusData.cancellationReason.length > 255) {
    errors.push("Cancellation reason cannot exceed 255 characters");
  }

  if (statusData.trackingNumber && typeof statusData.trackingNumber !== 'string') {
    errors.push("Tracking number must be a string");
  } else if (statusData.trackingNumber && statusData.trackingNumber.length > 50) {
    errors.push("Tracking number cannot exceed 50 characters");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validates payment status update data
 * @param {Object} paymentData - The payment status update data to validate
 * @returns {Object} - Validation result with isValid and errors properties
 */
const validatePaymentStatusUpdate = (paymentData) => {
  const errors = [];
  const validPaymentStatuses = ["pending", "paid", "failed", "refunded"];

  if (!paymentData.paymentStatus) {
    errors.push("Payment status is required");
  } else if (!validPaymentStatuses.includes(paymentData.paymentStatus)) {
    errors.push("Invalid payment status. Must be one of: " + validPaymentStatuses.join(", "));
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = {
  validateOrder,
  validateOrderStatusUpdate,
  validatePaymentStatusUpdate
};
