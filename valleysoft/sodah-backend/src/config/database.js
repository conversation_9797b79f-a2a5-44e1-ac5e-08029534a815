/**
 * <AUTHOR> <irfand<PERSON>@gmail.com>
 */

const { Sequelize } = require("sequelize");
require("dotenv").config();

const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 3306,
  dialect: "mysql",
  logging: (msg) => console.log(`[DB] ${msg}`),
  retry: {
    max: 5,
    timeout: 60000,
    match: [/ECONNREFUSED/, /ETIMEDOUT/],
    backoffBase: 1000,
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
});


sequelize
  .authenticate()
  .then(() => console.log("Database connection established"))
  .catch((err) => {
    console.error("Database connection failed:", err);

  });

module.exports = sequelize;
