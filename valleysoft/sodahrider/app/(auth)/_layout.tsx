import { images } from "@/constants";
import useAuthStore from "@/store/auth.store";
import { Redirect, Slot } from "expo-router";
import { Dimensions, Image, ImageBackground, ScrollView, View } from "react-native";

export default function AuthLayout() {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  if (isAuthenticated) return <Redirect href="/" />;

  return (
    // <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"}>
    <ScrollView
      className="bg-white "
      keyboardShouldPersistTaps="handled"
      contentContainerClassName="flex-1">
      <View className="w-full relative " style={{ height: Dimensions.get("screen").height / 2.25 }}>
        <ImageBackground
          source={images.loginGraphic}
          className="size-full rounded-b-lg"
          resizeMode="stretch"
        />
        <Image source={images.logo} className="self-center size-48 absolute -bottom-16 z-10" />
      </View>
      <Slot />
    </ScrollView>
    // </KeyboardAvoidingView>
  );
}
