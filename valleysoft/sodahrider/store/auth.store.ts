import { create } from "zustand";
//add types for user if needed

type User = {
  id: string;
  name: string;
  email: string;
};

type AuthState = {
  user: User | null;
  setUser: (user: User | null) => void;
  clearUser: () => void;
  isAuthenticated: boolean;
  getUser: () => User | null;
  setIsAuthenticated: (isAuthenticated: boolean) => void;
};
const useUserStrore = create<AuthState>((set, get) => ({
  user: null,
  setUser: (user) => set({ user }),
  clearUser: () => set({ user: null }),
  isAuthenticated: false,
  getUser: () => get().user,
  setIsAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
}));
export default useUserStrore;
