import React, { createContext, useContext, useState } from "react";

// Define the types of effects available
type EffectType = "none" | "shadow" | "gradient" | "shader";

// Define the types of shaders available
type ShaderType = "rainbow" | "neon" | "fire" | "water" | "matrix" | "ice" | "none";

// Define the types of backgrounds available
type BackgroundType = "brick" | "stone" | "wood" | "metal" | "transparent" | "none";

// Define the context interface
interface TextEffectsContextType {
  text: string;
  setText: (text: string) => void;
  fontSize: number;
  setFontSize: (size: number) => void;
  fontColor: string;
  setFontColor: (color: string) => void;
  effectType: EffectType;
  setEffectType: (type: EffectType) => void;
  effectIntensity: number;
  setEffectIntensity: (intensity: number) => void;
  shaderType: ShaderType;
  setShaderType: (type: ShaderType) => void;
  backgroundType: BackgroundType;
  setBackgroundType: (type: BackgroundType) => void;
}

// Default context values
const defaultContext: TextEffectsContextType = {
  text: "TEXTFY",
  setText: () => {},
  fontSize: 60,
  setFontSize: () => {},
  fontColor: "#000000",
  setFontColor: () => {},
  effectType: "none",
  setEffectType: () => {},
  effectIntensity: 0.5,
  setEffectIntensity: () => {},
  shaderType: "none",
  setShaderType: () => {},
  backgroundType: "none",
  setBackgroundType: () => {},
};

const TextEffectsContext = createContext<TextEffectsContextType>(defaultContext);

export const useTextEffects = () => useContext(TextEffectsContext);

export const TextEffectsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State for text content and styling
  const [text, setText] = useState(defaultContext.text);
  const [fontSize, setFontSize] = useState(defaultContext.fontSize);
  const [fontColor, setFontColor] = useState(defaultContext.fontColor);

  // State for effects
  const [effectType, setEffectType] = useState<EffectType>(defaultContext.effectType);
  const [effectIntensity, setEffectIntensity] = useState(defaultContext.effectIntensity);
  const [shaderType, setShaderType] = useState<ShaderType>(defaultContext.shaderType);
  const [backgroundType, setBackgroundType] = useState<BackgroundType>(
    defaultContext.backgroundType
  );

  // Custom effect type handler to ensure shader type is properly set
  const handleEffectTypeChange = (type: EffectType) => {
    setEffectType(type);

    // If changing away from shader, reset shader type
    if (type !== "shader") {
      setShaderType("none");
    }
    // If changing to shader and no shader is selected, set a default
    else if (type === "shader" && shaderType === "none") {
      setShaderType("rainbow");
    }
  };

  return (
    <TextEffectsContext.Provider
      value={{
        text,
        setText,
        fontSize,
        setFontSize,
        fontColor,
        setFontColor,
        effectType,
        setEffectType: handleEffectTypeChange, // Use our custom handler
        effectIntensity,
        setEffectIntensity,
        shaderType,
        setShaderType,
        backgroundType,
        setBackgroundType,
      }}>
      {children}
    </TextEffectsContext.Provider>
  );
};
