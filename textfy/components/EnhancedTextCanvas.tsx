import React, { useEffect, useState } from "react";
import { ImageBackground, StyleSheet, Text, View } from "react-native";
import { useTextEffects } from "./TextEffectsProvider";

// Background images for different textures
const backgroundImages = {
  brick: require("../assets/backgrounds/brick.jpeg"),
  stone: require("../assets/backgrounds/stone.jpeg"),
  wood: require("../assets/backgrounds/wood.jpeg"),
  metal: require("../assets/backgrounds/metal.jpeg"),
  transparent: null,
  none: null,
};

export const EnhancedTextCanvas = () => {
  // We don't need window dimensions for this implementation
  const { text, fontSize, fontColor, effectType, effectIntensity, shaderType, backgroundType } =
    useTextEffects();

  // Add animation state
  const [animationTime, setAnimationTime] = useState(0);

  // Set up animation loop for shader effects
  useEffect(() => {
    // Only run animation if using shader effects
    if (effectType !== "shader") return;

    let animationFrame: number;
    let lastTime = Date.now();

    const updateAnimation = () => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      setAnimationTime((prev) => prev + deltaTime);
      animationFrame = requestAnimationFrame(updateAnimation);
    };

    // Start animation
    animationFrame = requestAnimationFrame(updateAnimation);

    // Clean up animation on unmount or effect change
    return () => {
      cancelAnimationFrame(animationFrame);
    };
  }, [effectType, shaderType]);

  // Calculate effect values based on the current settings
  let textColor = fontColor;
  let textShadowColor = "transparent";
  let textShadowWidth = 0;
  let textShadowHeight = 0;
  let textShadowRadius = 0;
  let textBorderWidth = 0;

  // Apply different effects
  if (effectType === "gradient") {
    // For gradient effect, use a purple gradient
    textColor = fontColor;
    textShadowColor = "rgba(128, 0, 128, 0.8)";
    textShadowWidth = 0;
    textShadowHeight = 0;
    textShadowRadius = 15 * effectIntensity;
  } else if (effectType === "shadow") {
    // For shadow effect
    textColor = fontColor;
    textShadowColor = "rgba(0, 0, 0, 0.8)";
    textShadowWidth = 5 * effectIntensity;
    textShadowHeight = 5 * effectIntensity;
    textShadowRadius = 10 * effectIntensity;
  } else if (effectType === "shader") {
    // For shader effects, use distinctive colors based on shader type
    switch (shaderType) {
      case "rainbow":
        // Rainbow effect - use a gradient of colors
        const rainbowColors = ["red", "orange", "yellow", "green", "blue", "indigo", "violet"];
        const rainbowIndex = Math.floor(animationTime / 500) % rainbowColors.length;
        textColor = rainbowColors[rainbowIndex];
        textShadowColor = rainbowColors[(rainbowIndex + 4) % rainbowColors.length];
        textShadowWidth = 3 * effectIntensity;
        textShadowHeight = 3 * effectIntensity;
        textShadowRadius = 6 * effectIntensity;
        break;

      case "fire":
        // Fire effect - use orange/red colors with flickering
        const fireIntensity = 0.7 + Math.sin(animationTime / 200) * 0.3 * effectIntensity;
        textColor = `rgba(255, ${Math.floor(150 * fireIntensity)}, 50, 1)`;
        textShadowColor = "rgba(255, 50, 0, 0.8)";
        textShadowWidth = 3 * effectIntensity;
        textShadowHeight = 3 * effectIntensity;
        textShadowRadius = 8 * effectIntensity;
        break;

      case "neon":
        // Neon effect - bright color with pulsing glow
        const neonPulse = 0.5 + Math.sin(animationTime / 400) * 0.5 * effectIntensity;
        textColor = fontColor;
        textShadowColor = fontColor;
        textShadowWidth = 0;
        textShadowHeight = 0;
        textShadowRadius = 15 * neonPulse;
        break;

      case "water":
        // Water effect - blue colors with ripple
        const waterOffset = Math.sin(animationTime / 300) * 2 * effectIntensity;
        textColor = "rgba(0, 150, 255, 1)";
        textShadowColor = "rgba(0, 200, 255, 0.8)";
        textShadowWidth = waterOffset;
        textShadowHeight = waterOffset;
        textShadowRadius = 8 * effectIntensity;
        break;

      case "matrix":
        // Matrix effect - green digital rain
        const matrixIntensity = 0.7 + Math.sin(animationTime / 150) * 0.3 * effectIntensity;
        textColor = `rgba(0, ${Math.floor(255 * matrixIntensity)}, 0, 1)`;
        textShadowColor = "rgba(0, 255, 0, 0.6)";
        textShadowWidth = 0;
        textShadowHeight = 0;
        textShadowRadius = 10 * effectIntensity;
        break;

      case "ice":
        // Ice effect - blue/white glow with white border like in the image
        const icePulse = 0.7 + Math.sin(animationTime / 600) * 0.3 * effectIntensity;
        textColor = "#a0e0ff"; // Light blue
        textShadowColor = "rgba(255, 255, 255, 0.9)";
        textShadowWidth = 0;
        textShadowHeight = 0;
        textShadowRadius = 12 * icePulse;

        // Add a second shadow for the outer glow
        // We can't actually add multiple shadows in RN, but we can fake it with style layering
        textBorderWidth = 3 * effectIntensity;
        break;

      default:
        textColor = fontColor;
    }
  }

  // Create the main text component with the applied effects
  const TextWithEffects = () => (
    <View style={styles.textContainer}>
      {/* If using ice effect, add a white border text behind */}
      {shaderType === "ice" && (
        <Text
          style={[
            styles.iceOuterText,
            {
              fontSize: fontSize,
              color: "white",
              textShadowColor: "rgba(255, 255, 255, 0.8)",
              textShadowOffset: { width: 0, height: 0 },
              textShadowRadius: 15 * effectIntensity,
            },
          ]}>
          {text}
        </Text>
      )}

      {/* Main text with applied effects */}
      <Text
        style={[
          styles.mainText,
          {
            fontSize: fontSize,
            color: textColor,
            textShadowColor: textShadowColor,
            textShadowOffset: {
              width: textShadowWidth,
              height: textShadowHeight,
            },
            textShadowRadius: textShadowRadius,
          },
        ]}>
        {text}
      </Text>
    </View>
  );

  return (
    <View
      style={[
        styles.container,
        backgroundType === "transparent" && { backgroundColor: "transparent" },
      ]}>
      {/* Background image based on selected type */}
      {backgroundType !== "none" &&
      backgroundType !== "transparent" &&
      backgroundImages[backgroundType] ? (
        <ImageBackground
          source={backgroundImages[backgroundType]}
          style={styles.backgroundImage}
          resizeMode="cover">
          <TextWithEffects />
        </ImageBackground>
      ) : backgroundType === "transparent" ? (
        <View style={styles.transparentContainer}>
          <TextWithEffects />
        </View>
      ) : (
        <TextWithEffects />
      )}

      {/* Debug overlay */}
      <View style={styles.debugOverlay}>
        <Text style={styles.debugInfo}>
          Text: {text}
          {"\n"}
          Font Size: {fontSize}
          {"\n"}
          Effect: {effectType}
          {"\n"}
          Shader: {effectType === "shader" ? shaderType : "none"}
          {"\n"}
          Background: {backgroundType}
          {"\n"}
          Intensity: {effectIntensity.toFixed(1)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  backgroundImage: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  transparentContainer: {
    flex: 1,
    backgroundColor: "transparent",
    justifyContent: "center",
    alignItems: "center",
  },
  textContainer: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  mainText: {
    fontWeight: "bold",
    textAlign: "center",
  },
  iceOuterText: {
    fontWeight: "bold",
    textAlign: "center",
    position: "absolute",
    zIndex: 1,
  },
  debugOverlay: {
    position: "absolute",
    bottom: 10,
    left: 10,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: 10,
    borderRadius: 5,
  },
  debugInfo: {
    color: "white",
    fontSize: 12,
  },
});
