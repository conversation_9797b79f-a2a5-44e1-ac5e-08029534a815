import React, { useEffect, useState } from "react";
import { StyleSheet, Text, useWindowDimensions, View } from "react-native";
import { useTextEffects } from "./TextEffectsProvider";

export const TextCanvasNew = () => {
  const { width, height } = useWindowDimensions();
  const { text, fontSize, fontColor, effectType, effectIntensity, shaderType } = useTextEffects();

  // Add animation state
  const [animationTime, setAnimationTime] = useState(0);

  // Set up animation loop for shader effects
  useEffect(() => {
    // Only run animation if using shader effects
    if (effectType !== "shader") return;

    let animationFrame: number;
    let lastTime = Date.now();

    const updateAnimation = () => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      setAnimationTime((prev) => prev + deltaTime);
      animationFrame = requestAnimationFrame(updateAnimation);
    };

    // Start animation
    animationFrame = requestAnimationFrame(updateAnimation);

    // Clean up animation on unmount or effect change
    return () => {
      cancelAnimationFrame(animationFrame);
    };
  }, [effectType, shaderType]);

  // Calculate effect values based on the current settings
  let textColor = fontColor;
  let textShadowColor = "transparent";
  let textShadowWidth = 0;
  let textShadowHeight = 0;
  let textShadowRadius = 0;

  // Apply different effects
  if (effectType === "gradient") {
    // For gradient effect, use a purple gradient
    textColor = fontColor;
    textShadowColor = "rgba(128, 0, 128, 0.8)";
    textShadowWidth = 0;
    textShadowHeight = 0;
    textShadowRadius = 15 * effectIntensity;
  } else if (effectType === "shadow") {
    // For shadow effect
    textColor = fontColor;
    textShadowColor = "rgba(0, 0, 0, 0.8)";
    textShadowWidth = 5 * effectIntensity;
    textShadowHeight = 5 * effectIntensity;
    textShadowRadius = 10 * effectIntensity;
  } else if (effectType === "shader") {
    // For shader effects, use distinctive colors based on shader type
    switch (shaderType) {
      case "rainbow":
        // Rainbow effect - use a gradient of colors
        const rainbowColors = ["red", "orange", "yellow", "green", "blue", "indigo", "violet"];
        const rainbowIndex = Math.floor(animationTime / 500) % rainbowColors.length;
        textColor = rainbowColors[rainbowIndex];
        textShadowColor = rainbowColors[(rainbowIndex + 4) % rainbowColors.length];
        textShadowWidth = 3 * effectIntensity;
        textShadowHeight = 3 * effectIntensity;
        textShadowRadius = 6 * effectIntensity;
        break;

      case "fire":
        // Fire effect - use orange/red colors with flickering
        const fireIntensity = 0.7 + Math.sin(animationTime / 200) * 0.3 * effectIntensity;
        textColor = `rgba(255, ${Math.floor(150 * fireIntensity)}, 50, 1)`;
        textShadowColor = "rgba(255, 50, 0, 0.8)";
        textShadowWidth = 3 * effectIntensity;
        textShadowHeight = 3 * effectIntensity;
        textShadowRadius = 8 * effectIntensity;
        break;

      case "neon":
        // Neon effect - bright color with pulsing glow
        const neonPulse = 0.5 + Math.sin(animationTime / 400) * 0.5 * effectIntensity;
        textColor = fontColor;
        textShadowColor = fontColor;
        textShadowWidth = 0;
        textShadowHeight = 0;
        textShadowRadius = 15 * neonPulse;
        break;

      case "water":
        // Water effect - blue colors with ripple
        const waterOffset = Math.sin(animationTime / 300) * 2 * effectIntensity;
        textColor = "rgba(0, 150, 255, 1)";
        textShadowColor = "rgba(0, 200, 255, 0.8)";
        textShadowWidth = waterOffset;
        textShadowHeight = waterOffset;
        textShadowRadius = 8 * effectIntensity;
        break;

      case "matrix":
        // Matrix effect - green digital rain
        const matrixIntensity = 0.7 + Math.sin(animationTime / 150) * 0.3 * effectIntensity;
        textColor = `rgba(0, ${Math.floor(255 * matrixIntensity)}, 0, 1)`;
        textShadowColor = "rgba(0, 255, 0, 0.6)";
        textShadowWidth = 0;
        textShadowHeight = 0;
        textShadowRadius = 10 * effectIntensity;
        break;

      default:
        textColor = fontColor;
    }
  }

  return (
    <View style={styles.container}>
      {/* Main text display */}
      <View style={styles.textContainer}>
        <Text
          style={[
            styles.mainText,
            {
              fontSize: fontSize,
              color: textColor,
              textShadowColor: textShadowColor,
              textShadowOffset: {
                width: textShadowWidth,
                height: textShadowHeight,
              },
              textShadowRadius: textShadowRadius,
            },
          ]}>
          {text}
        </Text>
      </View>

      {/* Debug overlay */}
      <View style={styles.debugOverlay}>
        <Text style={styles.debugInfo}>
          Text: {text}
          {"\n"}
          Font Size: {fontSize}
          {"\n"}
          Effect: {effectType}
          {"\n"}
          Shader: {effectType === "shader" ? shaderType : "none"}
          {"\n"}
          Intensity: {effectIntensity.toFixed(1)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  textContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  mainText: {
    fontWeight: "bold",
    textAlign: "center",
  },
  debugOverlay: {
    position: "absolute",
    bottom: 10,
    left: 10,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: 10,
    borderRadius: 5,
  },
  debugInfo: {
    color: "white",
    fontSize: 12,
  },
});
