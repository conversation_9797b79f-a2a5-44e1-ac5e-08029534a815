import React from "react";
import { ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";
import { TextEffectPresets } from "./TextEffectPresets";
import { useTextEffects } from "./TextEffectsProvider";

export const ControlPanel = ({ onSaveImage }: { onSaveImage: () => void }) => {
  const {
    text,
    setText,
    fontSize,
    setFontSize,
    fontColor,
    setFontColor,
    effectType,
    setEffectType,
    effectIntensity,
    setEffectIntensity,
    shaderType,
    setShaderType,
    backgroundType,
    setBackgroundType,
  } = useTextEffects();

  const effectButtons = [
    { label: "None", value: "none" },
    { label: "Shadow", value: "shadow" },
    { label: "Gradient", value: "gradient" },
    { label: "Shader", value: "shader" },
  ];

  const shaderButtons = [
    { label: "Rainbow", value: "rainbow" },
    { label: "Fire", value: "fire" },
    { label: "Neon", value: "neon" },
    { label: "Water", value: "water" },
    { label: "Matrix", value: "matrix" },
    { label: "Ice", value: "ice" },
  ];

  const backgroundButtons = [
    { label: "None", value: "none" },
    { label: "Transparent", value: "transparent" },
    { label: "Brick", value: "brick" },
    { label: "Stone", value: "stone" },
    { label: "Wood", value: "wood" },
    { label: "Metal", value: "metal" },
  ];

  // Handler for effect type change
  const handleEffectTypeChange = (type: string) => {
    setEffectType(type as any);
    // Reset shader type if not using shader effect
    if (type !== "shader") {
      setShaderType("none");
    } else if (shaderType === "none") {
      // Set a default shader type if none is selected
      setShaderType("rainbow");
    }
  };

  const colorOptions = [
    { label: "Black", value: "#000000" },
    { label: "Red", value: "#FF0000" },
    { label: "Blue", value: "#0000FF" },
    { label: "Green", value: "#00FF00" },
  ];

  // Font size adjustment
  const increaseFontSize = () => {
    setFontSize(Math.min(fontSize + 5, 120));
  };

  const decreaseFontSize = () => {
    setFontSize(Math.max(fontSize - 5, 20));
  };

  // Intensity adjustment
  const increaseIntensity = () => {
    setEffectIntensity(Math.min(effectIntensity + 0.1, 1));
  };

  const decreaseIntensity = () => {
    setEffectIntensity(Math.max(effectIntensity - 0.1, 0));
  };

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Text</Text>
          <TextInput
            style={styles.textInput}
            value={text}
            onChangeText={setText}
            placeholder="Enter text"
          />
        </View>

        <TextEffectPresets />

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Font Size: {Math.round(fontSize)}</Text>
          <View style={styles.adjustmentRow}>
            <TouchableOpacity style={styles.adjustButton} onPress={decreaseFontSize}>
              <Text style={styles.adjustButtonText}>-</Text>
            </TouchableOpacity>
            <View style={styles.valueDisplay}>
              <Text style={styles.valueText}>{fontSize}</Text>
            </View>
            <TouchableOpacity style={styles.adjustButton} onPress={increaseFontSize}>
              <Text style={styles.adjustButtonText}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Font Color</Text>
          <View style={styles.colorOptions}>
            {colorOptions.map((color) => (
              <TouchableOpacity
                key={color.value}
                style={[
                  styles.colorButton,
                  { backgroundColor: color.value },
                  fontColor === color.value && styles.selectedColorButton,
                ]}
                onPress={() => setFontColor(color.value)}
              />
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Effect Type</Text>
          <View style={styles.effectButtons}>
            {effectButtons.map((effect) => (
              <TouchableOpacity
                key={effect.value}
                style={[
                  styles.effectButton,
                  effectType === effect.value && styles.selectedEffectButton,
                ]}
                onPress={() => handleEffectTypeChange(effect.value)}>
                <Text
                  style={[
                    styles.effectButtonText,
                    effectType === effect.value && styles.selectedEffectButtonText,
                  ]}>
                  {effect.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Effect Intensity: {effectIntensity.toFixed(1)}</Text>
          <View style={styles.adjustmentRow}>
            <TouchableOpacity style={styles.adjustButton} onPress={decreaseIntensity}>
              <Text style={styles.adjustButtonText}>-</Text>
            </TouchableOpacity>
            <View style={styles.valueDisplay}>
              <Text style={styles.valueText}>{effectIntensity.toFixed(1)}</Text>
            </View>
            <TouchableOpacity style={styles.adjustButton} onPress={increaseIntensity}>
              <Text style={styles.adjustButtonText}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Shader Type Selection - Only show when shader effect is selected */}
        {effectType === "shader" && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Shader Type</Text>
            <View style={styles.effectButtons}>
              {shaderButtons.map((shader) => (
                <TouchableOpacity
                  key={shader.value}
                  style={[
                    styles.effectButton,
                    shaderType === shader.value && styles.selectedEffectButton,
                  ]}
                  onPress={() => setShaderType(shader.value as any)}>
                  <Text
                    style={[
                      styles.effectButtonText,
                      shaderType === shader.value && styles.selectedEffectButtonText,
                    ]}>
                    {shader.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Background Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Background</Text>
          <View style={styles.effectButtons}>
            {backgroundButtons.map((bg) => (
              <TouchableOpacity
                key={bg.value}
                style={[
                  styles.effectButton,
                  backgroundType === bg.value && styles.selectedEffectButton,
                ]}
                onPress={() => setBackgroundType(bg.value as any)}>
                <Text
                  style={[
                    styles.effectButtonText,
                    backgroundType === bg.value && styles.selectedEffectButtonText,
                  ]}>
                  {bg.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Save Button */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.saveButton} onPress={onSaveImage}>
            <Text style={styles.saveButtonText}>Save as Image</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  adjustmentRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 8,
  },
  adjustButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#007AFF",
    justifyContent: "center",
    alignItems: "center",
  },
  adjustButtonText: {
    fontSize: 24,
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  valueDisplay: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    marginHorizontal: 10,
  },
  valueText: {
    fontSize: 16,
    fontWeight: "bold",
  },
  colorOptions: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  colorButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  selectedColorButton: {
    borderWidth: 3,
    borderColor: "#007AFF",
  },
  effectButtons: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  effectButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#f0f0f0",
    marginRight: 8,
    marginBottom: 8,
  },
  selectedEffectButton: {
    backgroundColor: "#007AFF",
  },
  effectButtonText: {
    fontSize: 14,
    color: "#333",
  },
  selectedEffectButtonText: {
    color: "#fff",
  },
  saveButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 10,
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
});
