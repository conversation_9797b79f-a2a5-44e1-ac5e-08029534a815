import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useTextEffects } from "./TextEffectsProvider";

// Define preset types
interface TextEffectPreset {
  name: string;
  fontColor: string;
  effectType: "none" | "shadow" | "gradient" | "shader";
  effectIntensity: number;
  fontSize?: number;
  shaderType?: "rainbow" | "neon" | "fire" | "water" | "matrix" | "ice" | "none";
}

// Define presets
const presets: TextEffectPreset[] = [
  {
    name: "Shadow",
    fontColor: "#000000",
    effectType: "shadow",
    effectIntensity: 0.8,
  },
  {
    name: "Red Glow",
    fontColor: "#FF0000",
    effectType: "shadow",
    effectIntensity: 0.7,
  },
  {
    name: "Blue Gradient",
    fontColor: "#0000FF",
    effectType: "gradient",
    effectIntensity: 0.6,
  },
  {
    name: "Neon Green",
    fontColor: "#00FF00",
    effectType: "shadow",
    effectIntensity: 0.9,
  },
  {
    name: "Purple Magic",
    fontColor: "#800080",
    effectType: "gradient",
    effectIntensity: 0.5,
  },
  // Shader presets
  {
    name: "Rainbow",
    fontColor: "#FFFFFF",
    effectType: "shader",
    effectIntensity: 0.8,
    shaderType: "rainbow",
  },
  {
    name: "Fire",
    fontColor: "#FF5500",
    effectType: "shader",
    effectIntensity: 0.9,
    shaderType: "fire",
  },
  {
    name: "Neon",
    fontColor: "#00FFFF",
    effectType: "shader",
    effectIntensity: 0.7,
    shaderType: "neon",
  },
  {
    name: "Water",
    fontColor: "#0088FF",
    effectType: "shader",
    effectIntensity: 0.8,
    shaderType: "water",
  },
  {
    name: "Matrix",
    fontColor: "#00FF00",
    effectType: "shader",
    effectIntensity: 0.9,
    shaderType: "matrix",
  },
  {
    name: "Ice",
    fontColor: "#a0e0ff",
    effectType: "shader",
    effectIntensity: 0.8,
    shaderType: "ice",
  },
];

export const TextEffectPresets = () => {
  const { setFontColor, setEffectType, setEffectIntensity, setFontSize, setShaderType } =
    useTextEffects();

  const applyPreset = (preset: TextEffectPreset) => {
    console.log("Applying preset:", preset);

    // First set the effect type
    setEffectType(preset.effectType);

    // Then set shader type if needed
    if (preset.effectType === "shader" && preset.shaderType) {
      setShaderType(preset.shaderType);
    } else {
      // Reset shader type if not using shader effect
      setShaderType("none");
    }

    // Set other properties
    setFontColor(preset.fontColor);
    setEffectIntensity(preset.effectIntensity);

    // Apply fontSize if provided
    if (preset.fontSize) {
      setFontSize(preset.fontSize);
    }

    // Log the applied preset for debugging
    console.log(
      `Applied preset: ${preset.name}, Effect: ${preset.effectType}, Shader: ${
        preset.shaderType || "none"
      }`
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Presets</Text>
      <View style={styles.presetContainer}>
        {presets.map((preset, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.presetButton,
              { backgroundColor: preset.fontColor === "#FFFFFF" ? "#EEEEEE" : preset.fontColor },
            ]}
            onPress={() => applyPreset(preset)}>
            <Text
              style={[
                styles.presetText,
                { color: isLightColor(preset.fontColor) ? "#000000" : "#FFFFFF" },
              ]}>
              {preset.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

// Helper function to determine if a color is light or dark
const isLightColor = (color: string): boolean => {
  // Convert hex to RGB
  const hex = color.replace("#", "");
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Calculate brightness (YIQ formula)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;

  // Return true if the color is light
  return brightness > 128;
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  presetContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  presetButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  presetText: {
    fontSize: 12,
    fontWeight: "bold",
  },
});
