// Collection of GLSL shaders for text effects

// Rainbow shader - creates a rainbow gradient effect
export const rainbowShader = `
uniform float u_time;
uniform vec2 u_resolution;
uniform float u_intensity;

vec4 main(vec2 pos) {
    // Normalize coordinates
    vec2 uv = pos / u_resolution.xy;
    
    // Create rainbow effect
    float time = u_time * 0.001;
    vec3 col = 0.5 + 0.5 * cos(time + uv.xyx + vec3(0, 2, 4));
    
    // Apply intensity
    col = mix(vec3(1.0), col, u_intensity);
    
    return vec4(col, 1.0);
}
`;

// Neon glow shader - creates a neon glow effect
export const neonShader = `
uniform float u_time;
uniform vec2 u_resolution;
uniform float u_intensity;
uniform vec4 u_color;

vec4 main(vec2 pos) {
    // Normalize coordinates
    vec2 uv = pos / u_resolution.xy;
    
    // Create pulsing effect
    float pulse = 0.5 + 0.5 * sin(u_time * 0.001);
    
    // Mix with base color
    vec3 baseColor = u_color.rgb;
    vec3 glowColor = baseColor + vec3(0.2, 0.2, 0.2) * pulse * u_intensity;
    
    return vec4(glowColor, 1.0);
}
`;

// Fire shader - creates a fire-like effect
export const fireShader = `
uniform float u_time;
uniform vec2 u_resolution;
uniform float u_intensity;

// Noise function
float noise(vec2 p) {
    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
}

vec4 main(vec2 pos) {
    // Normalize coordinates
    vec2 uv = pos / u_resolution.xy;
    
    // Time varying
    float time = u_time * 0.001;
    
    // Create fire effect
    float n = noise(uv * 8.0 + time);
    float ny = noise(uv * 8.0 - time);
    
    // Fire colors
    vec3 col = vec3(1.0, 0.5, 0.0); // Orange base
    
    // Add yellow tips
    if (n > 0.7) {
        col = mix(col, vec3(1.0, 1.0, 0.0), (n - 0.7) / 0.3);
    }
    
    // Add red base
    if (ny < 0.3) {
        col = mix(col, vec3(0.8, 0.0, 0.0), 1.0 - ny / 0.3);
    }
    
    // Apply intensity
    col = mix(vec3(1.0), col, u_intensity);
    
    return vec4(col, 1.0);
}
`;

// Water ripple shader
export const waterShader = `
uniform float u_time;
uniform vec2 u_resolution;
uniform float u_intensity;

vec4 main(vec2 pos) {
    // Normalize coordinates
    vec2 uv = pos / u_resolution.xy;
    
    // Time varying
    float time = u_time * 0.001;
    
    // Create ripple effect
    float frequency = 20.0;
    float amplitude = 0.02 * u_intensity;
    float distortion = sin(uv.x * frequency + time * 2.0) * sin(uv.y * frequency + time * 2.0) * amplitude;
    
    // Blue water colors
    vec3 col1 = vec3(0.0, 0.5, 1.0); // Light blue
    vec3 col2 = vec3(0.0, 0.2, 0.8); // Dark blue
    
    vec3 col = mix(col1, col2, distortion * 10.0 + 0.5);
    
    // Apply intensity
    col = mix(vec3(1.0), col, u_intensity);
    
    return vec4(col, 1.0);
}
`;

// Matrix digital rain effect
export const matrixShader = `
uniform float u_time;
uniform vec2 u_resolution;
uniform float u_intensity;

float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
}

vec4 main(vec2 pos) {
    // Normalize coordinates
    vec2 uv = pos / u_resolution.xy;
    
    // Time varying
    float time = u_time * 0.001;
    
    // Create matrix effect
    float size = 15.0;
    vec2 ipos = floor(uv * size);
    
    // Random green intensity
    float r = random(ipos + time);
    float g = 0.5 + 0.5 * r;
    
    // Digital rain effect
    float rain = mod(ipos.y + time * 2.0, size) / size;
    g *= smoothstep(0.0, 0.8, rain);
    
    // Matrix green color
    vec3 col = vec3(0.0, g, 0.0);
    
    // Apply intensity
    col = mix(vec3(1.0), col, u_intensity);
    
    return vec4(col, 1.0);
}
`;
