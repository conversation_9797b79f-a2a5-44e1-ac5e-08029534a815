import { Canvas, Group } from "@shopify/react-native-skia";
import React, { useEffect, useState } from "react";
import { StyleSheet, Text, useWindowDimensions, View } from "react-native";
import { useTextEffects } from "./TextEffectsProvider";

export const TextCanvas = () => {
  const { width, height } = useWindowDimensions();
  const { text, fontSize, fontColor, effectType, effectIntensity, shaderType } = useTextEffects();

  // Add animation state
  const [animationTime, setAnimationTime] = useState(0);

  // Set up animation loop for shader effects
  useEffect(() => {
    // Only run animation if using shader effects
    if (effectType !== "shader") return;

    let animationFrame: number;
    let lastTime = Date.now();

    const updateAnimation = () => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      setAnimationTime((prev) => prev + deltaTime);
      animationFrame = requestAnimationFrame(updateAnimation);
    };

    // Start animation
    animationFrame = requestAnimationFrame(updateAnimation);

    // Clean up animation on unmount or effect change
    return () => {
      cancelAnimationFrame(animationFrame);
    };
  }, [effectType, shaderType]);

  // We'll use a different approach for text rendering
  // Instead of drawing circles, we'll just use the React Native Text component
  // and hide the Skia canvas

  // Calculate effect values based on the current settings
  let pathColor = fontColor;
  let textColor = fontColor;
  let textShadowColor = "transparent";
  let textShadowWidth = 0;
  let textShadowHeight = 0;
  let textShadowRadius = 0;

  // Apply different effects
  if (effectType === "gradient") {
    // For gradient effect, use a purple color for the path
    pathColor = `rgba(128, 0, 128, ${effectIntensity})`;
    // And keep the original color for the text
    textColor = fontColor;
  } else if (effectType === "shadow") {
    // For shadow effect, use the original color for both path and text
    pathColor = fontColor;
    textColor = fontColor;
    // And add a shadow to the text
    textShadowColor = "rgba(0, 0, 0, 0.8)";
    textShadowWidth = 5 * effectIntensity;
    textShadowHeight = 5 * effectIntensity;
    textShadowRadius = 10 * effectIntensity;
  } else if (effectType === "shader") {
    // For shader effects, use distinctive colors based on shader type
    switch (shaderType) {
      case "rainbow":
        // Rainbow effect - use a gradient of colors
        const rainbowColors = ["red", "orange", "yellow", "green", "blue", "indigo", "violet"];
        const rainbowIndex = Math.floor(animationTime / 500) % rainbowColors.length;
        pathColor = rainbowColors[rainbowIndex];
        textColor = rainbowColors[(rainbowIndex + 2) % rainbowColors.length];
        textShadowColor = rainbowColors[(rainbowIndex + 4) % rainbowColors.length];
        textShadowWidth = 3 * effectIntensity;
        textShadowHeight = 3 * effectIntensity;
        textShadowRadius = 6 * effectIntensity;
        break;

      case "fire":
        // Fire effect - use orange/red colors with flickering
        const fireIntensity = 0.7 + Math.sin(animationTime / 200) * 0.3 * effectIntensity;
        pathColor = `rgba(255, ${Math.floor(100 * fireIntensity)}, 0, 1)`;
        textColor = `rgba(255, ${Math.floor(150 * fireIntensity)}, 50, 1)`;
        textShadowColor = "rgba(255, 50, 0, 0.8)";
        textShadowWidth = 3 * effectIntensity;
        textShadowHeight = 3 * effectIntensity;
        textShadowRadius = 8 * effectIntensity;
        break;

      case "neon":
        // Neon effect - bright color with pulsing glow
        const neonPulse = 0.5 + Math.sin(animationTime / 400) * 0.5 * effectIntensity;
        pathColor = fontColor;
        textColor = fontColor;
        textShadowColor = fontColor;
        textShadowWidth = 0;
        textShadowHeight = 0;
        textShadowRadius = 15 * neonPulse;
        break;

      case "water":
        // Water effect - blue colors with ripple
        const waterOffset = Math.sin(animationTime / 300) * 2 * effectIntensity;
        pathColor = "rgba(0, 100, 255, 1)";
        textColor = "rgba(0, 150, 255, 1)";
        textShadowColor = "rgba(0, 200, 255, 0.8)";
        textShadowWidth = waterOffset;
        textShadowHeight = waterOffset;
        textShadowRadius = 8 * effectIntensity;
        break;

      case "matrix":
        // Matrix effect - green digital rain
        const matrixIntensity = 0.7 + Math.sin(animationTime / 150) * 0.3 * effectIntensity;
        pathColor = `rgba(0, ${Math.floor(200 * matrixIntensity)}, 0, 1)`;
        textColor = `rgba(0, ${Math.floor(255 * matrixIntensity)}, 0, 1)`;
        textShadowColor = "rgba(0, 255, 0, 0.6)";
        textShadowWidth = 0;
        textShadowHeight = 0;
        textShadowRadius = 10 * effectIntensity;
        break;

      default:
        pathColor = fontColor;
        textColor = fontColor;
    }
  }

  return (
    <View style={styles.container}>
      {/* Hide the Skia canvas since we're not using it */}
      <Canvas style={[styles.canvas, { opacity: 0 }]}>
        <Group>{/* Empty group - we're not drawing anything with Skia */}</Group>
      </Canvas>

      {/* Use React Native Text for rendering */}
      <View style={styles.textContainer}>
        <Text
          style={[
            styles.fallbackText,
            {
              fontSize: fontSize,
              color: textColor,
              textShadowColor: textShadowColor,
              textShadowOffset: {
                width: textShadowWidth,
                height: textShadowHeight,
              },
              textShadowRadius: textShadowRadius,
            },
          ]}>
          {text}
        </Text>
      </View>

      {/* Debug overlay */}
      <View style={styles.debugOverlay}>
        <Text style={styles.debugInfo}>
          Text: {text}
          {"\n"}
          Font Size: {fontSize}
          {"\n"}
          Effect: {effectType}
          {"\n"}
          Shader: {effectType === "shader" ? shaderType : "none"}
          {"\n"}
          Intensity: {effectIntensity.toFixed(1)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  canvas: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    justifyContent: "center",
    alignItems: "center",
  },
  textContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  fallbackText: {
    fontWeight: "bold",
    textAlign: "center",
  },
  debugText: {
    fontSize: 16,
    color: "#333",
  },
  debugOverlay: {
    position: "absolute",
    top: 10,
    left: 10,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    padding: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: "#ccc",
  },
  debugInfo: {
    fontSize: 12,
    color: "#333",
  },
});
