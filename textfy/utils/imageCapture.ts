import * as FileSystem from "expo-file-system";
import * as MediaLibrary from "expo-media-library";
import { Alert, Platform } from "react-native";
import { captureRef } from "react-native-view-shot";

/**
 * Captures a view as an image and saves it to the device's media library
 * @param viewRef React ref to the view to capture
 * @returns Promise that resolves when the image is saved
 */
export const captureAndSaveImage = async (viewRef: any): Promise<boolean> => {
  try {
    // Request permissions first
    const { status } = await MediaLibrary.requestPermissionsAsync();

    if (status !== "granted") {
      Alert.alert("Permission Required", "Please grant permission to save images to your device.", [
        { text: "OK" },
      ]);
      return false;
    }

    // Capture the view as an image
    // Use PNG format to preserve transparency
    const uri = await captureRef(viewRef, {
      format: "png",
      quality: 1,
      // @ts-ignore - transparent is supported but not in the type definitions
      transparent: true,
    });

    // Generate a unique filename with timestamp
    const timestamp = new Date().getTime();
    const filename = `textfy_${timestamp}.png`;

    // On Android, we need to copy the file to a location that's accessible by the media library
    if (Platform.OS === "android") {
      const fileUri = `${FileSystem.documentDirectory}${filename}`;
      await FileSystem.copyAsync({
        from: uri,
        to: fileUri,
      });

      // Save the image to the media library
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      await MediaLibrary.createAlbumAsync("Textfy", asset, false);
    } else {
      // On iOS, we can save directly
      const asset = await MediaLibrary.createAssetAsync(uri);
      await MediaLibrary.createAlbumAsync("Textfy", asset, false);
    }

    // Show success message
    Alert.alert("Success", "Your text effect has been saved to your photos!", [{ text: "OK" }]);

    return true;
  } catch (error) {
    console.error("Error saving image:", error);

    Alert.alert("Error", "Failed to save the image. Please try again.", [{ text: "OK" }]);

    return false;
  }
};
