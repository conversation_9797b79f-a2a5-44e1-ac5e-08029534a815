import React, { useRef } from "react";
import { SafeAreaView, StatusBar, StyleSheet, View } from "react-native";
import { CaptureableTextCanvas } from "../components/CaptureableTextCanvas";
import { ControlPanel } from "../components/ControlPanel";
import { TextEffectsProvider } from "../components/TextEffectsProvider";
import { captureAndSaveImage } from "../utils/imageCapture";

export default function Index() {
  // Create a ref to the canvas component for capturing
  const canvasRef = useRef(null);

  // Handle saving the image
  const handleSaveImage = async () => {
    if (canvasRef.current) {
      await captureAndSaveImage(canvasRef.current);
    }
  };

  return (
    <TextEffectsProvider>
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />
        <View style={styles.canvasContainer}>
          <CaptureableTextCanvas ref={canvasRef} />
        </View>
        <View style={styles.controlsContainer}>
          <ControlPanel onSaveImage={handleSaveImage} />
        </View>
      </SafeAreaView>
    </TextEffectsProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  canvasContainer: {
    flex: 1,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  controlsContainer: {
    flex: 1,
  },
});
