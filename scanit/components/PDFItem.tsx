import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as MediaLibrary from 'expo-media-library';

interface PDFItemProps {
  pdf: MediaLibrary.Asset;
  onPress: (pdf: MediaLibrary.Asset) => void;
}

const PDFItem: React.FC<PDFItemProps> = ({ pdf, onPress }) => {
  // Format the creation time
  const formatDate = (date: string | number | Date) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress(pdf)}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Ionicons name="document-text" size={32} color="#2196F3" />
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.fileName} numberOfLines={1}>
          {pdf.filename}
        </Text>
        <Text style={styles.dateText}>
          {formatDate(pdf.creationTime)}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={24} color="#757575" />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    marginVertical: 8,
    marginHorizontal: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  iconContainer: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    marginRight: 16,
  },
  infoContainer: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    color: '#757575',
  },
});

export default PDFItem;
