{"expo": {"name": "scanit", "slug": "scanit", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "scanit", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.scanit.app", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to scan documents", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to save scanned documents"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.scanit.app", "edgeToEdgeEnabled": true, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-camera", "expo-media-library", "expo-file-system", "expo-print", "expo-sharing"], "experiments": {"typedRoutes": true}}}