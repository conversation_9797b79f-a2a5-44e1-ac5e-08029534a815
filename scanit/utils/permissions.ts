import { Camera } from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Linking, Platform } from 'react-native';

/**
 * Request camera permission
 * @returns boolean indicating if permission was granted
 */
export const requestCameraPermission = async (): Promise<boolean> => {
  const { status } = await Camera.requestCameraPermissionsAsync();
  
  if (status !== 'granted') {
    Alert.alert(
      'Camera Permission Required',
      'This app needs camera access to scan documents. Please grant camera permission in your device settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => Linking.openSettings() }
      ]
    );
    return false;
  }
  
  return true;
};

/**
 * Request media library permission
 * @returns boolean indicating if permission was granted
 */
export const requestMediaLibraryPermission = async (): Promise<boolean> => {
  const { status } = await MediaLibrary.requestPermissionsAsync();
  
  if (status !== 'granted') {
    Alert.alert(
      'Media Library Permission Required',
      'This app needs access to your media library to save scanned documents. Please grant media library permission in your device settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => Linking.openSettings() }
      ]
    );
    return false;
  }
  
  return true;
};

/**
 * Request all required permissions for the app
 * @returns boolean indicating if all permissions were granted
 */
export const requestAllPermissions = async (): Promise<boolean> => {
  const cameraPermission = await requestCameraPermission();
  const mediaLibraryPermission = await requestMediaLibraryPermission();
  
  return cameraPermission && mediaLibraryPermission;
};
