import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import * as MediaLibrary from 'expo-media-library';

/**
 * Service for handling document scanning and image processing
 */
class ScanService {
  /**
   * Process a captured image to enhance it for document scanning
   * @param imageUri URI of the captured image
   * @returns URI of the processed image
   */
  async processImage(imageUri: string): Promise<string> {
    try {
      // Apply image processing operations to enhance the document
      const processedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          // Convert to grayscale
          { resize: { width: 1200 } }, // Resize to a reasonable resolution
          { auto: { contrast: true, brightness: true } }, // Auto-adjust contrast and brightness
        ],
        { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
      );
      
      return processedImage.uri;
    } catch (error) {
      console.error('Error processing image:', error);
      // Return the original image if processing fails
      return imageUri;
    }
  }
  
  /**
   * Save a processed image to a temporary directory
   * @param imageUri URI of the image to save
   * @returns URI of the saved image
   */
  async saveImageToTemp(imageUri: string): Promise<string> {
    try {
      // Create a temporary directory if it doesn't exist
      const tempDir = `${FileSystem.cacheDirectory}temp/`;
      const tempDirInfo = await FileSystem.getInfoAsync(tempDir);
      
      if (!tempDirInfo.exists) {
        await FileSystem.makeDirectoryAsync(tempDir, { intermediates: true });
      }
      
      // Generate a unique filename
      const fileName = `scan_${new Date().getTime()}.jpg`;
      const destUri = `${tempDir}${fileName}`;
      
      // Copy the image to the temporary directory
      await FileSystem.copyAsync({
        from: imageUri,
        to: destUri,
      });
      
      return destUri;
    } catch (error) {
      console.error('Error saving image to temp:', error);
      throw error;
    }
  }
  
  /**
   * Clear temporary images
   */
  async clearTempImages(): Promise<void> {
    try {
      const tempDir = `${FileSystem.cacheDirectory}temp/`;
      const tempDirInfo = await FileSystem.getInfoAsync(tempDir);
      
      if (tempDirInfo.exists) {
        await FileSystem.deleteAsync(tempDir);
      }
    } catch (error) {
      console.error('Error clearing temp images:', error);
    }
  }
}

export default new ScanService();
