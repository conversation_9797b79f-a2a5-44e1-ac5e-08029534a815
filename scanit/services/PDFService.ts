import * as FileSystem from "expo-file-system";
import * as MediaLibrary from "expo-media-library";
import * as Print from "expo-print";

/**
 * Service for handling PDF creation and management
 */
class PDFService {
  /**
   * Create a PDF from an array of image URIs
   * @param imageUris Array of image URIs to include in the PDF
   * @param documentName Name of the document (without extension)
   * @returns URI of the created PDF file
   */
  async createPDFFromImages(imageUris: string[], documentName: string): Promise<string> {
    try {
      // Create HTML content with the images
      let htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
            <style>
              body {
                font-family: Helvetica, Arial, sans-serif;
                margin: 0;
                padding: 0;
              }
              .page-break {
                page-break-after: always;
              }
              .image-container {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 20px;
              }
              img {
                max-width: 100%;
                max-height: 100vh;
                object-fit: contain;
              }
            </style>
          </head>
          <body>
      `;

      // Convert image URIs to base64 and add to HTML
      for (let i = 0; i < imageUris.length; i++) {
        const imageUri = imageUris[i];

        // Read the image as base64
        const base64Image = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Add the image to the HTML
        htmlContent += `
          <div class="image-container">
            <img src="data:image/jpeg;base64,${base64Image}" />
          </div>
        `;

        // Add page break if not the last image
        if (i < imageUris.length - 1) {
          htmlContent += '<div class="page-break"></div>';
        }
      }

      // Close the HTML
      htmlContent += `
          </body>
        </html>
      `;

      // Generate the PDF using expo-print
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      // Generate the PDF file path in the documents directory
      const pdfFileName = `${documentName}.pdf`;
      const pdfPath = `${FileSystem.documentDirectory}${pdfFileName}`;

      // Copy the PDF to the documents directory
      await FileSystem.copyAsync({
        from: uri,
        to: pdfPath,
      });

      // Save the PDF to the media library
      await this.savePDFToMediaLibrary(pdfPath, pdfFileName);

      return pdfPath;
    } catch (error) {
      console.error("Error creating PDF:", error);
      throw error;
    }
  }

  /**
   * Save a PDF file to the device's media library
   * @param pdfUri URI of the PDF file
   * @param fileName Name of the file (with extension)
   * @returns Asset object of the saved file
   */
  async savePDFToMediaLibrary(pdfUri: string, fileName: string): Promise<MediaLibrary.Asset> {
    try {
      // Save the file to the media library
      const asset = await MediaLibrary.createAssetAsync(pdfUri);

      // Create a "ScanIt" album if it doesn't exist
      const albums = await MediaLibrary.getAlbumsAsync();
      let scanItAlbum = albums.find((album) => album.title === "ScanIt");

      if (!scanItAlbum) {
        scanItAlbum = await MediaLibrary.createAlbumAsync("ScanIt", asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], scanItAlbum, false);
      }

      return asset;
    } catch (error) {
      console.error("Error saving PDF to media library:", error);
      throw error;
    }
  }

  /**
   * Get all saved PDFs from the media library
   * @returns Array of PDF assets
   */
  async getSavedPDFs(): Promise<MediaLibrary.Asset[]> {
    try {
      // Get all albums
      const albums = await MediaLibrary.getAlbumsAsync();
      const scanItAlbum = albums.find((album) => album.title === "ScanIt");

      if (!scanItAlbum) {
        return [];
      }

      // Get all assets in the ScanIt album
      const { assets } = await MediaLibrary.getAssetsAsync({
        album: scanItAlbum,
        mediaType: MediaLibrary.MediaType.document,
      });

      return assets;
    } catch (error) {
      console.error("Error getting saved PDFs:", error);
      return [];
    }
  }

  /**
   * Share a PDF file
   * @param pdfUri URI of the PDF file
   */
  async sharePDF(pdfUri: string): Promise<void> {
    try {
      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();

      if (isAvailable) {
        await Sharing.shareAsync(pdfUri, {
          UTI: ".pdf",
          mimeType: "application/pdf",
        });
      } else {
        console.error("Sharing is not available on this device");
      }
    } catch (error) {
      console.error("Error sharing PDF:", error);
      throw error;
    }
  }

  /**
   * Get image dimensions
   * @param imageUri URI of the image
   * @returns Object containing width and height of the image
   */
  private async getImageDimensions(imageUri: string): Promise<{ width: number; height: number }> {
    try {
      // For simplicity, we'll use standard A4 dimensions (595 x 842 points)
      // In a production app, you would want to get the actual image dimensions
      return {
        width: 595,
        height: 842,
      };

      // Note: To get actual image dimensions, you would need to use
      // a library like react-native-image-size or Image.getSize from react-native
    } catch (error) {
      console.error("Error getting image dimensions:", error);
      // Default to A4 size if there's an error
      return {
        width: 595,
        height: 842,
      };
    }
  }
}

export default new PDFService();
