import React, { useState } from 'react';
import { StyleSheet, View, Text, TextInput, ActivityIndicator, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import * as Haptics from 'expo-haptics';

import Button from '../components/Button';
import PDFService from '../services/PDFService';
import ScanService from '../services/ScanService';

export default function CreatePDFScreen() {
  const { images } = useLocalSearchParams<{ images: string }>();
  const [documentName, setDocumentName] = useState('Scanned Document');
  const [creating, setCreating] = useState(false);
  
  const imageUris = images ? JSON.parse(images) as string[] : [];

  const handleCreatePDF = async () => {
    if (!documentName.trim()) {
      Alert.alert('Error', 'Please enter a document name.');
      return;
    }

    if (imageUris.length === 0) {
      Alert.alert('Error', 'No images to create PDF from.');
      return;
    }

    try {
      setCreating(true);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      // Create the PDF
      const pdfUri = await PDFService.createPDFFromImages(imageUris, documentName);
      
      // Clear temporary images
      await ScanService.clearTempImages();
      
      // Show success message
      Alert.alert(
        'Success',
        'PDF created successfully!',
        [
          { 
            text: 'View PDF', 
            onPress: () => {
              router.push({
                pathname: '/view-pdf',
                params: { uri: pdfUri }
              });
            } 
          },
          { 
            text: 'Back to Home', 
            onPress: () => {
              router.replace('/');
            } 
          }
        ]
      );
    } catch (error) {
      console.error('Error creating PDF:', error);
      Alert.alert('Error', 'Failed to create PDF. Please try again.');
    } finally {
      setCreating(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView style={styles.content}>
        <View style={styles.formContainer}>
          <Text style={styles.label}>Document Name</Text>
          <TextInput
            style={styles.input}
            value={documentName}
            onChangeText={setDocumentName}
            placeholder="Enter document name"
            autoCapitalize="words"
            autoCorrect={false}
          />
          
          <Text style={styles.infoText}>
            Your PDF will include {imageUris.length} {imageUris.length === 1 ? 'page' : 'pages'}.
          </Text>
        </View>
      </ScrollView>
      
      <View style={styles.footer}>
        <Button
          title="Cancel"
          variant="secondary"
          onPress={() => router.back()}
          buttonStyle={styles.footerButton}
          disabled={creating}
        />
        <Button
          title={creating ? 'Creating...' : 'Create PDF'}
          variant="primary"
          onPress={handleCreatePDF}
          buttonStyle={styles.footerButton}
          disabled={creating || !documentName.trim() || imageUris.length === 0}
        />
      </View>
      
      {creating && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Creating PDF...</Text>
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    color: '#757575',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    marginTop: 16,
  },
});
