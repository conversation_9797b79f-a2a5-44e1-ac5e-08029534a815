import { Ionicons } from "@expo/vector-icons";
import * as MediaLibrary from "expo-media-library";
import { router } from "expo-router";
import { useEffect, useState } from "react";
import { FlatList, SafeAreaView, StyleSheet, Text, View } from "react-native";

import Button from "../components/Button";
import PDFItem from "../components/PDFItem";
import PDFService from "../services/PDFService";
import ScanService from "../services/ScanService";
import { requestAllPermissions } from "../utils/permissions";

export default function HomeScreen() {
  const [pdfs, setPdfs] = useState<MediaLibrary.Asset[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load saved PDFs when the screen is focused
    const loadPDFs = async () => {
      setLoading(true);
      // Ensure we have permissions
      const hasPermissions = await requestAllPermissions();

      if (hasPermissions) {
        const savedPDFs = await PDFService.getSavedPDFs();
        setPdfs(savedPDFs);
      }

      setLoading(false);
    };

    loadPDFs();

    // Clean up temporary images when the component mounts
    ScanService.clearTempImages();
  }, []);

  const handleScanPress = () => {
    router.push("/scan");
  };

  const handlePDFPress = (pdf: MediaLibrary.Asset) => {
    router.push({
      pathname: "/view-pdf",
      params: { uri: pdf.uri },
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>ScanIt</Text>
        <Text style={styles.subtitle}>Scan documents and save as PDF</Text>
      </View>

      <View style={styles.content}>
        {pdfs.length > 0 ? (
          <FlatList
            data={pdfs}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => <PDFItem pdf={item} onPress={handlePDFPress} />}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="document-text-outline" size={80} color="#BDBDBD" />
            <Text style={styles.emptyText}>No scanned documents yet</Text>
            <Text style={styles.emptySubtext}>Tap the scan button to start scanning documents</Text>
          </View>
        )}
      </View>

      <View style={styles.footer}>
        <Button
          title="Scan Document"
          variant="primary"
          size="large"
          onPress={handleScanPress}
          buttonStyle={styles.scanButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F5",
  },
  header: {
    padding: 16,
    backgroundColor: "#2196F3",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "white",
  },
  subtitle: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.8)",
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 8,
  },
  listContent: {
    paddingVertical: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#757575",
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: "#9E9E9E",
    textAlign: "center",
    marginTop: 8,
  },
  footer: {
    padding: 16,
    backgroundColor: "white",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scanButton: {
    width: "100%",
  },
});
