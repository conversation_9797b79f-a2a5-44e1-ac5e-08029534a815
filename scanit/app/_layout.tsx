import { Stack } from "expo-router";
import { useEffect } from "react";
import { requestAllPermissions } from "../utils/permissions";

export default function RootLayout() {
  useEffect(() => {
    // Request permissions when the app starts
    requestAllPermissions();
  }, []);

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: "#2196F3",
        },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
      }}
    />
  );
}
