import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Image, Text, TouchableOpacity, ActivityIndicator, Alert, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import Button from '../components/Button';
import DocumentPreview from '../components/DocumentPreview';
import ScanService from '../services/ScanService';

export default function PreviewScreen() {
  const { uri } = useLocalSearchParams<{ uri: string }>();
  const [scannedImages, setScannedImages] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Add the current image to the scanned images array
    if (uri) {
      setScannedImages(prev => [...prev, uri]);
    }
  }, [uri]);

  const handleScanMore = () => {
    router.push('/scan');
  };

  const handleRemoveImage = (index: number) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setScannedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleCreatePDF = () => {
    if (scannedImages.length === 0) {
      Alert.alert('Error', 'Please scan at least one document to create a PDF.');
      return;
    }

    router.push({
      pathname: '/create-pdf',
      params: { images: JSON.stringify(scannedImages) }
    });
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel Scanning',
      'Are you sure you want to cancel? All scanned images will be discarded.',
      [
        { text: 'No', style: 'cancel' },
        { 
          text: 'Yes', 
          onPress: async () => {
            await ScanService.clearTempImages();
            router.back();
          } 
        }
      ]
    );
  };

  if (!uri && scannedImages.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No image to preview</Text>
        <Button
          title="Back to Scan"
          onPress={() => router.push('/scan')}
          variant="primary"
          style={{ marginTop: 20 }}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Preview</Text>
        <Text style={styles.subtitle}>
          {scannedImages.length} {scannedImages.length === 1 ? 'page' : 'pages'} scanned
        </Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.previewContainer}>
          {scannedImages.map((imageUri, index) => (
            <DocumentPreview
              key={`${imageUri}-${index}`}
              imageUri={imageUri}
              onRemove={() => handleRemoveImage(index)}
            />
          ))}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Cancel"
          variant="secondary"
          onPress={handleCancel}
          buttonStyle={styles.footerButton}
        />
        <Button
          title="Scan More"
          variant="secondary"
          onPress={handleScanMore}
          buttonStyle={styles.footerButton}
        />
        <Button
          title="Create PDF"
          variant="primary"
          onPress={handleCreatePDF}
          buttonStyle={styles.footerButton}
          disabled={scannedImages.length === 0}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    padding: 16,
    backgroundColor: '#2196F3',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  previewContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  errorText: {
    fontSize: 18,
    color: '#757575',
    textAlign: 'center',
  },
});
