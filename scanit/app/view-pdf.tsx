import { router, useLocalSearchParams } from "expo-router";
import React from "react";
import { Alert, StyleSheet, Text, View } from "react-native";
import Button from "../components/Button";
import PDFService from "../services/PDFService";

export default function ViewPDFScreen() {
  const { uri } = useLocalSearchParams<{ uri: string }>();

  if (!uri) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No PDF to display</Text>
      </View>
    );
  }

  const handleOpenPDF = async () => {
    try {
      // Share the PDF file using our service
      await PDFService.sharePDF(uri as string);
    } catch (error) {
      console.error("Error opening PDF:", error);
      Alert.alert("Error", "Failed to open PDF");
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>PDF Created Successfully!</Text>
        <Text style={styles.description}>
          Your document has been saved as a PDF. You can view it using your device's PDF viewer.
        </Text>

        <Button
          title="Open PDF"
          variant="primary"
          size="large"
          onPress={handleOpenPDF}
          buttonStyle={styles.button}
        />

        <Button
          title="Back to Home"
          variant="secondary"
          size="medium"
          onPress={() => router.replace("/")}
          buttonStyle={styles.button}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F5",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  content: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
    width: "100%",
    maxWidth: 500,
    alignItems: "center",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 16,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 22,
  },
  button: {
    width: "100%",
    marginVertical: 8,
  },
  errorText: {
    fontSize: 16,
    color: "#F44336",
    textAlign: "center",
  },
});
