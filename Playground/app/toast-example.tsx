import { Button, StyleSheet, Text, View } from "react-native";
import React from "react";
import { useToastStore } from "@/store/useToastStore";

const ToastExample = () => {
  const showToast = useToastStore(
    (state: { showToast: (title: string, message: string, type: "success" | "error") => void }) =>
      state.showToast
  );

  const handleSuccess = () => {
    showToast("Success!", "Operation completed", "success");
  };

  const handleError = () => {
    showToast("Error!", "Something went wrong", "error");
  };
  return (
    <View>
      <View style={{ gap: 10 }}>
        <Button title="Show Success" onPress={handleSuccess} />
        <Button title="Show Error" onPress={handleError} />
      </View>
    </View>
  );
};

export default ToastExample;

const styles = StyleSheet.create({});
