import { Stack } from "expo-router";
import { StatusBar } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import Toast from "../components/ui/Toast";

export default function RootLayout() {
  return (
    <GestureHandlerRootView>
      <StatusBar backgroundColor="#111" />

      <Stack screenOptions={{ headerShown: false }}></Stack>
      <Toast />
    </GestureHandlerRootView>
  );
}
