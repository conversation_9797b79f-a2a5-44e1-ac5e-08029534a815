import BotttomSheet from "@/components/ui/BotttomSheet";
import CustomSwitch from "@/components/ui/Switch";
import Toast from "../components/ui/Toast";
import React, { useState } from "react";
import { Button, Pressable, Text, View } from "react-native";
import { useToastStore } from "../store/useToastStore";
import { Link, useRouter } from "expo-router";
const examples = [
  {
    name: "Toast",
    page: "/toast-example",
    description: "A simple toast notification component.",
  },
  {
    name: "BottomSheet",
    page: "/bottomsheet-example",
    description: "A customizable bottom sheet component.",
  },
  {
    name: "CustomSwitch",
    page: "/custom-switch-example",
    description: "A customizable switch component.",
  },
  {
    name: "PullToRefresh",
    page: "/pull-to-refresh-example",
    description: "A pull-to-refresh component.",
  },
];

const Index = () => {
  return (
    <View style={{ flex: 1, alignItems: "center", backgroundColor: "#111", gap: 30 }}>
      {examples.map((example) => (
        <View key={example.name} style={{ gap: 10 }}>
          <Link
            href={example.page}
            style={{ padding: 10, backgroundColor: "#222", borderRadius: 5 }}
            asChild>
            <Pressable>
              <Text style={{ color: "#fff" }}>{example.name}</Text>
            </Pressable>
          </Link>
          <Text style={{ color: "#fff" }}>{example.description}</Text>
        </View>
      ))}
    </View>
  );
};

export default Index;
