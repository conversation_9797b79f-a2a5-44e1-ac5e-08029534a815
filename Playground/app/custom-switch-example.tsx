import { StyleSheet, Text, View } from "react-native";
import React, { useState } from "react";
import CustomSwitch from "@/components/ui/Switch";

const CustomSwitchExample = () => {
  const [isEnabled, setIsEnabled] = useState(false);

  return (
    <View>
      <CustomSwitch initialValue={isEnabled} onToggle={setIsEnabled} />
    </View>
  );
};

export default CustomSwitchExample;

const styles = StyleSheet.create({});
