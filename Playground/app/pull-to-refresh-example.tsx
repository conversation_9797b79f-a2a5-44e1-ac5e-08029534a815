import { PullToRefreshFlatList } from "@/components/ui/CustomPullToRefresh";
import React, { useState } from "react";
import { View, Text } from "react-native";

const PullToRefreshExample = () => {
  const [data, setData] = useState<number[]>([1, 2, 3, 4, 5]);

  const onRefresh = async () => {
    await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate API call
    setData((prev) => [...prev, prev.length + 1]);
  };

  return (
    <PullToRefreshFlatList
      data={data}
      keyExtractor={(item, index) => index.toString()}
      onRefresh={onRefresh}
      renderItem={({ item }) => (
        <View style={{ padding: 20, borderBottomWidth: 1, borderColor: "#ddd" }}>
          <Text>{item}</Text>
        </View>
      )}
    />
  );
};

export default PullToRefreshExample;
