import { create } from "zustand";

type ToastType = "success" | "error";

interface ToastState {
  visible: boolean;
  title: string;
  message: string;
  type: ToastType;
  showToast: (title: string, message: string, type: ToastType) => void;
  hideToast: () => void;
}

export const useToastStore = create<ToastState>((set) => ({
  visible: false,
  title: "",
  message: "",
  type: "success",
  showToast: (title: string, message: string, type: ToastType) =>
    set({ visible: true, title, message, type }),
  hideToast: () => set({ visible: false }),
}));
