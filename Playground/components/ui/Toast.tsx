import React, { useEffect } from "react";
import { StyleSheet, Text, View } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from "react-native-reanimated";
import { useToastStore } from "../../store/useToastStore";

export interface ToastHandle {
  show: (params: { title: string; message: string; type: "success" | "error" }) => void;
  hide: () => void;
}

const Toast = () => {
  const { visible, title, message, type, hideToast } = useToastStore();
  const translateY = useSharedValue(-100);

  useEffect(() => {
    if (visible) {
      // Slide in
      translateY.value = withTiming(0, { duration: 300 });

      // Auto hide after 3 seconds
      const timer = setTimeout(() => {
        hideWithAnimation();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [visible]);

  const hideWithAnimation = () => {
    translateY.value = withTiming(-100, { duration: 300 }, (isFinished) => {
      if (isFinished) runOnJS(hideToast)();
    });
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    backgroundColor: type === "success" ? "#4CAF50" : "#F44336",
  }));

  if (!visible) return null;

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    padding: 16,
    zIndex: 999,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  content: {
    marginTop: 20,
  },
  title: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  message: {
    color: "#fff",
    fontSize: 14,
  },
});

export default Toast;
