import React, { useState } from "react";
import { Pressable, View, StyleSheet } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolateColor,
  withTiming,
} from "react-native-reanimated";
import * as Haptics from "expo-haptics";

const SWITCH_WIDTH = 60;
const SWITCH_HEIGHT = 30;
const THUMB_SIZE = 26;

const CustomSwitch = ({ initialValue = false, onToggle }) => {
  const [isOn, setIsOn] = useState(initialValue);
  const progress = useSharedValue(initialValue ? 1 : 0);

  const toggleSwitch = () => {
    const newValue = !isOn;
    setIsOn(newValue);
    // progress.value = withSpring(newValue ? 1 : 0, { damping: 10, stiffness: 100 });
    progress.value = withTiming(newValue ? 1 : 0, { duration: 200 });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (onToggle) onToggle(newValue);
  };

  const animatedThumbStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: progress.value * (SWITCH_WIDTH - THUMB_SIZE - 4) + 2 }],
      backgroundColor: interpolateColor(progress.value, [0, 1], ["#ccc", "#fff"]),
    };
  });

  const animatedBackgroundStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(progress.value, [0, 1], ["#ddd", "#4CAF50"]),
    };
  });

  return (
    <Pressable onPress={toggleSwitch} style={styles.container}>
      <Animated.View style={[styles.switchBackground, animatedBackgroundStyle]}>
        <Animated.View style={[styles.thumb, animatedThumbStyle]} />
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
  },
  switchBackground: {
    width: SWITCH_WIDTH,
    height: SWITCH_HEIGHT,
    borderRadius: SWITCH_HEIGHT / 2,
    justifyContent: "center",
    paddingHorizontal: 2,
  },
  thumb: {
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    position: "absolute",
    top: 2,
  },
});

export default CustomSwitch;
