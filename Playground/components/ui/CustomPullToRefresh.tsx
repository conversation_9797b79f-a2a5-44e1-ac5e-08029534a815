import React, { useState } from "react";
import {
  FlatList,
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  ListRenderItemInfo,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from "react-native-reanimated";
import { PanGestureHandler, GestureHandlerRootView } from "react-native-gesture-handler";
import { GestureHandlerGestureEvent } from "react-native-gesture-handler";

interface Props<T> {
  data: T[];
  renderItem: (info: ListRenderItemInfo<T>) => React.ReactElement | null;
  keyExtractor: (item: T, index: number) => string;
  onRefresh: () => void | Promise<void>;
  refreshThreshold?: number;
}

export function PullToRefreshFlatList<T>(props: Props<T>) {
  const { data, renderItem, keyExtractor, onRefresh, refreshThreshold = 80 } = props;

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [scrollOffset, setScrollOffset] = useState(0);

  const pullDistance = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: pullDistance.value }],
    };
  });

  const indicatorAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: pullDistance.value > 0 ? pullDistance.value / 2 : 0 }],
      opacity: pullDistance.value > 0 ? 1 : 0,
    };
  });

  const handleGesture = (event: GestureHandlerGestureEvent["nativeEvent"]) => {
    if (scrollOffset <= 0 && !isRefreshing) {
      if (event.translationY > 0) {
        pullDistance.value = event.translationY * 0.5; // dampen pull
      }
    }
  };

  const handleGestureEnd = (event: GestureHandlerGestureEvent["nativeEvent"]) => {
    if (scrollOffset <= 0 && !isRefreshing) {
      if (pullDistance.value >= refreshThreshold) {
        // Trigger refresh
        pullDistance.value = withTiming(refreshThreshold, { duration: 100 });
        runOnJS(triggerRefresh)();
      } else {
        // Not enough pull, reset
        pullDistance.value = withTiming(0, { duration: 200 });
      }
    }
  };

  const triggerRefresh = async () => {
    setIsRefreshing(true);
    await onRefresh();
    pullDistance.value = withTiming(0, { duration: 300 });
    setIsRefreshing(false);
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PanGestureHandler
        onGestureEvent={(e) => handleGesture(e.nativeEvent)}
        onEnded={(e) => handleGestureEnd(e.nativeEvent)}>
        <Animated.View style={[styles.container, animatedStyle]}>
          <Animated.View style={[styles.indicatorContainer, indicatorAnimatedStyle]}>
            {isRefreshing ? (
              <ActivityIndicator size="small" color="#333" />
            ) : (
              <Text style={styles.indicatorText}>
                {pullDistance.value >= refreshThreshold ? "Release to refresh" : "Pull to refresh"}
              </Text>
            )}
          </Animated.View>

          <FlatList
            data={data}
            keyExtractor={keyExtractor}
            renderItem={renderItem}
            onScroll={(e: NativeSyntheticEvent<NativeScrollEvent>) => {
              setScrollOffset(e.nativeEvent.contentOffset.y);
            }}
            scrollEventThrottle={16}
            alwaysBounceVertical={false}
            bounces={false}
            overScrollMode="never"
            contentContainerStyle={{ paddingTop: 0 }}
          />
        </Animated.View>
      </PanGestureHandler>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  indicatorContainer: {
    height: 60,
    alignItems: "center",
    justifyContent: "center",
  },
  indicatorText: {
    fontSize: 14,
    color: "#666",
  },
});
