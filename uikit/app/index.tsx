import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { 
  BottomSheet, 
  Checkbox, 
  CheckboxGroup, 
  Dropdown, 
  Loader, 
  RadioButton, 
  RadioGroup, 
  Spinner, 
  Switch 
} from "../components";
import type { CheckboxOption } from "../components/Checkbox";
import type { DropdownItem } from "../components/Dropdown";
import type { RadioOption } from "../components/RadioButton";
import { colors, fontSizes, fontWeights, spacing } from "../theme";

export default function Index() {
  // State for components
  const [switchValue, setSwitchValue] = useState(false);
  const [selectedDropdownValue, setSelectedDropdownValue] = useState<string | number>("");
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const [isLoaderVisible, setIsLoaderVisible] = useState(false);
  const [loaderType, setLoaderType] = useState<"spinner" | "dots" | "pulse" | "native">("spinner");
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [indeterminateValue, setIndeterminateValue] = useState(true);
  const [selectedRadioValue, setSelectedRadioValue] = useState<string | number>("radio1");
  const [selectedCheckboxValues, setSelectedCheckboxValues] = useState<(string | number)[]>(["check1"]);

  // Dropdown items
  const dropdownItems: DropdownItem[] = [
    {
      label: "Option 1",
      value: "option1",
      icon: <Ionicons name="star" size={20} color={colors.primary} />,
    },
    {
      label: "Option 2",
      value: "option2",
      icon: <Ionicons name="heart" size={20} color={colors.error} />,
    },
    {
      label: "Option 3",
      value: "option3",
      icon: <Ionicons name="checkmark-circle" size={20} color={colors.success} />,
    },
    {
      label: "Option 4",
      value: "option4",
      icon: <Ionicons name="alert-circle" size={20} color={colors.warning} />,
    },
  ];

  // Radio options
  const radioOptions: RadioOption[] = [
    { label: "Option 1", value: "radio1" },
    { label: "Option 2", value: "radio2" },
    { label: "Option 3 (Disabled)", value: "radio3", disabled: true },
  ];

  // Checkbox options
  const checkboxOptions: CheckboxOption[] = [
    { label: "Option 1", value: "check1" },
    { label: "Option 2", value: "check2" },
    { label: "Option 3 (Disabled)", value: "check3", disabled: true },
  ];

  // Show loader with different types
  const showLoader = (type: "spinner" | "dots" | "pulse" | "native") => {
    setLoaderType(type);
    setIsLoaderVisible(true);

    // Auto-hide loader after 3 seconds
    setTimeout(() => {
      setIsLoaderVisible(false);
    }, 3000);
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={styles.title}>UI Kit Components</Text>

      {/* Spinners */}
      <SectionTitle title="Spinners" />
      <View style={styles.componentRow}>
        <Spinner size={30} color="primary" />
        <Spinner size={30} color="secondary" />
        <Spinner size={30} color="success" />
        <Spinner size={30} color="error" />
      </View>

      {/* Switches */}
      <SectionTitle title="Switches" />
      <View style={styles.componentRow}>
        <View style={styles.switchContainer}>
          <Text style={styles.componentLabel}>Default</Text>
          <Switch value={switchValue} onValueChange={setSwitchValue} />
        </View>

        <View style={styles.switchContainer}>
          <Text style={styles.componentLabel}>Small</Text>
          <Switch value={switchValue} onValueChange={setSwitchValue} size="small" />
        </View>

        <View style={styles.switchContainer}>
          <Text style={styles.componentLabel}>Large</Text>
          <Switch
            value={switchValue}
            onValueChange={setSwitchValue}
            size="large"
            activeColor="success"
          />
        </View>

        <View style={styles.switchContainer}>
          <Text style={styles.componentLabel}>Disabled</Text>
          <Switch value={true} onValueChange={() => {}} disabled={true} />
        </View>
      </View>

      {/* Checkboxes */}
      <SectionTitle title="Checkboxes" />
      <View style={styles.checkboxContainer}>
        <View style={styles.checkboxRow}>
          <Checkbox 
            checked={checkboxValue} 
            onPress={() => setCheckboxValue(!checkboxValue)} 
            label="Default Checkbox" 
          />
        </View>
        <View style={styles.checkboxRow}>
          <Checkbox 
            checked={true} 
            onPress={() => {}} 
            label="Checked Checkbox" 
            activeColor="success" 
          />
        </View>
        <View style={styles.checkboxRow}>
          <Checkbox 
            checked={indeterminateValue} 
            onPress={() => setIndeterminateValue(!indeterminateValue)} 
            label="Indeterminate Checkbox" 
            indeterminate={true} 
            activeColor="secondary" 
          />
        </View>
        <View style={styles.checkboxRow}>
          <Checkbox 
            checked={true} 
            onPress={() => {}} 
            label="Disabled Checkbox" 
            disabled={true} 
          />
        </View>
      </View>

      {/* Checkbox Group */}
      <SectionTitle title="Checkbox Group" />
      <CheckboxGroup
        options={checkboxOptions}
        values={selectedCheckboxValues}
        onChange={setSelectedCheckboxValues}
        activeColor="primary"
      />

      {/* Radio Buttons */}
      <SectionTitle title="Radio Buttons" />
      <View style={styles.radioContainer}>
        <View style={styles.radioRow}>
          <RadioButton 
            selected={selectedRadioValue === "radio1"} 
            onPress={() => setSelectedRadioValue("radio1")} 
            label="Default Radio Button" 
          />
        </View>
        <View style={styles.radioRow}>
          <RadioButton 
            selected={true} 
            onPress={() => {}} 
            label="Selected Radio Button" 
            activeColor="success" 
          />
        </View>
        <View style={styles.radioRow}>
          <RadioButton 
            selected={false} 
            onPress={() => {}} 
            label="Disabled Radio Button" 
            disabled={true} 
          />
        </View>
      </View>

      {/* Radio Group */}
      <SectionTitle title="Radio Group" />
      <RadioGroup
        options={radioOptions}
        value={selectedRadioValue}
        onChange={setSelectedRadioValue}
        activeColor="primary"
      />

      {/* Dropdown */}
      <SectionTitle title="Dropdown" />
      <View style={styles.dropdownContainer}>
        <Dropdown
          items={dropdownItems}
          selectedValue={selectedDropdownValue}
          onValueChange={setSelectedDropdownValue}
          placeholder="Select an option"
          searchable={true}
        />
      </View>

      {/* Loaders */}
      <SectionTitle title="Loaders" />
      <View style={styles.componentRow}>
        <TouchableOpacity style={styles.button} onPress={() => showLoader("spinner")}>
          <Text style={styles.buttonText}>Spinner</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={() => showLoader("dots")}>
          <Text style={styles.buttonText}>Dots</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={() => showLoader("pulse")}>
          <Text style={styles.buttonText}>Pulse</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={() => showLoader("native")}>
          <Text style={styles.buttonText}>Native</Text>
        </TouchableOpacity>
      </View>

      {/* Bottom Sheet */}
      <SectionTitle title="Bottom Sheet" />
      <TouchableOpacity
        style={[styles.button, styles.fullWidthButton]}
        onPress={() => setIsBottomSheetVisible(true)}>
        <Text style={styles.buttonText}>Show Bottom Sheet</Text>
      </TouchableOpacity>

      {/* Loader Modal */}
      <Loader
        visible={isLoaderVisible}
        type={loaderType}
        text={`${loaderType.charAt(0).toUpperCase() + loaderType.slice(1)} Loader`}
        fullScreen={true}
      />

      {/* Bottom Sheet */}
      <BottomSheet
        isVisible={isBottomSheetVisible}
        onClose={() => setIsBottomSheetVisible(false)}
        title="Bottom Sheet"
        height="50%"
        enableDragIndicator={true}
        enableBackdropDismiss={true}>
        <View style={styles.bottomSheetContent}>
          <Text style={styles.bottomSheetText}>
            This is a bottom sheet component that can be customized with different heights, snap
            points, and other options.
          </Text>

          <View style={styles.bottomSheetSection}>
            <Text style={styles.bottomSheetSectionTitle}>Features:</Text>
            <Text style={styles.bottomSheetFeature}>• Draggable with indicator</Text>
            <Text style={styles.bottomSheetFeature}>• Customizable height</Text>
            <Text style={styles.bottomSheetFeature}>• Backdrop dismiss</Text>
            <Text style={styles.bottomSheetFeature}>• Snap points</Text>
            <Text style={styles.bottomSheetFeature}>• Custom styling</Text>
          </View>

          <TouchableOpacity
            style={[styles.button, styles.fullWidthButton, styles.closeButton]}
            onPress={() => setIsBottomSheetVisible(false)}>
            <Text style={styles.buttonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </BottomSheet>
    </ScrollView>
  );
}

// Section Title Component
const SectionTitle = ({ title }: { title: string }) => (
  <Text style={styles.sectionTitle}>{title}</Text>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  contentContainer: {
    padding: spacing.md,
    paddingBottom: spacing.xxl,
  },
  title: {
    fontSize: fontSizes.xxl,
    fontWeight: fontWeights.bold,
    color: colors.textPrimary,
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.semibold,
    color: colors.textPrimary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  componentRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: spacing.md,
  },
  switchContainer: {
    alignItems: "center",
    marginVertical: spacing.sm,
    width: "22%",
  },
  componentLabel: {
    fontSize: fontSizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  checkboxContainer: {
    marginBottom: spacing.md,
  },
  checkboxRow: {
    marginBottom: spacing.sm,
  },
  radioContainer: {
    marginBottom: spacing.md,
  },
  radioRow: {
    marginBottom: spacing.sm,
  },
  dropdownContainer: {
    marginBottom: spacing.md,
  },
  button: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginVertical: spacing.xs,
    minWidth: "22%",
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSizes.md,
    fontWeight: fontWeights.medium,
  },
  fullWidthButton: {
    width: "100%",
  },
  bottomSheetContent: {
    padding: spacing.md,
  },
  bottomSheetText: {
    fontSize: fontSizes.md,
    color: colors.textPrimary,
    marginBottom: spacing.md,
    lineHeight: 22,
  },
  bottomSheetSection: {
    marginBottom: spacing.lg,
  },
  bottomSheetSectionTitle: {
    fontSize: fontSizes.md,
    fontWeight: fontWeights.semibold,
    color: colors.textPrimary,
    marginBottom: spacing.sm,
  },
  bottomSheetFeature: {
    fontSize: fontSizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  closeButton: {
    backgroundColor: colors.error,
    marginTop: spacing.md,
  },
});
