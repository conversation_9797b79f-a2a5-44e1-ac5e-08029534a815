import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  FlatList,
  TextInput,
  TouchableWithoutFeedback,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, ColorName, fontSizes, fontWeights, spacing } from '../../theme';

export interface DropdownItem {
  label: string;
  value: string | number;
  icon?: React.ReactNode;
}

interface DropdownProps {
  items: DropdownItem[];
  selectedValue?: string | number;
  onValueChange: (value: string | number) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: ViewStyle;
  dropdownStyle?: ViewStyle;
  textStyle?: TextStyle;
  placeholderStyle?: TextStyle;
  itemTextStyle?: TextStyle;
  iconColor?: ColorName;
  dropdownIconName?: keyof typeof Ionicons.glyphMap;
  maxHeight?: number;
  searchable?: boolean;
  renderCustomItem?: (item: DropdownItem, isSelected: boolean) => React.ReactNode;
}

export const Dropdown: React.FC<DropdownProps> = ({
  items,
  selectedValue,
  onValueChange,
  placeholder = 'Select an option',
  disabled = false,
  style,
  dropdownStyle,
  textStyle,
  placeholderStyle,
  itemTextStyle,
  iconColor = 'primary',
  dropdownIconName = 'chevron-down',
  maxHeight = 300,
  searchable = false,
  renderCustomItem,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [dropdownTop, setDropdownTop] = useState(0);
  const [dropdownLeft, setDropdownLeft] = useState(0);
  const [dropdownWidth, setDropdownWidth] = useState(0);
  const [filteredItems, setFilteredItems] = useState(items);
  const [searchText, setSearchText] = useState('');
  const DropdownButton = useRef<View>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const dropdownRef = useRef<View>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleOutsideClick = () => {
      if (isVisible) {
        hideDropdown();
      }
    };

    // Add event listener for outside clicks
    if (isVisible) {
      document.addEventListener('click', handleOutsideClick);
    }

    return () => {
      document.removeEventListener('click', handleOutsideClick);
    };
  }, [isVisible]);

  useEffect(() => {
    setFilteredItems(items);
  }, [items]);

  const toggleDropdown = () => {
    if (disabled) return;
    
    if (isVisible) {
      hideDropdown();
    } else {
      openDropdown();
    }
  };

  const openDropdown = () => {
    DropdownButton.current?.measure((fx, fy, width, height, px, py) => {
      const screenHeight = Dimensions.get('window').height;
      const screenWidth = Dimensions.get('window').width;
      const dropdownHeight = Math.min(maxHeight, filteredItems.length * 50);
      
      // Check if dropdown should open upward or downward
      const spaceBelow = screenHeight - py - height;
      const openBelow = spaceBelow >= dropdownHeight || spaceBelow >= py;
      
      // Ensure dropdown doesn't go off screen horizontally
      const rightOverflow = (px + width) - screenWidth;
      const adjustedLeft = rightOverflow > 0 ? px - rightOverflow : px;
      
      setDropdownTop(openBelow ? py + height : py - dropdownHeight);
      setDropdownLeft(adjustedLeft);
      setDropdownWidth(width);
      setIsVisible(true);
      
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  };

  const hideDropdown = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
    });
  };

  const onItemPress = (item: DropdownItem) => {
    onValueChange(item.value);
    hideDropdown();
  };

  const getSelectedItem = () => {
    return items.find(item => item.value === selectedValue);
  };

  const selectedItem = getSelectedItem();

  const handleSearch = (text: string) => {
    setSearchText(text);
    const filtered = items.filter(item => 
      item.label.toLowerCase().includes(text.toLowerCase())
    );
    setFilteredItems(filtered);
  };

  const renderItem = ({ item }: { item: DropdownItem }) => {
    const isSelected = item.value === selectedValue;
    
    if (renderCustomItem) {
      return (
        <TouchableOpacity
          style={[styles.item, isSelected && styles.selectedItem]}
          onPress={() => onItemPress(item)}
        >
          {renderCustomItem(item, isSelected)}
        </TouchableOpacity>
      );
    }
    
    return (
      <TouchableOpacity
        style={[styles.item, isSelected && styles.selectedItem]}
        onPress={() => onItemPress(item)}
      >
        <View style={styles.itemContent}>
          {item.icon && <View style={styles.iconContainer}>{item.icon}</View>}
          <Text
            style={[
              styles.itemText,
              isSelected && styles.selectedItemText,
              itemTextStyle,
            ]}
          >
            {item.label}
          </Text>
        </View>
        {isSelected && (
          <Ionicons name="checkmark" size={20} color={colors.primary} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        ref={DropdownButton as any}
        style={[
          styles.button,
          disabled && styles.buttonDisabled,
        ]}
        onPress={toggleDropdown}
        disabled={disabled}
      >
        <Text
          style={[
            styles.buttonText,
            !selectedItem && styles.placeholderText,
            !selectedItem && placeholderStyle,
            selectedItem && textStyle,
          ]}
          numberOfLines={1}
        >
          {selectedItem ? selectedItem.label : placeholder}
        </Text>
        <Ionicons
          name={isVisible ? 'chevron-up' : dropdownIconName}
          size={20}
          color={disabled ? colors.gray : colors[iconColor]}
        />
      </TouchableOpacity>

      {isVisible && (
        <View style={styles.dropdownContainer} ref={dropdownRef}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.dropdown,
                {
                  top: dropdownTop,
                  left: dropdownLeft,
                  width: dropdownWidth,
                  maxHeight,
                  opacity: fadeAnim,
                  zIndex: 1000,
                },
                dropdownStyle,
              ]}
            >
              {searchable && (
                <View style={styles.searchContainer}>
                  <Ionicons name="search" size={20} color={colors.gray} style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search..."
                    value={searchText}
                    onChangeText={handleSearch}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  {searchText.length > 0 && (
                    <TouchableOpacity onPress={() => handleSearch('')}>
                      <Ionicons name="close-circle" size={20} color={colors.gray} />
                    </TouchableOpacity>
                  )}
                </View>
              )}
              <FlatList
                data={filteredItems}
                renderItem={renderItem}
                keyExtractor={(item) => item.value.toString()}
                showsVerticalScrollIndicator={true}
                style={styles.list}
                keyboardShouldPersistTaps="handled"
              />
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 100,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 50,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
  },
  buttonDisabled: {
    backgroundColor: colors.extraLightGray,
    borderColor: colors.lightGray,
  },
  buttonText: {
    flex: 1,
    fontSize: fontSizes.md,
    color: colors.textPrimary,
  },
  placeholderText: {
    color: colors.textTertiary,
  },
  dropdownContainer: {
    position: 'absolute',
    width: '100%',
    zIndex: 1000,
  },
  dropdown: {
    position: 'absolute',
    backgroundColor: colors.white,
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  list: {
    flex: 1,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  selectedItem: {
    backgroundColor: colors.extraLightGray,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: spacing.sm,
  },
  itemText: {
    fontSize: fontSizes.md,
    color: colors.textPrimary,
  },
  selectedItemText: {
    fontWeight: fontWeights.medium,
    color: colors.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.extraLightGray,
  },
  searchIcon: {
    marginRight: spacing.xs,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: fontSizes.md,
  },
});

export default Dropdown;
