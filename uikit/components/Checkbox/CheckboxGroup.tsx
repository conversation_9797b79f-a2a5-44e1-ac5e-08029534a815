import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import Checkbox from './Checkbox';
import { ColorName, spacing } from '../../theme';

export interface CheckboxOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface CheckboxGroupProps {
  options: CheckboxOption[];
  values: (string | number)[];
  onChange: (values: (string | number)[]) => void;
  direction?: 'vertical' | 'horizontal';
  size?: number;
  activeColor?: ColorName;
  inactiveColor?: ColorName;
  checkColor?: ColorName;
  style?: ViewStyle;
  itemStyle?: ViewStyle;
  spacing?: number;
}

export const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  options,
  values,
  onChange,
  direction = 'vertical',
  size,
  activeColor,
  inactiveColor,
  checkColor,
  style,
  itemStyle,
  spacing: itemSpacing = spacing.md,
}) => {
  const handleToggle = (value: string | number) => {
    if (values.includes(value)) {
      onChange(values.filter((v) => v !== value));
    } else {
      onChange([...values, value]);
    }
  };

  return (
    <View
      style={[
        styles.container,
        {
          flexDirection: direction === 'vertical' ? 'column' : 'row',
          flexWrap: direction === 'horizontal' ? 'wrap' : 'nowrap',
        },
        style,
      ]}
    >
      {options.map((option) => (
        <View
          key={option.value.toString()}
          style={[
            {
              marginBottom: direction === 'vertical' ? itemSpacing : 0,
              marginRight: direction === 'horizontal' ? itemSpacing : 0,
            },
            itemStyle,
          ]}
        >
          <Checkbox
            checked={values.includes(option.value)}
            onPress={() => handleToggle(option.value)}
            label={option.label}
            disabled={option.disabled}
            size={size}
            activeColor={activeColor}
            inactiveColor={inactiveColor}
            checkColor={checkColor}
          />
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
  },
});

export default CheckboxGroup;
