import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  Easing,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, ColorName, spacing, fontSizes, fontWeights } from '../../theme';

interface CheckboxProps {
  checked: boolean;
  onPress: () => void;
  label?: string;
  disabled?: boolean;
  size?: number;
  activeColor?: ColorName;
  inactiveColor?: ColorName;
  checkColor?: ColorName;
  style?: ViewStyle;
  labelStyle?: TextStyle;
  iconName?: keyof typeof Ionicons.glyphMap;
  position?: 'left' | 'right';
  indeterminate?: boolean;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onPress,
  label,
  disabled = false,
  size = 24,
  activeColor = 'primary',
  inactiveColor = 'lightGray',
  checkColor = 'white',
  style,
  labelStyle,
  iconName = 'checkmark',
  position = 'left',
  indeterminate = false,
}) => {
  // Animation for the check mark
  const scaleValue = React.useRef(new Animated.Value(checked ? 1 : 0)).current;

  React.useEffect(() => {
    Animated.timing(scaleValue, {
      toValue: checked ? 1 : 0,
      duration: 150,
      easing: Easing.bezier(0.0, 0.0, 0.2, 1),
      useNativeDriver: true,
    }).start();
  }, [checked, scaleValue]);

  const handlePress = () => {
    if (!disabled) {
      onPress();
    }
  };

  const renderCheckbox = () => (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={handlePress}
      disabled={disabled}
      style={[
        styles.checkbox,
        {
          width: size,
          height: size,
          borderRadius: size / 6,
          backgroundColor: checked ? colors[activeColor] : 'transparent',
          borderColor: checked ? colors[activeColor] : colors[inactiveColor],
          opacity: disabled ? 0.5 : 1,
        },
        style,
      ]}
    >
      {checked && !indeterminate && (
        <Animated.View
          style={{
            transform: [{ scale: scaleValue }],
          }}
        >
          <Ionicons
            name={iconName}
            size={size * 0.7}
            color={colors[checkColor]}
          />
        </Animated.View>
      )}
      {indeterminate && (
        <Animated.View
          style={{
            transform: [{ scale: scaleValue }],
          }}
        >
          <Ionicons
            name="remove"
            size={size * 0.7}
            color={colors[checkColor]}
          />
        </Animated.View>
      )}
    </TouchableOpacity>
  );

  if (!label) {
    return renderCheckbox();
  }

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={handlePress}
      disabled={disabled}
      style={[
        styles.container,
        {
          flexDirection: position === 'left' ? 'row' : 'row-reverse',
          opacity: disabled ? 0.5 : 1,
        },
      ]}
    >
      {renderCheckbox()}
      <Text
        style={[
          styles.label,
          {
            marginLeft: position === 'left' ? spacing.sm : 0,
            marginRight: position === 'right' ? spacing.sm : 0,
            color: disabled ? colors.textTertiary : colors.textPrimary,
          },
          labelStyle,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  label: {
    fontSize: fontSizes.md,
    fontWeight: fontWeights.regular,
  },
});

export default Checkbox;
