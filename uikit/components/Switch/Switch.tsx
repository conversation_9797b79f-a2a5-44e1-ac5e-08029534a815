import React, { useEffect } from 'react';
import { 
  View, 
  TouchableWithoutFeedback, 
  StyleSheet, 
  Animated, 
  ViewStyle 
} from 'react-native';
import { colors, ColorName } from '../../theme';

interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
  activeColor?: ColorName;
  inactiveColor?: ColorName;
  thumbColor?: ColorName;
  style?: ViewStyle;
  size?: 'small' | 'medium' | 'large';
}

export const Switch: React.FC<SwitchProps> = ({
  value,
  onValueChange,
  disabled = false,
  activeColor = 'primary',
  inactiveColor = 'lightGray',
  thumbColor = 'white',
  style,
  size = 'medium',
}) => {
  const translateX = React.useRef(new Animated.Value(0)).current;
  
  const sizeMap = {
    small: { width: 40, height: 24, thumbSize: 20 },
    medium: { width: 50, height: 30, thumbSize: 26 },
    large: { width: 60, height: 36, thumbSize: 32 },
  };
  
  const { width, height, thumbSize } = sizeMap[size];
  const trackPadding = (height - thumbSize) / 2;
  const thumbPosition = width - thumbSize - trackPadding * 2;

  useEffect(() => {
    Animated.timing(translateX, {
      toValue: value ? thumbPosition : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [value, translateX, thumbPosition]);

  const handlePress = () => {
    if (!disabled) {
      onValueChange(!value);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={handlePress} disabled={disabled}>
      <View
        style={[
          styles.container,
          {
            width,
            height,
            backgroundColor: value ? colors[activeColor] : colors[inactiveColor],
            opacity: disabled ? 0.5 : 1,
            borderRadius: height / 2,
          },
          style,
        ]}
      >
        <Animated.View
          style={[
            styles.thumb,
            {
              width: thumbSize,
              height: thumbSize,
              backgroundColor: colors[thumbColor],
              transform: [{ translateX }],
              margin: trackPadding,
            },
          ]}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
  },
  thumb: {
    borderRadius: 999,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
});

export default Switch;
