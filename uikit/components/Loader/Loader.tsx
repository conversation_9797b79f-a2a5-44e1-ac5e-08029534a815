import React from 'react';
import { View, Text, StyleSheet, Modal, ActivityIndicator, ViewStyle, TextStyle, Animated } from 'react-native';
import { colors, ColorName, fontSizes, fontWeights } from '../../theme';
import Spinner from '../Spinner';

type LoaderType = 'spinner' | 'dots' | 'pulse' | 'native';

interface LoaderProps {
  visible: boolean;
  type?: LoaderType;
  text?: string;
  color?: ColorName;
  size?: number;
  fullScreen?: boolean;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
}

export const Loader: React.FC<LoaderProps> = ({
  visible,
  type = 'spinner',
  text,
  color = 'primary',
  size = 40,
  fullScreen = false,
  containerStyle,
  textStyle,
}) => {
  const renderLoader = () => {
    switch (type) {
      case 'spinner':
        return <Spinner size={size} color={color} />;
      case 'native':
        return <ActivityIndicator size={size} color={colors[color]} />;
      case 'dots':
        return <DotsLoader size={size} color={color} />;
      case 'pulse':
        return <PulseLoader size={size} color={color} />;
      default:
        return <Spinner size={size} color={color} />;
    }
  };

  const content = (
    <View
      style={[
        styles.container,
        fullScreen ? styles.fullScreen : null,
        containerStyle,
      ]}
    >
      <View style={styles.loaderContainer}>
        {renderLoader()}
        {text && (
          <Text
            style={[
              styles.text,
              { color: colors[color] },
              textStyle,
            ]}
          >
            {text}
          </Text>
        )}
      </View>
    </View>
  );

  if (fullScreen) {
    return (
      <Modal
        transparent
        animationType="fade"
        visible={visible}
        onRequestClose={() => {}}
      >
        {content}
      </Modal>
    );
  }

  return visible ? content : null;
};

// Dots Loader Component
const DotsLoader: React.FC<{ size: number; color: ColorName }> = ({ size, color }) => {
  const [animationValue] = React.useState([
    React.useRef(new Animated.Value(0)).current,
    React.useRef(new Animated.Value(0)).current,
    React.useRef(new Animated.Value(0)).current,
  ]);

  React.useEffect(() => {
    const animations = animationValue.map((value, index) => {
      return Animated.sequence([
        Animated.delay(index * 150),
        Animated.loop(
          Animated.sequence([
            Animated.timing(value, {
              toValue: 1,
              duration: 500,
              useNativeDriver: true,
            }),
            Animated.timing(value, {
              toValue: 0,
              duration: 500,
              useNativeDriver: true,
            }),
          ])
        ),
      ]);
    });

    Animated.parallel(animations).start();

    return () => {
      animations.forEach((anim) => anim.stop());
    };
  }, [animationValue]);

  return (
    <View style={styles.dotsContainer}>
      {animationValue.map((value, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              width: size / 3,
              height: size / 3,
              backgroundColor: colors[color],
              marginHorizontal: size / 10,
              opacity: value,
              transform: [
                {
                  scale: value.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.7, 1],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

// Pulse Loader Component
const PulseLoader: React.FC<{ size: number; color: ColorName }> = ({ size, color }) => {
  const pulseValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    return () => {
      pulseValue.stopAnimation();
    };
  }, [pulseValue]);

  return (
    <View style={styles.pulseContainer}>
      <Animated.View
        style={[
          styles.pulse,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor: colors[color],
            opacity: pulseValue.interpolate({
              inputRange: [0, 1],
              outputRange: [0.3, 0],
            }),
            transform: [
              {
                scale: pulseValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 2],
                }),
              },
            ],
          },
        ]}
      />
      <View
        style={[
          styles.pulseCore,
          {
            width: size / 2,
            height: size / 2,
            borderRadius: size / 4,
            backgroundColor: colors[color],
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  fullScreen: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999,
  },
  loaderContainer: {
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  text: {
    marginTop: 10,
    fontSize: fontSizes.md,
    fontWeight: fontWeights.medium,
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    borderRadius: 999,
  },
  pulseContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulse: {
    position: 'absolute',
  },
  pulseCore: {
    position: 'absolute',
  },
});

export default Loader;
