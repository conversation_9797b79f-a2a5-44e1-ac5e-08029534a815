import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Modal,
  StyleSheet,
  Animated,
  TouchableWithoutFeedback,
  Dimensions,
  PanResponder,
  ViewStyle,
  TextStyle,
  Text,
  ScrollView,
  StatusBar,
  Platform,
} from 'react-native';
import { colors, spacing, fontSizes, fontWeights } from '../../theme';
import { TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface BottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  height?: number | string;
  maxHeight?: number | string;
  enableDragIndicator?: boolean;
  enableBackdropDismiss?: boolean;
  animationDuration?: number;
  containerStyle?: ViewStyle;
  headerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  contentContainerStyle?: ViewStyle;
  backdropOpacity?: number;
  snapPoints?: string[];
  initialSnapIndex?: number;
  onSnapChange?: (index: number) => void;
  enablePanDownToClose?: boolean;
  showHeader?: boolean;
  closeIconName?: keyof typeof Ionicons.glyphMap;
  closeIconColor?: string;
  closeIconSize?: number;
}

export const BottomSheet: React.FC<BottomSheetProps> = ({
  isVisible,
  onClose,
  children,
  title,
  height,
  maxHeight = '80%',
  enableDragIndicator = true,
  enableBackdropDismiss = true,
  animationDuration = 300,
  containerStyle,
  headerStyle,
  titleStyle,
  contentContainerStyle,
  backdropOpacity = 0.5,
  snapPoints,
  initialSnapIndex = 0,
  onSnapChange,
  enablePanDownToClose = true,
  showHeader = true,
  closeIconName = 'close',
  closeIconColor = colors.textPrimary,
  closeIconSize = 24,
}) => {
  const { height: screenHeight } = Dimensions.get('window');
  const [currentSnapIndex, setCurrentSnapIndex] = useState(initialSnapIndex);
  const translateY = useRef(new Animated.Value(screenHeight)).current;
  const backdropOpacityAnim = useRef(new Animated.Value(0)).current;
  
  // Calculate sheet height based on snapPoints or height prop
  const getSheetHeight = () => {
    if (snapPoints && snapPoints.length > 0) {
      const snapPoint = snapPoints[currentSnapIndex];
      if (typeof snapPoint === 'string' && snapPoint.endsWith('%')) {
        return (screenHeight * parseInt(snapPoint)) / 100;
      }
      return parseInt(snapPoint as string);
    }
    
    if (typeof height === 'string' && height.endsWith('%')) {
      return (screenHeight * parseInt(height)) / 100;
    }
    
    return height || screenHeight * 0.5;
  };
  
  const sheetHeight = getSheetHeight();
  
  // Calculate max height
  const getMaxHeight = () => {
    if (typeof maxHeight === 'string' && maxHeight.endsWith('%')) {
      return (screenHeight * parseInt(maxHeight)) / 100;
    }
    return maxHeight as number;
  };
  
  const maxSheetHeight = getMaxHeight();
  
  // Pan responder for drag gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return gestureState.dy > 0;
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > sheetHeight * 0.4 && enablePanDownToClose) {
          // Close the bottom sheet if dragged down more than 40%
          closeBottomSheet();
        } else if (snapPoints && snapPoints.length > 1) {
          // Snap to the nearest snap point
          const currentHeight = sheetHeight - gestureState.dy;
          let closestSnapIndex = 0;
          let closestDistance = Infinity;
          
          snapPoints.forEach((point, index) => {
            const snapHeight = typeof point === 'string' && point.endsWith('%')
              ? (screenHeight * parseInt(point)) / 100
              : parseInt(point as string);
              
            const distance = Math.abs(snapHeight - currentHeight);
            if (distance < closestDistance) {
              closestDistance = distance;
              closestSnapIndex = index;
            }
          });
          
          snapToIndex(closestSnapIndex);
        } else {
          // Spring back to original position
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
            bounciness: 4,
          }).start();
        }
      },
    })
  ).current;
  
  const snapToIndex = (index: number) => {
    if (!snapPoints || index >= snapPoints.length) return;
    
    setCurrentSnapIndex(index);
    if (onSnapChange) onSnapChange(index);
    
    const snapPoint = snapPoints[index];
    const snapHeight = typeof snapPoint === 'string' && snapPoint.endsWith('%')
      ? (screenHeight * parseInt(snapPoint)) / 100
      : parseInt(snapPoint as string);
      
    const newTranslateY = sheetHeight - snapHeight;
    
    Animated.spring(translateY, {
      toValue: newTranslateY,
      useNativeDriver: true,
      bounciness: 4,
    }).start();
  };

  useEffect(() => {
    if (isVisible) {
      openBottomSheet();
    } else {
      closeBottomSheet();
    }
  }, [isVisible]);
  
  useEffect(() => {
    if (isVisible && snapPoints && snapPoints.length > 0) {
      snapToIndex(currentSnapIndex);
    }
  }, [snapPoints, currentSnapIndex, isVisible]);

  const openBottomSheet = () => {
    translateY.setValue(screenHeight);
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: animationDuration,
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacityAnim, {
        toValue: backdropOpacity,
        duration: animationDuration,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const closeBottomSheet = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: screenHeight,
        duration: animationDuration,
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacityAnim, {
        toValue: 0,
        duration: animationDuration,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  const handleBackdropPress = () => {
    if (enableBackdropDismiss) {
      closeBottomSheet();
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={closeBottomSheet}
    >
      <View style={styles.modalContainer}>
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <Animated.View
            style={[
              styles.backdrop,
              { opacity: backdropOpacityAnim },
            ]}
          />
        </TouchableWithoutFeedback>
        
        <Animated.View
          style={[
            styles.container,
            {
              maxHeight: maxSheetHeight,
              height: sheetHeight,
              transform: [{ translateY }],
            },
            containerStyle,
          ]}
        >
          {enableDragIndicator && (
            <View style={styles.dragIndicatorContainer}>
              <View style={styles.dragIndicator} />
            </View>
          )}
          
          {showHeader && (title || onClose) && (
            <View style={[styles.header, headerStyle]}>
              {title && (
                <Text style={[styles.title, titleStyle]} numberOfLines={1}>
                  {title}
                </Text>
              )}
              
              {onClose && (
                <TouchableOpacity onPress={closeBottomSheet} style={styles.closeButton}>
                  <Ionicons
                    name={closeIconName}
                    size={closeIconSize}
                    color={closeIconColor}
                  />
                </TouchableOpacity>
              )}
            </View>
          )}
          
          <ScrollView
            style={[styles.contentContainer, contentContainerStyle]}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            bounces={false}
            {...(enableDragIndicator ? { ...panResponder.panHandlers } : {})}
          >
            {children}
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.black,
  },
  container: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  dragIndicatorContainer: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },
  dragIndicator: {
    width: 40,
    height: 5,
    borderRadius: 3,
    backgroundColor: colors.lightGray,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    flex: 1,
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.semibold,
    color: colors.textPrimary,
  },
  closeButton: {
    padding: spacing.xs,
  },
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.md,
  },
});

export default BottomSheet;
