import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import RadioButton from './RadioButton';
import { ColorName, spacing } from '../../theme';

export interface RadioOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface RadioGroupProps {
  options: RadioOption[];
  value: string | number;
  onChange: (value: string | number) => void;
  direction?: 'vertical' | 'horizontal';
  size?: number;
  activeColor?: ColorName;
  inactiveColor?: ColorName;
  dotColor?: ColorName;
  style?: ViewStyle;
  itemStyle?: ViewStyle;
  spacing?: number;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  options,
  value,
  onChange,
  direction = 'vertical',
  size,
  activeColor,
  inactiveColor,
  dotColor,
  style,
  itemStyle,
  spacing: itemSpacing = spacing.md,
}) => {
  return (
    <View
      style={[
        styles.container,
        {
          flexDirection: direction === 'vertical' ? 'column' : 'row',
          flexWrap: direction === 'horizontal' ? 'wrap' : 'nowrap',
        },
        style,
      ]}
    >
      {options.map((option, index) => (
        <View
          key={option.value.toString()}
          style={[
            {
              marginBottom: direction === 'vertical' ? itemSpacing : 0,
              marginRight: direction === 'horizontal' ? itemSpacing : 0,
            },
            itemStyle,
          ]}
        >
          <RadioButton
            selected={value === option.value}
            onPress={() => onChange(option.value)}
            label={option.label}
            disabled={option.disabled}
            size={size}
            activeColor={activeColor}
            inactiveColor={inactiveColor}
            dotColor={dotColor}
          />
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
  },
});

export default RadioGroup;
