import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  Easing,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors, ColorName, spacing, fontSizes, fontWeights } from '../../theme';

interface RadioButtonProps {
  selected: boolean;
  onPress: () => void;
  label?: string;
  disabled?: boolean;
  size?: number;
  activeColor?: ColorName;
  inactiveColor?: ColorName;
  dotColor?: ColorName;
  style?: ViewStyle;
  labelStyle?: TextStyle;
  position?: 'left' | 'right';
}

export const RadioButton: React.FC<RadioButtonProps> = ({
  selected,
  onPress,
  label,
  disabled = false,
  size = 24,
  activeColor = 'primary',
  inactiveColor = 'lightGray',
  dotColor = 'white',
  style,
  labelStyle,
  position = 'left',
}) => {
  // Animation for the radio dot
  const scaleValue = React.useRef(new Animated.Value(selected ? 1 : 0)).current;

  React.useEffect(() => {
    Animated.timing(scaleValue, {
      toValue: selected ? 1 : 0,
      duration: 150,
      easing: Easing.bezier(0.0, 0.0, 0.2, 1),
      useNativeDriver: true,
    }).start();
  }, [selected, scaleValue]);

  const handlePress = () => {
    if (!disabled) {
      onPress();
    }
  };

  const renderRadioButton = () => (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={handlePress}
      disabled={disabled}
      style={[
        styles.radioButton,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          borderColor: selected ? colors[activeColor] : colors[inactiveColor],
          borderWidth: 2,
          opacity: disabled ? 0.5 : 1,
        },
        style,
      ]}
    >
      {selected && (
        <Animated.View
          style={[
            styles.dot,
            {
              width: size * 0.5,
              height: size * 0.5,
              borderRadius: (size * 0.5) / 2,
              backgroundColor: colors[selected ? activeColor : dotColor],
              transform: [{ scale: scaleValue }],
            },
          ]}
        />
      )}
    </TouchableOpacity>
  );

  if (!label) {
    return renderRadioButton();
  }

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={handlePress}
      disabled={disabled}
      style={[
        styles.container,
        {
          flexDirection: position === 'left' ? 'row' : 'row-reverse',
          opacity: disabled ? 0.5 : 1,
        },
      ]}
    >
      {renderRadioButton()}
      <Text
        style={[
          styles.label,
          {
            marginLeft: position === 'left' ? spacing.sm : 0,
            marginRight: position === 'right' ? spacing.sm : 0,
            color: disabled ? colors.textTertiary : colors.textPrimary,
          },
          labelStyle,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  label: {
    fontSize: fontSizes.md,
    fontWeight: fontWeights.regular,
  },
});

export default RadioButton;
