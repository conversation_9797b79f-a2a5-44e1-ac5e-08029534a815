// Theme colors for the app
export const Colors = {
  light: {
    // Primary colors
    primary: "#4361ee",
    secondary: "#3a0ca3",
    accent: "#7209b7",
    background: "#f8f9fa",
    card: "#ffffff",
    text: "#2b2d42",
    subtext: "#6c757d",
    border: "#e9ecef",
    shadow: "rgba(0, 0, 0, 0.1)",

    // Status colors
    success: "#06d6a0",
    warning: "#ffd166",
    error: "#ef476f",
    info: "#118ab2",

    // Tool-specific colors
    resize: "#4361ee",
    crop: "#f77f00",
    filter: "#7209b7",
    rotate: "#06d6a0",

    // UI elements
    buttonText: "#ffffff",
    inputBackground: "#f1f3f5",
    inputBorder: "#dee2e6",
    tabActive: "#4361ee",
    tabInactive: "#adb5bd",

    // Gradients
    gradientStart: "#4361ee",
    gradientEnd: "#3a0ca3",
  },
  dark: {
    // Primary colors
    primary: "#4cc9f0",
    secondary: "#4895ef",
    accent: "#7209b7",
    background: "#121212",
    card: "#1e1e1e",
    text: "#f8f9fa",
    subtext: "#adb5bd",
    border: "#2a2a2a",
    shadow: "rgba(0, 0, 0, 0.3)",

    // Status colors
    success: "#06d6a0",
    warning: "#ffd166",
    error: "#ef476f",
    info: "#118ab2",

    // Tool-specific colors
    resize: "#4cc9f0",
    crop: "#f77f00",
    filter: "#7209b7",
    rotate: "#06d6a0",

    // UI elements
    buttonText: "#ffffff",
    inputBackground: "#2a2a2a",
    inputBorder: "#3a3a3a",
    tabActive: "#4cc9f0",
    tabInactive: "#6c757d",

    // Gradients
    gradientStart: "#4cc9f0",
    gradientEnd: "#4895ef",
  },
};

export default Colors;
