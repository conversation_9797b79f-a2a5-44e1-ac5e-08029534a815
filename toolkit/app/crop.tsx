import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import * as MediaLibrary from "expo-media-library";
import { Stack } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  useColorScheme,
  View,
} from "react-native";
import ImageSelector from "../components/ImageSelector";

const screenWidth = Dimensions.get("window").width;

export default function CropScreen() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 });
  const [cropX, setCropX] = useState("0");
  const [cropY, setCropY] = useState("0");
  const [cropWidth, setCropWidth] = useState("");
  const [cropHeight, setCropHeight] = useState("");
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [saving, setSaving] = useState(false);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  useEffect(() => {
    if (selectedImage) {
      // Get original image dimensions
      Image.getSize(selectedImage, (width, height) => {
        setOriginalDimensions({ width, height });
        setCropWidth(width.toString());
        setCropHeight(height.toString());
      });
    }
  }, [selectedImage]);

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
    setProcessedImage(null);
    setCropX("0");
    setCropY("0");
  };

  const validateCropValues = () => {
    const x = parseInt(cropX);
    const y = parseInt(cropY);
    const width = parseInt(cropWidth);
    const height = parseInt(cropHeight);

    if (
      isNaN(x) ||
      isNaN(y) ||
      isNaN(width) ||
      isNaN(height) ||
      width <= 0 ||
      height <= 0 ||
      x < 0 ||
      y < 0 ||
      x + width > originalDimensions.width ||
      y + height > originalDimensions.height
    ) {
      return false;
    }

    return true;
  };

  const processImage = async () => {
    if (!selectedImage) return;

    if (!validateCropValues()) {
      Alert.alert(
        "Invalid Crop Values",
        "Please ensure crop values are within the image boundaries."
      );
      return;
    }

    setProcessing(true);
    try {
      const x = parseInt(cropX);
      const y = parseInt(cropY);
      const width = parseInt(cropWidth);
      const height = parseInt(cropHeight);

      const result = await ImageManipulator.manipulateAsync(
        selectedImage,
        [{ crop: { originX: x, originY: y, width, height } }],
        { compress: 1, format: ImageManipulator.SaveFormat.JPEG }
      );

      setProcessedImage(result.uri);
    } catch (error) {
      console.error("Error processing image:", error);
      Alert.alert("Error", "Failed to crop image. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const saveImage = async () => {
    if (!processedImage) return;

    setSaving(true);
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant permission to save images to your gallery."
        );
        setSaving(false);
        return;
      }

      // Save the image
      const asset = await MediaLibrary.createAssetAsync(processedImage);
      await MediaLibrary.createAlbumAsync("ImageToolkit", asset, false);

      Alert.alert("Success", "Image saved to gallery!");
    } catch (error) {
      console.error("Error saving image:", error);
      Alert.alert("Error", "Failed to save image. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "Crop Image",
          headerTitleAlign: "center",
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <ImageSelector
          onImageSelected={handleImageSelected}
          selectedImage={processedImage || selectedImage}
        />

        {selectedImage && (
          <View style={styles.controlsContainer}>
            <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>
              Original Size: {originalDimensions.width} × {originalDimensions.height}
            </Text>

            <View style={styles.cropControlsContainer}>
              <View style={styles.cropRow}>
                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    X Position
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropX}
                    onChangeText={setCropX}
                    keyboardType="number-pad"
                    placeholder="X"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    Y Position
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropY}
                    onChangeText={setCropY}
                    keyboardType="number-pad"
                    placeholder="Y"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>
              </View>

              <View style={styles.cropRow}>
                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    Width
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropWidth}
                    onChangeText={setCropWidth}
                    keyboardType="number-pad"
                    placeholder="Width"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    Height
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropHeight}
                    onChangeText={setCropHeight}
                    keyboardType="number-pad"
                    placeholder="Height"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Pressable
                style={[
                  styles.button,
                  { backgroundColor: "#FF9800", opacity: processing ? 0.7 : 1 },
                ]}
                onPress={processImage}
                disabled={processing || !selectedImage}>
                {processing ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Ionicons
                      name="crop-outline"
                      size={20}
                      color="#fff"
                      style={styles.buttonIcon}
                    />
                    <Text style={styles.buttonText}>Crop Image</Text>
                  </>
                )}
              </Pressable>

              {processedImage && (
                <Pressable
                  style={[styles.button, { backgroundColor: "#4CAF50", opacity: saving ? 0.7 : 1 }]}
                  onPress={saveImage}
                  disabled={saving}>
                  {saving ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <Ionicons
                        name="save-outline"
                        size={20}
                        color="#fff"
                        style={styles.buttonIcon}
                      />
                      <Text style={styles.buttonText}>Save to Gallery</Text>
                    </>
                  )}
                </Pressable>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  controlsContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 15,
  },
  cropControlsContainer: {
    width: "100%",
    marginBottom: 20,
  },
  cropRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 15,
  },
  inputContainer: {
    width: "48%",
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: "column",
    justifyContent: "center",
    width: "100%",
    gap: 10,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
