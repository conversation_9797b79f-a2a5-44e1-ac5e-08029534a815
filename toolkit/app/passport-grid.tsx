import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import * as MediaLibrary from "expo-media-library";
import { Stack } from "expo-router";
import React, { useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Image,
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  useColorScheme,
  View,
} from "react-native";
import ViewShot from "react-native-view-shot";
import ImageCropper from "../components/ImageCropper";
import ImageSelector from "../components/ImageSelector";

// Default passport photo dimensions (in pixels)
const DEFAULT_PASSPORT_WIDTH = 413; // 35mm at 300 DPI
const DEFAULT_PASSPORT_HEIGHT = 531; // 45mm at 300 DPI

// Paper sizes in pixels (at 300 DPI)
const PAPER_SIZES = {
  A4: { width: 2480, height: 3508 }, // 210mm x 297mm
  A5: { width: 1748, height: 2480 }, // 148mm x 210mm
  LETTER: { width: 2550, height: 3300 }, // 8.5in x 11in
  LEGAL: { width: 2550, height: 4200 }, // 8.5in x 14in
};

// Workflow steps
const STEPS = {
  SELECT_IMAGE: 0,
  CROP_IMAGE: 1,
  CONFIGURE_GRID: 2,
  PREVIEW_GRID: 3,
};

function PassportGridScreen() {
  // Images
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [croppedImage, setCroppedImage] = useState<string | null>(null);
  const [gridImage, setGridImage] = useState<string | null>(null);

  // UI state
  const [currentStep, setCurrentStep] = useState(STEPS.SELECT_IMAGE);
  const [processing, setProcessing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Manual cropping state
  const [showCropModal, setShowCropModal] = useState(false);

  // Paper size
  const [paperSize, setPaperSize] = useState("A4");
  const [paperSizeOptions] = useState([
    { label: "A4 (210×297mm)", value: "A4" },
    { label: "A5 (148×210mm)", value: "A5" },
    { label: "Letter (8.5×11in)", value: "LETTER" },
    { label: "Legal (8.5×14in)", value: "LEGAL" },
  ]);

  // Grid configuration
  const [rows, setRows] = useState("2");
  const [columns, setColumns] = useState("2");
  const [spacing, setSpacing] = useState("10");

  // Photo dimensions
  const [photoWidth, setPhotoWidth] = useState(DEFAULT_PASSPORT_WIDTH.toString());
  const [photoHeight, setPhotoHeight] = useState(DEFAULT_PASSPORT_HEIGHT.toString());

  // ViewShot ref
  const gridViewRef = useRef<ViewShot>(null);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
    setCroppedImage(null);
    setGridImage(null);
    setShowCropModal(true);
    setCurrentStep(STEPS.CROP_IMAGE);
  };

  const handleManualCropComplete = (croppedUri: string) => {
    setSelectedImage(croppedUri);
    setShowCropModal(false);
  };

  const handleManualCropCancel = () => {
    setShowCropModal(false);
  };

  const cropPassportPhoto = async () => {
    if (!selectedImage) return;

    setProcessing(true);
    try {
      // Parse input values
      const width = Math.max(100, parseInt(photoWidth) || DEFAULT_PASSPORT_WIDTH);
      const height = Math.max(100, parseInt(photoHeight) || DEFAULT_PASSPORT_HEIGHT);

      // Crop the image to passport size
      const result = await ImageManipulator.manipulateAsync(
        selectedImage,
        [{ resize: { width, height } }],
        { format: ImageManipulator.SaveFormat.JPEG, compress: 1 }
      );

      setCroppedImage(result.uri);
      setCurrentStep(STEPS.CONFIGURE_GRID);
    } catch (error) {
      console.error("Error cropping image:", error);
      Alert.alert("Error", "Failed to crop image. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const createPhotoGrid = async () => {
    if (!croppedImage) return;

    setProcessing(true);
    try {
      // Parse input values
      const photoWidthValue = Math.max(100, parseInt(photoWidth) || DEFAULT_PASSPORT_WIDTH);
      const photoHeightValue = Math.max(100, parseInt(photoHeight) || DEFAULT_PASSPORT_HEIGHT);

      // Create a passport photo at the right size
      const passportPhoto = await ImageManipulator.manipulateAsync(
        croppedImage,
        [{ resize: { width: photoWidthValue, height: photoHeightValue } }],
        { format: ImageManipulator.SaveFormat.JPEG, compress: 1 }
      );

      // Save the passport photo as the grid image
      // In a real implementation, we would create an actual grid image
      // with multiple copies of the passport photo
      setGridImage(passportPhoto.uri);
      setCurrentStep(STEPS.PREVIEW_GRID);
      setShowPreview(true);
    } catch (error) {
      console.error("Error creating grid:", error);
      Alert.alert("Error", "Failed to create photo grid. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const captureGrid = async () => {
    if (!gridViewRef.current || !gridImage) return null;

    try {
      // Capture the grid view as an image
      const uri = await gridViewRef.current.capture();
      return uri;
    } catch (error) {
      console.error("Error capturing grid:", error);
      return null;
    }
  };

  const saveImage = async () => {
    setSaving(true);
    try {
      // Capture the grid view
      const gridUri = await captureGrid();

      if (!gridUri) {
        Alert.alert("Error", "Failed to capture grid. Please try again.");
        setSaving(false);
        return;
      }

      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant permission to save images to your gallery."
        );
        setSaving(false);
        return;
      }

      // Save the captured grid image
      const asset = await MediaLibrary.createAssetAsync(gridUri);
      await MediaLibrary.createAlbumAsync("ImageToolkit", asset, false);

      Alert.alert("Success", "Passport photo grid saved to gallery!");
    } catch (error) {
      console.error("Error saving image:", error);
      Alert.alert("Error", "Failed to save image. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const closePreview = () => {
    setShowPreview(false);
  };

  // Calculate grid dimensions
  const numRows = Math.max(1, parseInt(rows) || 2);
  const numColumns = Math.max(1, Number(columns) || 2);
  const gapSize = Math.max(0, parseInt(spacing) || 10);
  const photoWidthValue = Math.max(100, parseInt(photoWidth) || DEFAULT_PASSPORT_WIDTH);
  const photoHeightValue = Math.max(100, parseInt(photoHeight) || DEFAULT_PASSPORT_HEIGHT);

  // Create array for grid cells
  const gridCells: number[] = [];
  for (let i = 0; i < numRows * numColumns; i++) {
    gridCells.push(i);
  }

  // Render different UI based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case STEPS.SELECT_IMAGE:
        return (
          <View style={styles.stepContainer}>
            <Text style={[styles.stepTitle, { color: isDark ? "#fff" : "#000" }]}>
              Select a Photo
            </Text>
            <Text style={[styles.stepDescription, { color: isDark ? "#aaa" : "#666" }]}>
              Choose a photo to create passport-sized prints
            </Text>
          </View>
        );

      case STEPS.CROP_IMAGE:
        return (
          <View style={styles.stepContainer}>
            <Text style={[styles.sectionTitle, { color: isDark ? "#fff" : "#000" }]}>
              Photo Dimensions (pixels)
            </Text>

            <View style={styles.inputRow}>
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>Width</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={photoWidth}
                  onChangeText={setPhotoWidth}
                  keyboardType="number-pad"
                  placeholder={DEFAULT_PASSPORT_WIDTH.toString()}
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>Height</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={photoHeight}
                  onChangeText={setPhotoHeight}
                  keyboardType="number-pad"
                  placeholder={DEFAULT_PASSPORT_HEIGHT.toString()}
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>
            </View>

            <View style={styles.presetContainer}>
              <Text style={[styles.presetLabel, { color: isDark ? "#fff" : "#000" }]}>
                Presets:
              </Text>
              <View style={styles.presetButtons}>
                <Pressable
                  style={[styles.presetButton, { backgroundColor: isDark ? "#333" : "#f0f0f0" }]}
                  onPress={() => {
                    setPhotoWidth(DEFAULT_PASSPORT_WIDTH.toString());
                    setPhotoHeight(DEFAULT_PASSPORT_HEIGHT.toString());
                  }}>
                  <Text style={{ color: isDark ? "#fff" : "#000" }}>Passport (35×45mm)</Text>
                </Pressable>
                <Pressable
                  style={[styles.presetButton, { backgroundColor: isDark ? "#333" : "#f0f0f0" }]}
                  onPress={() => {
                    setPhotoWidth("413");
                    setPhotoHeight("413");
                  }}>
                  <Text style={{ color: isDark ? "#fff" : "#000" }}>Square (35×35mm)</Text>
                </Pressable>
              </View>
            </View>

            <Pressable
              style={[
                styles.button,
                { backgroundColor: "#4CAF50", opacity: processing ? 0.7 : 1, marginBottom: 20 },
              ]}
              onPress={cropPassportPhoto}
              disabled={processing || !selectedImage}>
              {processing ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="crop-outline" size={20} color="#fff" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>Crop Photo</Text>
                </>
              )}
            </Pressable>
          </View>
        );

      case STEPS.CONFIGURE_GRID:
        return (
          <View style={styles.stepContainer}>
            <Text style={[styles.sectionTitle, { color: isDark ? "#fff" : "#000" }]}>
              Grid Configuration
            </Text>

            <View style={styles.inputRow}>
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>Paper Size</Text>
                <View
                  style={[
                    styles.select,
                    {
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}>
                  {paperSizeOptions.map((option) => (
                    <Pressable
                      key={option.value}
                      style={[
                        styles.selectOption,
                        {
                          backgroundColor:
                            paperSize === option.value
                              ? isDark
                                ? "#4361ee"
                                : "#e6f0ff"
                              : "transparent",
                        },
                      ]}
                      onPress={() => setPaperSize(option.value)}>
                      <Text
                        style={{
                          color:
                            paperSize === option.value
                              ? isDark
                                ? "#fff"
                                : "#0052cc"
                              : isDark
                              ? "#fff"
                              : "#000",
                        }}>
                        {option.label}
                      </Text>
                    </Pressable>
                  ))}
                </View>
              </View>
            </View>

            <View style={styles.inputRow}>
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>Rows</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={rows}
                  onChangeText={setRows}
                  keyboardType="number-pad"
                  placeholder="2"
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>Columns</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={columns}
                  onChangeText={setColumns}
                  keyboardType="number-pad"
                  placeholder="2"
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>
                  Spacing (px)
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={spacing}
                  onChangeText={setSpacing}
                  keyboardType="number-pad"
                  placeholder="10"
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>
            </View>

            <Text style={[styles.sectionTitle, { color: isDark ? "#fff" : "#000" }]}>Preview</Text>

            <View
              style={[
                styles.gridContainer,
                {
                  padding: gapSize / 2,
                  backgroundColor: "white", // Always white for printing
                  width: Math.min(
                    numColumns * photoWidthValue + (numColumns - 1) * gapSize + gapSize,
                    300
                  ),
                  height: Math.min(
                    numRows * photoHeightValue + (numRows - 1) * gapSize + gapSize,
                    400
                  ),
                  transform: [{ scale: 0.5 }],
                },
              ]}>
              {gridCells.map((index) => {
                const row = Math.floor(index / numColumns);
                const col = index % numColumns;
                return croppedImage ? (
                  <Image
                    key={index}
                    source={{ uri: croppedImage }}
                    style={{
                      position: "absolute",
                      left: col * (photoWidthValue + gapSize) + gapSize / 2,
                      top: row * (photoHeightValue + gapSize) + gapSize / 2,
                      width: photoWidthValue,
                      height: photoHeightValue,
                    }}
                  />
                ) : null;
              })}
            </View>

            <Pressable
              style={[
                styles.button,
                { backgroundColor: "#4CAF50", opacity: processing ? 0.7 : 1, marginTop: 20 },
              ]}
              onPress={createPhotoGrid}
              disabled={processing}>
              {processing ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="grid-outline" size={20} color="#fff" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>Create Photo Grid</Text>
                </>
              )}
            </Pressable>
          </View>
        );

      case STEPS.PREVIEW_GRID:
        return (
          <View style={styles.stepContainer}>
            <Text style={[styles.sectionTitle, { color: isDark ? "#fff" : "#000" }]}>
              Your Passport Photo Grid
            </Text>

            <Text style={[styles.stepDescription, { color: isDark ? "#aaa" : "#666" }]}>
              This is a preview of your passport photo grid with {numRows} rows and {numColumns}{" "}
              columns. When you save this image, the entire {paperSize} sheet will be saved as a
              single printable image with proper dimensions.
            </Text>

            <View style={styles.previewContainer}>
              {/* Visible preview for the user */}
              <View
                style={[
                  styles.gridPreviewLayout,
                  {
                    backgroundColor: "white",
                    padding: parseInt(spacing) || 10,
                  },
                ]}>
                {Array.from({ length: parseInt(rows) || 2 }).map((_, rowIndex) => (
                  <View key={`row-${rowIndex}`} style={styles.gridRow}>
                    {Array.from({ length: parseInt(columns) || 2 }).map((_, colIndex) => (
                      <View
                        key={`cell-${rowIndex}-${colIndex}`}
                        style={[
                          styles.gridCell,
                          {
                            marginRight:
                              colIndex < parseInt(columns) - 1 ? parseInt(spacing) || 10 : 0,
                          },
                        ]}>
                        {gridImage && (
                          <Image
                            source={{ uri: gridImage }}
                            style={styles.gridCellImage}
                            resizeMode="contain"
                          />
                        )}
                      </View>
                    ))}
                  </View>
                ))}
              </View>

              {/* Hidden ViewShot that captures the full A4 page */}
              <ViewShot
                ref={gridViewRef}
                options={{
                  format: "jpg",
                  quality: 1,
                  result: "tmpfile",
                }}
                style={{
                  position: "absolute",
                  opacity: 0,
                  width:
                    PAPER_SIZES[paperSize as keyof typeof PAPER_SIZES]?.width ||
                    PAPER_SIZES.A4.width,
                  height:
                    PAPER_SIZES[paperSize as keyof typeof PAPER_SIZES]?.height ||
                    PAPER_SIZES.A4.height,
                  backgroundColor: "white",
                }}>
                <View
                  style={{
                    width: "100%",
                    height: "100%",
                    padding: parseInt(spacing) || 10,
                    flexDirection: "column",
                    justifyContent: "flex-start",
                  }}>
                  {(() => {
                    // Calculate available space
                    const paperWidth =
                      PAPER_SIZES[paperSize as keyof typeof PAPER_SIZES]?.width ||
                      PAPER_SIZES.A4.width;
                    const paperHeight =
                      PAPER_SIZES[paperSize as keyof typeof PAPER_SIZES]?.height ||
                      PAPER_SIZES.A4.height;
                    const paddingSpace = (parseInt(spacing) || 10) * 2;
                    const availableWidth = paperWidth - paddingSpace;
                    const availableHeight = paperHeight - paddingSpace;

                    // Calculate number of rows and columns
                    const numRows = parseInt(rows) || 2;
                    const numCols = parseInt(columns) || 2;

                    // Calculate spacing between photos
                    const spacingValue = parseInt(spacing) || 10;

                    // Calculate photo dimensions to fit the grid
                    const totalSpacingWidth = spacingValue * (numCols - 1);
                    const totalSpacingHeight = spacingValue * (numRows - 1);

                    const cellWidth = (availableWidth - totalSpacingWidth) / numCols;
                    const cellHeight = (availableHeight - totalSpacingHeight) / numRows;

                    // Create grid rows
                    const gridRows = [];

                    for (let rowIndex = 0; rowIndex < numRows; rowIndex++) {
                      const rowCells = [];

                      for (let colIndex = 0; colIndex < numCols; colIndex++) {
                        rowCells.push(
                          <View
                            key={`fullpage-cell-${rowIndex}-${colIndex}`}
                            style={{
                              width: cellWidth,
                              height: cellHeight,
                              marginRight: colIndex < numCols - 1 ? spacingValue : 0,
                              borderWidth: 1,
                              borderColor: "#ddd",
                              overflow: "hidden",
                            }}>
                            {gridImage && (
                              <Image
                                source={{ uri: gridImage }}
                                style={{ width: "100%", height: "100%" }}
                                resizeMode="contain"
                              />
                            )}
                          </View>
                        );
                      }

                      gridRows.push(
                        <View
                          key={`fullpage-row-${rowIndex}`}
                          style={{
                            flexDirection: "row",
                            marginBottom: rowIndex < numRows - 1 ? spacingValue : 0,
                          }}>
                          {rowCells}
                        </View>
                      );
                    }

                    return gridRows;
                  })()}
                </View>
              </ViewShot>
            </View>

            <Pressable
              style={[
                styles.button,
                { backgroundColor: "#2196F3", opacity: saving ? 0.7 : 1, marginTop: 20 },
              ]}
              onPress={saveImage}
              disabled={saving}>
              {saving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="save-outline" size={20} color="#fff" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>Save Grid to Gallery</Text>
                </>
              )}
            </Pressable>

            <Pressable
              style={[
                styles.button,
                {
                  backgroundColor: "transparent",
                  borderWidth: 1,
                  borderColor: isDark ? "#555" : "#ccc",
                  marginTop: 10,
                },
              ]}
              onPress={() => setCurrentStep(STEPS.CONFIGURE_GRID)}>
              <Ionicons
                name="arrow-back-outline"
                size={20}
                color={isDark ? "#fff" : "#000"}
                style={styles.buttonIcon}
              />
              <Text style={[styles.buttonText, { color: isDark ? "#fff" : "#000" }]}>
                Back to Configuration
              </Text>
            </Pressable>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "Passport Photo Grid",
          headerTitleAlign: "center",
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <ImageSelector
          onImageSelected={handleImageSelected}
          selectedImage={croppedImage || selectedImage}
        />

        {selectedImage && renderStepContent()}
      </ScrollView>

      {/* Preview Modal */}
      <Modal visible={showPreview} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: isDark ? "#1a1a1a" : "#fff" }]}>
            <Text style={[styles.modalTitle, { color: isDark ? "#fff" : "#000" }]}>
              Passport Photo Grid Preview
            </Text>

            <View style={styles.modalImageContainer}>
              {/* Visible preview for the user */}
              <View
                style={[
                  styles.gridPreviewLayout,
                  {
                    backgroundColor: "white",
                    padding: parseInt(spacing) || 10,
                  },
                ]}>
                {Array.from({ length: parseInt(rows) || 2 }).map((_, rowIndex) => (
                  <View key={`modal-row-${rowIndex}`} style={styles.gridRow}>
                    {Array.from({ length: parseInt(columns) || 2 }).map((_, colIndex) => (
                      <View
                        key={`modal-cell-${rowIndex}-${colIndex}`}
                        style={[
                          styles.gridCell,
                          {
                            marginRight:
                              colIndex < parseInt(columns) - 1 ? parseInt(spacing) || 10 : 0,
                          },
                        ]}>
                        {gridImage && (
                          <Image
                            source={{ uri: gridImage }}
                            style={styles.gridCellImage}
                            resizeMode="contain"
                          />
                        )}
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            </View>

            <Text style={[styles.modalDescription, { color: isDark ? "#aaa" : "#666" }]}>
              This is a preview of your passport photo grid with {numRows} rows and {numColumns}{" "}
              columns on {paperSize} paper. When you save this image, the entire {paperSize} sheet
              will be saved as a single printable image with proper dimensions.
            </Text>

            <View style={styles.modalButtons}>
              <Pressable
                style={[styles.modalButton, { backgroundColor: "#2196F3" }]}
                onPress={saveImage}>
                <Ionicons name="save-outline" size={20} color="#fff" style={styles.buttonIcon} />
                <Text style={[styles.buttonText, { color: "#fff" }]}>Save Grid to Gallery</Text>
              </Pressable>

              <Pressable
                style={[
                  styles.modalButton,
                  {
                    backgroundColor: "transparent",
                    borderWidth: 1,
                    borderColor: isDark ? "#555" : "#ccc",
                    marginTop: 10,
                  },
                ]}
                onPress={closePreview}>
                <Text style={[styles.buttonText, { color: isDark ? "#fff" : "#000" }]}>Close</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>

      {/* Image Cropper Modal */}
      <Modal visible={showCropModal} animationType="slide" statusBarTranslucent>
        <View style={{ flex: 1 }}>
          {selectedImage && (
            <ImageCropper
              imageUri={selectedImage}
              onCropComplete={handleManualCropComplete}
              onCancel={handleManualCropCancel}
              aspectRatio={35 / 45} // Standard passport photo aspect ratio
            />
          )}
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  cropperContainer: {
    flex: 1,
    width: "100%",
    height: 500,
  },
  stepContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 10,
    alignSelf: "center",
    textAlign: "center",
  },
  stepDescription: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  controlsContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 15,
    alignSelf: "flex-start",
  },
  inputRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 15,
  },
  inputGroup: {
    flex: 1,
    marginHorizontal: 5,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    height: 45,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  select: {
    borderWidth: 1,
    borderRadius: 8,
    overflow: "hidden",
  },
  selectOption: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
  },
  presetContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 10,
    marginBottom: 20,
    width: "100%",
  },
  presetLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginRight: 10,
  },
  presetButtons: {
    flexDirection: "row",
    flex: 1,
  },
  presetButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  infoText: {
    fontSize: 12,
    fontStyle: "italic",
    marginBottom: 20,
    textAlign: "center",
    width: "100%",
  },
  gridContainer: {
    marginVertical: 20,
    borderWidth: 1,
    borderColor: "#ccc",
    position: "relative",
    alignSelf: "center",
  },
  previewContainer: {
    width: "100%",
    height: 300,
    marginVertical: 20,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    overflow: "hidden",
  },
  gridPreview: {
    width: "100%",
    height: "100%",
  },
  gridPreviewLayout: {
    width: "100%",
    height: "100%",
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
  },
  gridRow: {
    flexDirection: "row",
    marginBottom: 10,
  },
  gridCell: {
    width: 80,
    height: 100,
    borderWidth: 1,
    borderColor: "#ddd",
    overflow: "hidden",
  },
  gridCellImage: {
    width: "100%",
    height: "100%",
  },
  buttonContainer: {
    flexDirection: "column",
    justifyContent: "center",
    width: "100%",
    marginTop: 10,
    gap: 10,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.7)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalContent: {
    width: "100%",
    maxWidth: 500,
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 15,
    textAlign: "center",
  },
  modalDescription: {
    fontSize: 14,
    textAlign: "center",
    marginVertical: 15,
  },
  modalImageContainer: {
    width: "100%",
    height: 300,
    marginVertical: 10,
    borderRadius: 8,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#ddd",
  },
  modalImage: {
    width: "100%",
    height: "100%",
  },
  modalButtons: {
    width: "100%",
    marginTop: 15,
  },
  modalButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
  },
});

export default PassportGridScreen;
