import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import * as MediaLibrary from "expo-media-library";
import { Stack } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { useTheme } from "../theme/ThemeContext";
import ImageSelector from "../components/ImageSelector";

type RotateOperation = "rotate90" | "rotate180" | "rotate270" | "flipHorizontal" | "flipVertical";

interface RotateOption {
  id: RotateOperation;
  name: string;
  icon: React.ComponentProps<typeof Ionicons>["name"];
  description: string;
}

export default function RotateScreen() {
  const { colors, theme } = useTheme();
  const isDark = theme === "dark";

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [saving, setSaving] = useState(false);
  
  const [selectedOperation, setSelectedOperation] = useState<RotateOperation | null>(null);

  // Rotate and flip options
  const rotateOptions: RotateOption[] = [
    { 
      id: "rotate90", 
      name: "Rotate 90°", 
      icon: "refresh-outline", 
      description: "Rotate image 90 degrees clockwise" 
    },
    { 
      id: "rotate180", 
      name: "Rotate 180°", 
      icon: "refresh-outline", 
      description: "Rotate image 180 degrees" 
    },
    { 
      id: "rotate270", 
      name: "Rotate 270°", 
      icon: "refresh-outline", 
      description: "Rotate image 270 degrees clockwise" 
    },
    { 
      id: "flipHorizontal", 
      name: "Flip Horizontal", 
      icon: "swap-horizontal-outline", 
      description: "Mirror the image horizontally" 
    },
    { 
      id: "flipVertical", 
      name: "Flip Vertical", 
      icon: "swap-vertical-outline", 
      description: "Mirror the image vertically" 
    },
  ];

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
    setProcessedImage(null);
    setSelectedOperation(null);
  };

  const applyTransformation = async () => {
    if (!selectedImage || !selectedOperation) return;

    setProcessing(true);
    try {
      let actions: ImageManipulator.Action[] = [];
      
      switch (selectedOperation) {
        case "rotate90":
          actions = [{ rotate: 90 }];
          break;
          
        case "rotate180":
          actions = [{ rotate: 180 }];
          break;
          
        case "rotate270":
          actions = [{ rotate: 270 }];
          break;
          
        case "flipHorizontal":
          actions = [{ flip: { horizontal: true, vertical: false } }];
          break;
          
        case "flipVertical":
          actions = [{ flip: { horizontal: false, vertical: true } }];
          break;
      }

      const result = await ImageManipulator.manipulateAsync(
        selectedImage,
        actions,
        { compress: 1, format: ImageManipulator.SaveFormat.JPEG }
      );

      setProcessedImage(result.uri);
    } catch (error) {
      console.error("Error applying transformation:", error);
      Alert.alert("Error", "Failed to transform image. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const saveImage = async () => {
    if (!processedImage) return;

    setSaving(true);
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant permission to save images to your gallery."
        );
        setSaving(false);
        return;
      }

      // Save the image
      const asset = await MediaLibrary.createAssetAsync(processedImage);
      await MediaLibrary.createAlbumAsync("ImageToolkit", asset, false);

      Alert.alert("Success", "Image saved to gallery!");
    } catch (error) {
      console.error("Error saving image:", error);
      Alert.alert("Error", "Failed to save image. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "Rotate & Flip",
          headerTitleAlign: "center",
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <ImageSelector
          onImageSelected={handleImageSelected}
          selectedImage={processedImage || selectedImage}
        />

        {selectedImage && (
          <View style={styles.controlsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Choose an Operation
            </Text>

            <View style={styles.optionsContainer}>
              {rotateOptions.map((option) => (
                <Pressable
                  key={option.id}
                  style={[
                    styles.optionButton,
                    {
                      backgroundColor: selectedOperation === option.id ? colors.primary : colors.card,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={() => setSelectedOperation(option.id)}
                >
                  <Ionicons
                    name={option.icon}
                    size={24}
                    color={selectedOperation === option.id ? colors.buttonText : colors.text}
                  />
                  <Text
                    style={[
                      styles.optionName,
                      {
                        color: selectedOperation === option.id ? colors.buttonText : colors.text,
                      },
                    ]}
                  >
                    {option.name}
                  </Text>
                  <Text
                    style={[
                      styles.optionDescription,
                      {
                        color: selectedOperation === option.id ? colors.buttonText : colors.subtext,
                      },
                    ]}
                  >
                    {option.description}
                  </Text>
                </Pressable>
              ))}
            </View>

            <View style={styles.buttonContainer}>
              <Pressable
                style={[
                  styles.button,
                  { 
                    backgroundColor: colors.primary, 
                    opacity: processing || !selectedOperation ? 0.5 : 1 
                  },
                ]}
                onPress={applyTransformation}
                disabled={processing || !selectedOperation}>
                {processing ? (
                  <ActivityIndicator size="small" color={colors.buttonText} />
                ) : (
                  <>
                    <Ionicons
                      name="refresh-circle-outline"
                      size={20}
                      color={colors.buttonText}
                      style={styles.buttonIcon}
                    />
                    <Text style={[styles.buttonText, { color: colors.buttonText }]}>
                      Apply Transformation
                    </Text>
                  </>
                )}
              </Pressable>

              {processedImage && (
                <Pressable
                  style={[
                    styles.button,
                    { backgroundColor: colors.success, opacity: saving ? 0.7 : 1 },
                  ]}
                  onPress={saveImage}
                  disabled={saving}>
                  {saving ? (
                    <ActivityIndicator size="small" color={colors.buttonText} />
                  ) : (
                    <>
                      <Ionicons
                        name="save-outline"
                        size={20}
                        color={colors.buttonText}
                        style={styles.buttonIcon}
                      />
                      <Text style={[styles.buttonText, { color: colors.buttonText }]}>
                        Save to Gallery
                      </Text>
                    </>
                  )}
                </Pressable>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  controlsContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 15,
    alignSelf: "flex-start",
  },
  optionsContainer: {
    width: "100%",
    marginBottom: 20,
  },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginBottom: 10,
    borderRadius: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  optionName: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 12,
    flex: 1,
  },
  optionDescription: {
    fontSize: 12,
    marginLeft: 8,
    flex: 2,
  },
  buttonContainer: {
    flexDirection: "column",
    justifyContent: "center",
    width: "100%",
    marginTop: 10,
    gap: 10,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  buttonText: {
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
