import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import * as MediaLibrary from "expo-media-library";
import { Stack } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  useColorScheme,
  View,
} from "react-native";
import ImageSelector from "../components/ImageSelector";

const screenWidth = Dimensions.get("window").width;

type EditMode = "percentage" | "dimensions" | "crop";

export default function AllToolsScreen() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 });
  const [editMode, setEditMode] = useState<EditMode>("percentage");

  // Percentage resize state
  const [percentage, setPercentage] = useState(100);

  // Dimensions resize state
  const [width, setWidth] = useState("");
  const [height, setHeight] = useState("");
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);

  // Crop state
  const [cropX, setCropX] = useState("0");
  const [cropY, setCropY] = useState("0");
  const [cropWidth, setCropWidth] = useState("");
  const [cropHeight, setCropHeight] = useState("");

  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [saving, setSaving] = useState(false);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  useEffect(() => {
    if (selectedImage) {
      // Get original image dimensions
      Image.getSize(selectedImage, (width, height) => {
        setOriginalDimensions({ width, height });
        setWidth(width.toString());
        setHeight(height.toString());
        setCropWidth(width.toString());
        setCropHeight(height.toString());
      });
    }
  }, [selectedImage]);

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
    setProcessedImage(null);
    setPercentage(100);
    setCropX("0");
    setCropY("0");
  };

  const updateWidth = (text: string) => {
    setWidth(text);
    if (maintainAspectRatio && originalDimensions.width > 0 && text) {
      const aspectRatio = originalDimensions.height / originalDimensions.width;
      const newHeight = Math.round(parseInt(text) * aspectRatio);
      setHeight(newHeight.toString());
    }
  };

  const updateHeight = (text: string) => {
    setHeight(text);
    if (maintainAspectRatio && originalDimensions.height > 0 && text) {
      const aspectRatio = originalDimensions.width / originalDimensions.height;
      const newWidth = Math.round(parseInt(text) * aspectRatio);
      setWidth(newWidth.toString());
    }
  };

  const toggleAspectRatio = () => {
    setMaintainAspectRatio(!maintainAspectRatio);
  };

  const validateCropValues = () => {
    const x = parseInt(cropX);
    const y = parseInt(cropY);
    const width = parseInt(cropWidth);
    const height = parseInt(cropHeight);

    if (
      isNaN(x) ||
      isNaN(y) ||
      isNaN(width) ||
      isNaN(height) ||
      width <= 0 ||
      height <= 0 ||
      x < 0 ||
      y < 0 ||
      x + width > originalDimensions.width ||
      y + height > originalDimensions.height
    ) {
      return false;
    }

    return true;
  };

  const processImage = async () => {
    if (!selectedImage) return;

    setProcessing(true);
    try {
      let result;

      switch (editMode) {
        case "percentage":
          // Ensure percentage is greater than 0 to avoid invalid dimensions
          if (percentage <= 0) {
            Alert.alert("Invalid Percentage", "Resize percentage must be greater than 0.");
            setProcessing(false);
            return;
          }

          // Calculate quality based on percentage (0.01 to 1.00)
          const quality = Math.max(0.01, Math.min(1.0, percentage / 100));

          try {
            result = await ImageManipulator.manipulateAsync(
              selectedImage,
              [], // No resize operations, keep original dimensions
              {
                compress: quality,
                format: ImageManipulator.SaveFormat.JPEG,
              }
            );
          } catch (error) {
            console.error("Error in quality adjustment:", error);
            Alert.alert(
              "Error",
              "Failed to adjust image quality. Please try a different percentage value."
            );
            setProcessing(false);
            return;
          }
          break;

        case "dimensions":
          const newWidth = parseInt(width);
          const newHeight = parseInt(height);

          if (isNaN(newWidth) || isNaN(newHeight) || newWidth <= 0 || newHeight <= 0) {
            Alert.alert("Invalid Dimensions", "Please enter valid width and height values.");
            setProcessing(false);
            return;
          }

          result = await ImageManipulator.manipulateAsync(
            selectedImage,
            [{ resize: { width: newWidth, height: newHeight } }],
            { compress: 1, format: ImageManipulator.SaveFormat.JPEG }
          );
          break;

        case "crop":
          if (!validateCropValues()) {
            Alert.alert(
              "Invalid Crop Values",
              "Please ensure crop values are within the image boundaries."
            );
            setProcessing(false);
            return;
          }

          const x = parseInt(cropX);
          const y = parseInt(cropY);
          const cropW = parseInt(cropWidth);
          const cropH = parseInt(cropHeight);

          result = await ImageManipulator.manipulateAsync(
            selectedImage,
            [{ crop: { originX: x, originY: y, width: cropW, height: cropH } }],
            { compress: 1, format: ImageManipulator.SaveFormat.JPEG }
          );
          break;
      }

      setProcessedImage(result.uri);
    } catch (error) {
      console.error("Error processing image:", error);
      Alert.alert("Error", "Failed to process image. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const saveImage = async () => {
    if (!processedImage) return;

    setSaving(true);
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant permission to save images to your gallery."
        );
        setSaving(false);
        return;
      }

      // Save the image
      const asset = await MediaLibrary.createAssetAsync(processedImage);
      await MediaLibrary.createAlbumAsync("ImageToolkit", asset, false);

      Alert.alert("Success", "Image saved to gallery!");
    } catch (error) {
      console.error("Error saving image:", error);
      Alert.alert("Error", "Failed to save image. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const renderEditControls = () => {
    switch (editMode) {
      case "percentage":
        return (
          <View style={styles.editControlsContainer}>
            <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>
              Quality Percentage: {percentage}%
            </Text>

            <View style={styles.percentageControls}>
              <Pressable
                style={[styles.percentageButton, { backgroundColor: isDark ? "#333" : "#f0f0f0" }]}
                onPress={() => setPercentage(Math.max(1, percentage - 10))}>
                <Ionicons name="remove" size={24} color={isDark ? "#fff" : "#000"} />
              </Pressable>

              <View
                style={[
                  styles.percentageDisplay,
                  { backgroundColor: isDark ? "#333" : "#f0f0f0" },
                ]}>
                <Text style={{ color: isDark ? "#fff" : "#000", fontSize: 18, fontWeight: "bold" }}>
                  {percentage}%
                </Text>
              </View>

              <Pressable
                style={[styles.percentageButton, { backgroundColor: isDark ? "#333" : "#f0f0f0" }]}
                onPress={() => setPercentage(Math.min(200, percentage + 10))}>
                <Ionicons name="add" size={24} color={isDark ? "#fff" : "#000"} />
              </Pressable>
            </View>
          </View>
        );

      case "dimensions":
        return (
          <View style={styles.editControlsContainer}>
            <View style={styles.dimensionsContainer}>
              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>Width</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={width}
                  onChangeText={updateWidth}
                  keyboardType="number-pad"
                  placeholder="Width"
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>Height</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={height}
                  onChangeText={updateHeight}
                  keyboardType="number-pad"
                  placeholder="Height"
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>
            </View>

            <Pressable style={styles.aspectRatioButton} onPress={toggleAspectRatio}>
              <Ionicons
                name={maintainAspectRatio ? "lock-closed" : "lock-open"}
                size={18}
                color={maintainAspectRatio ? "#2196F3" : isDark ? "#aaa" : "#888"}
              />
              <Text
                style={[
                  styles.aspectRatioText,
                  {
                    color: maintainAspectRatio ? "#2196F3" : isDark ? "#aaa" : "#888",
                  },
                ]}>
                Maintain Aspect Ratio
              </Text>
            </Pressable>
          </View>
        );

      case "crop":
        return (
          <View style={styles.editControlsContainer}>
            <View style={styles.cropControlsContainer}>
              <View style={styles.cropRow}>
                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    X Position
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropX}
                    onChangeText={setCropX}
                    keyboardType="number-pad"
                    placeholder="X"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    Y Position
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropY}
                    onChangeText={setCropY}
                    keyboardType="number-pad"
                    placeholder="Y"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>
              </View>

              <View style={styles.cropRow}>
                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    Width
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropWidth}
                    onChangeText={setCropWidth}
                    keyboardType="number-pad"
                    placeholder="Width"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>
                    Height
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: isDark ? "#fff" : "#000",
                        backgroundColor: isDark ? "#333" : "#f0f0f0",
                        borderColor: isDark ? "#555" : "#ccc",
                      },
                    ]}
                    value={cropHeight}
                    onChangeText={setCropHeight}
                    keyboardType="number-pad"
                    placeholder="Height"
                    placeholderTextColor={isDark ? "#aaa" : "#888"}
                  />
                </View>
              </View>
            </View>
          </View>
        );
    }
  };

  const getButtonColor = () => {
    switch (editMode) {
      case "percentage":
        return "#4CAF50";
      case "dimensions":
        return "#2196F3";
      case "crop":
        return "#FF9800";
    }
  };

  const getButtonIcon = () => {
    switch (editMode) {
      case "percentage":
        return "contrast-outline";
      case "dimensions":
        return "expand-outline";
      case "crop":
        return "crop-outline";
    }
  };

  const getButtonText = () => {
    switch (editMode) {
      case "percentage":
        return "Adjust Quality";
      case "dimensions":
        return "Resize by Dimensions";
      case "crop":
        return "Crop Image";
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "All Image Tools",
          headerTitleAlign: "center",
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <ImageSelector
          onImageSelected={handleImageSelected}
          selectedImage={processedImage || selectedImage}
        />

        {selectedImage && (
          <View style={styles.controlsContainer}>
            <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>
              Original Size: {originalDimensions.width} × {originalDimensions.height}
            </Text>

            <View style={styles.tabsContainer}>
              <Pressable
                style={[
                  styles.tab,
                  editMode === "percentage" && {
                    backgroundColor: isDark ? "#333" : "#e0e0e0",
                    borderBottomColor: "#4CAF50",
                    borderBottomWidth: 3,
                  },
                ]}
                onPress={() => setEditMode("percentage")}>
                <Ionicons
                  name="contrast-outline"
                  size={20}
                  color={editMode === "percentage" ? "#4CAF50" : isDark ? "#aaa" : "#888"}
                />
                <Text
                  style={[
                    styles.tabText,
                    {
                      color:
                        editMode === "percentage"
                          ? isDark
                            ? "#fff"
                            : "#000"
                          : isDark
                          ? "#aaa"
                          : "#888",
                    },
                  ]}>
                  Quality
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.tab,
                  editMode === "dimensions" && {
                    backgroundColor: isDark ? "#333" : "#e0e0e0",
                    borderBottomColor: "#2196F3",
                    borderBottomWidth: 3,
                  },
                ]}
                onPress={() => setEditMode("dimensions")}>
                <Ionicons
                  name="expand-outline"
                  size={20}
                  color={editMode === "dimensions" ? "#2196F3" : isDark ? "#aaa" : "#888"}
                />
                <Text
                  style={[
                    styles.tabText,
                    {
                      color:
                        editMode === "dimensions"
                          ? isDark
                            ? "#fff"
                            : "#000"
                          : isDark
                          ? "#aaa"
                          : "#888",
                    },
                  ]}>
                  Dimensions
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.tab,
                  editMode === "crop" && {
                    backgroundColor: isDark ? "#333" : "#e0e0e0",
                    borderBottomColor: "#FF9800",
                    borderBottomWidth: 3,
                  },
                ]}
                onPress={() => setEditMode("crop")}>
                <Ionicons
                  name="crop-outline"
                  size={20}
                  color={editMode === "crop" ? "#FF9800" : isDark ? "#aaa" : "#888"}
                />
                <Text
                  style={[
                    styles.tabText,
                    {
                      color:
                        editMode === "crop" ? (isDark ? "#fff" : "#000") : isDark ? "#aaa" : "#888",
                    },
                  ]}>
                  Crop
                </Text>
              </Pressable>
            </View>

            {renderEditControls()}

            <View style={styles.buttonContainer}>
              <Pressable
                style={[
                  styles.button,
                  { backgroundColor: getButtonColor(), opacity: processing ? 0.7 : 1 },
                ]}
                onPress={processImage}
                disabled={processing || !selectedImage}>
                {processing ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Ionicons
                      name={getButtonIcon()}
                      size={20}
                      color="#fff"
                      style={styles.buttonIcon}
                    />
                    <Text style={styles.buttonText}>{getButtonText()}</Text>
                  </>
                )}
              </Pressable>

              {processedImage && (
                <Pressable
                  style={[styles.button, { backgroundColor: "#9C27B0", opacity: saving ? 0.7 : 1 }]}
                  onPress={saveImage}
                  disabled={saving}>
                  {saving ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <Ionicons
                        name="save-outline"
                        size={20}
                        color="#fff"
                        style={styles.buttonIcon}
                      />
                      <Text style={styles.buttonText}>Save to Gallery</Text>
                    </>
                  )}
                </Pressable>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  controlsContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 15,
  },
  tabsContainer: {
    flexDirection: "row",
    width: "100%",
    marginBottom: 20,
    borderRadius: 8,
    overflow: "hidden",
  },
  tab: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  tabText: {
    marginLeft: 5,
    fontSize: 14,
    fontWeight: "500",
  },
  editControlsContainer: {
    width: "100%",
    marginBottom: 20,
  },
  percentageControls: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginBottom: 20,
  },
  percentageButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: "center",
    justifyContent: "center",
  },
  percentageDisplay: {
    width: 100,
    height: 50,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 20,
  },
  dimensionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 15,
  },
  cropControlsContainer: {
    width: "100%",
  },
  cropRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 15,
  },
  inputContainer: {
    width: "48%",
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
  },
  aspectRatioButton: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 5,
  },
  aspectRatioText: {
    marginLeft: 8,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: "column",
    justifyContent: "center",
    width: "100%",
    gap: 10,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
