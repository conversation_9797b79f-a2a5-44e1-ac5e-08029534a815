import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { Pressable, ScrollView, StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useTheme } from "../theme/ThemeContext";

export default function Index() {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: isDark ? "#121212" : "#f9f9f9" }]}
      edges={["bottom"]}>
      <View style={[styles.header, { backgroundColor: isDark ? "#1a1a2e" : "#3498db" }]}>
        <View style={styles.logoContainer}>
          <Ionicons name="image" size={40} color="#ffffff" />
        </View>
        <Text style={styles.title}>Image Toolkit</Text>
        <Text style={styles.subtitle}>Professional image editing tools</Text>
      </View>

      <ScrollView
        style={[styles.scrollView, { backgroundColor: isDark ? "#121212" : "transparent" }]}>
        <Pressable
          style={[styles.card, { backgroundColor: "#4CAF50" }]}
          onPress={() => {
            console.log("Navigating to resize-percentage");
            // Try to navigate using the router
            try {
              router.push("/resize-percentage");
            } catch (error) {
              console.error("Navigation error:", error);
            }
          }}>
          <Ionicons name="contrast-outline" size={24} color="#ffffff" />
          <View style={styles.cardTextContainer}>
            <Text style={styles.cardTitle}>Adjust Quality</Text>
            <Text style={styles.cardDescription}>Change image compression level</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ffffff" />
        </Pressable>

        <Pressable
          style={[styles.card, { backgroundColor: "#2196F3" }]}
          onPress={() => router.push("/resize-dimensions")}>
          <Ionicons name="expand-outline" size={24} color="#ffffff" />
          <View style={styles.cardTextContainer}>
            <Text style={styles.cardTitle}>Resize by Dimensions</Text>
            <Text style={styles.cardDescription}>Set exact width and height</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ffffff" />
        </Pressable>

        <Pressable
          style={[styles.card, { backgroundColor: "#FF9800" }]}
          onPress={() => router.push("/crop")}>
          <Ionicons name="crop-outline" size={24} color="#ffffff" />
          <View style={styles.cardTextContainer}>
            <Text style={styles.cardTitle}>Crop Image</Text>
            <Text style={styles.cardDescription}>Crop to specific area</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ffffff" />
        </Pressable>

        <Pressable
          style={[styles.card, { backgroundColor: "#9C27B0" }]}
          onPress={() => router.push("/filters")}>
          <Ionicons name="color-filter-outline" size={24} color="#ffffff" />
          <View style={styles.cardTextContainer}>
            <Text style={styles.cardTitle}>Image Filters</Text>
            <Text style={styles.cardDescription}>Apply filters and effects</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ffffff" />
        </Pressable>

        <Pressable
          style={[styles.card, { backgroundColor: "#00BCD4" }]}
          onPress={() => router.push("/rotate")}>
          <Ionicons name="refresh-outline" size={24} color="#ffffff" />
          <View style={styles.cardTextContainer}>
            <Text style={styles.cardTitle}>Rotate & Flip</Text>
            <Text style={styles.cardDescription}>Rotate or mirror images</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ffffff" />
        </Pressable>

        <Pressable
          style={[styles.card, { backgroundColor: "#E91E63" }]}
          onPress={() => router.push("/passport-grid")}>
          <Ionicons name="grid-outline" size={24} color="#ffffff" />
          <View style={styles.cardTextContainer}>
            <Text style={styles.cardTitle}>Passport Photo Grid</Text>
            <Text style={styles.cardDescription}>Create printable photo sheets</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ffffff" />
        </Pressable>

        <Pressable
          style={[styles.card, { backgroundColor: "#607D8B" }]}
          onPress={() => router.push("/all-tools")}>
          <Ionicons name="construct-outline" size={24} color="#ffffff" />
          <View style={styles.cardTextContainer}>
            <Text style={styles.cardTitle}>All Tools</Text>
            <Text style={styles.cardDescription}>Access all image tools</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ffffff" />
        </Pressable>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "transparent", // Let the theme handle the background color
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    alignItems: "center",
    paddingTop: 20,
    paddingBottom: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 6,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 6,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#ffffff",
    marginBottom: 6,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.8)",
    marginBottom: 8,
    textShadowColor: "rgba(0, 0, 0, 0.2)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  cardTextContainer: {
    flex: 1,
    marginLeft: 16,
    marginRight: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#ffffff",
    marginBottom: 4,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  cardDescription: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.8)",
  },
});
