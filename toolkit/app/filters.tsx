import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import * as MediaLibrary from "expo-media-library";
import { Stack } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { useTheme } from "../theme/ThemeContext";
import ImageSelector from "../components/ImageSelector";
import CustomSlider from "../components/CustomSlider";

// Define filter types
type FilterType = 
  | "original" 
  | "sepia" 
  | "black-white" 
  | "negative" 
  | "brightness" 
  | "contrast" 
  | "saturation";

interface FilterOption {
  id: FilterType;
  name: string;
  icon: React.ComponentProps<typeof Ionicons>["name"];
  hasIntensity?: boolean;
}

export default function FiltersScreen() {
  const { colors, theme } = useTheme();
  const isDark = theme === "dark";

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [saving, setSaving] = useState(false);
  
  const [selectedFilter, setSelectedFilter] = useState<FilterType>("original");
  const [intensity, setIntensity] = useState(50);

  // Filter options
  const filterOptions: FilterOption[] = [
    { id: "original", name: "Original", icon: "image-outline" },
    { id: "sepia", name: "Sepia", icon: "color-filter-outline" },
    { id: "black-white", name: "B&W", icon: "contrast-outline" },
    { id: "negative", name: "Negative", icon: "invert-mode-outline" },
    { id: "brightness", name: "Brightness", icon: "sunny-outline", hasIntensity: true },
    { id: "contrast", name: "Contrast", icon: "contrast-outline", hasIntensity: true },
    { id: "saturation", name: "Saturation", icon: "color-palette-outline", hasIntensity: true },
  ];

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
    setProcessedImage(null);
    setSelectedFilter("original");
    setIntensity(50);
  };

  const applyFilter = async () => {
    if (!selectedImage) return;

    setProcessing(true);
    try {
      let actions: ImageManipulator.Action[] = [];
      
      switch (selectedFilter) {
        case "original":
          // No filter, just return the original
          setProcessedImage(selectedImage);
          setProcessing(false);
          return;
          
        case "sepia":
          actions = [{ sepia: 1 }];
          break;
          
        case "black-white":
          actions = [{ grayscale: true }];
          break;
          
        case "negative":
          actions = [{ negative: true }];
          break;
          
        case "brightness":
          // Convert intensity from 0-100 to -1 to 1 range
          const brightnessValue = (intensity - 50) / 50;
          actions = [{ brightness: brightnessValue }];
          break;
          
        case "contrast":
          // Convert intensity from 0-100 to 0 to 2 range
          const contrastValue = intensity / 50;
          actions = [{ contrast: contrastValue }];
          break;
          
        case "saturation":
          // Convert intensity from 0-100 to 0 to 2 range
          const saturationValue = intensity / 50;
          actions = [{ saturation: saturationValue }];
          break;
      }

      const result = await ImageManipulator.manipulateAsync(
        selectedImage,
        actions,
        { compress: 1, format: ImageManipulator.SaveFormat.JPEG }
      );

      setProcessedImage(result.uri);
    } catch (error) {
      console.error("Error applying filter:", error);
      Alert.alert("Error", "Failed to apply filter. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const saveImage = async () => {
    if (!processedImage) return;

    setSaving(true);
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant permission to save images to your gallery."
        );
        setSaving(false);
        return;
      }

      // Save the image
      const asset = await MediaLibrary.createAssetAsync(processedImage);
      await MediaLibrary.createAlbumAsync("ImageToolkit", asset, false);

      Alert.alert("Success", "Image saved to gallery!");
    } catch (error) {
      console.error("Error saving image:", error);
      Alert.alert("Error", "Failed to save image. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const renderFilterOption = ({ item }: { item: FilterOption }) => (
    <Pressable
      style={[
        styles.filterOption,
        {
          backgroundColor: selectedFilter === item.id ? colors.primary : colors.card,
          borderColor: colors.border,
        },
      ]}
      onPress={() => {
        setSelectedFilter(item.id);
        if (item.id === "original") {
          setProcessedImage(selectedImage);
        } else {
          setProcessedImage(null);
        }
      }}
    >
      <Ionicons
        name={item.icon}
        size={24}
        color={selectedFilter === item.id ? colors.buttonText : colors.text}
      />
      <Text
        style={[
          styles.filterName,
          {
            color: selectedFilter === item.id ? colors.buttonText : colors.text,
          },
        ]}
      >
        {item.name}
      </Text>
    </Pressable>
  );

  const currentFilter = filterOptions.find(filter => filter.id === selectedFilter);

  return (
    <>
      <Stack.Screen
        options={{
          title: "Image Filters",
          headerTitleAlign: "center",
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <ImageSelector
          onImageSelected={handleImageSelected}
          selectedImage={processedImage || selectedImage}
        />

        {selectedImage && (
          <View style={styles.controlsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Choose a Filter
            </Text>

            <FlatList
              data={filterOptions}
              renderItem={renderFilterOption}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.filtersList}
            />

            {currentFilter?.hasIntensity && (
              <View style={styles.intensityContainer}>
                <Text style={[styles.intensityLabel, { color: colors.text }]}>
                  Intensity
                </Text>
                <CustomSlider
                  minimumValue={0}
                  maximumValue={100}
                  step={5}
                  value={intensity}
                  onValueChange={setIntensity}
                  minimumTrackTintColor={colors.primary}
                  presets={[0, 25, 50, 75, 100]}
                  labelSuffix="%"
                />
              </View>
            )}

            <View style={styles.buttonContainer}>
              {selectedFilter !== "original" && (
                <Pressable
                  style={[
                    styles.button,
                    { backgroundColor: colors.primary, opacity: processing ? 0.7 : 1 },
                  ]}
                  onPress={applyFilter}
                  disabled={processing || !selectedImage}>
                  {processing ? (
                    <ActivityIndicator size="small" color={colors.buttonText} />
                  ) : (
                    <>
                      <Ionicons
                        name="color-wand-outline"
                        size={20}
                        color={colors.buttonText}
                        style={styles.buttonIcon}
                      />
                      <Text style={[styles.buttonText, { color: colors.buttonText }]}>
                        Apply Filter
                      </Text>
                    </>
                  )}
                </Pressable>
              )}

              {processedImage && (
                <Pressable
                  style={[
                    styles.button,
                    { backgroundColor: colors.success, opacity: saving ? 0.7 : 1 },
                  ]}
                  onPress={saveImage}
                  disabled={saving}>
                  {saving ? (
                    <ActivityIndicator size="small" color={colors.buttonText} />
                  ) : (
                    <>
                      <Ionicons
                        name="save-outline"
                        size={20}
                        color={colors.buttonText}
                        style={styles.buttonIcon}
                      />
                      <Text style={[styles.buttonText, { color: colors.buttonText }]}>
                        Save to Gallery
                      </Text>
                    </>
                  )}
                </Pressable>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  controlsContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 15,
    alignSelf: "flex-start",
  },
  filtersList: {
    paddingVertical: 10,
  },
  filterOption: {
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    marginHorizontal: 8,
    borderRadius: 12,
    width: 90,
    height: 90,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  filterName: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: "500",
  },
  intensityContainer: {
    width: "100%",
    marginTop: 20,
  },
  intensityLabel: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 10,
  },
  buttonContainer: {
    flexDirection: "column",
    justifyContent: "center",
    width: "100%",
    marginTop: 20,
    gap: 10,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  buttonText: {
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
