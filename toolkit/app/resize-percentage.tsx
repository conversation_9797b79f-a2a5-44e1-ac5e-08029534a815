import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import * as MediaLibrary from "expo-media-library";
import { Stack } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  useColorScheme,
  View,
} from "react-native";
import ImageSelector from "../components/ImageSelector";

export default function ResizePercentageScreen() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [percentage, setPercentage] = useState(100);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [saving, setSaving] = useState(false);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
    setProcessedImage(null);
  };

  const processImage = async () => {
    if (!selectedImage) return;

    setProcessing(true);
    try {
      // Ensure percentage is greater than 0 to avoid invalid dimensions
      if (percentage <= 0) {
        Alert.alert("Invalid Percentage", "Resize percentage must be greater than 0.");
        setProcessing(false);
        return;
      }

      // Adjust image quality based on percentage
      try {
        // Calculate quality based on percentage (0.01 to 1.00)
        const quality = Math.max(0.01, Math.min(1.0, percentage / 100));

        // Don't resize the image, just adjust the quality/compression
        const result = await ImageManipulator.manipulateAsync(
          selectedImage,
          [], // No resize operations, keep original dimensions
          { compress: quality, format: ImageManipulator.SaveFormat.JPEG }
        );
        setProcessedImage(result.uri);
      } catch (error) {
        console.error("Error in image processing:", error);
        Alert.alert(
          "Error",
          "Failed to adjust image quality. Please try a different percentage value."
        );
        setProcessing(false);
        return;
      }
    } catch (error) {
      console.error("Error processing image:", error);
      Alert.alert("Error", "Failed to process image. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const saveImage = async () => {
    if (!processedImage) return;

    setSaving(true);
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant permission to save images to your gallery."
        );
        setSaving(false);
        return;
      }

      // Save the image
      const asset = await MediaLibrary.createAssetAsync(processedImage);
      await MediaLibrary.createAlbumAsync("ImageToolkit", asset, false);

      Alert.alert("Success", "Image saved to gallery!");
    } catch (error) {
      console.error("Error saving image:", error);
      Alert.alert("Error", "Failed to save image. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "Adjust Quality by Percentage",
          headerTitleAlign: "center",
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <ImageSelector
          onImageSelected={handleImageSelected}
          selectedImage={processedImage || selectedImage}
        />

        {selectedImage && (
          <View style={styles.controlsContainer}>
            <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>
              Quality Percentage: {percentage}%
            </Text>

            <View style={{ width: "100%", paddingHorizontal: 10 }}>
              {/* Preset buttons instead of slider */}
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "center",
                  flexWrap: "wrap",
                  marginVertical: 10,
                  gap: 10,
                }}>
                {[10, 25, 50, 75, 100, 150, 200].map((preset) => (
                  <Pressable
                    key={preset}
                    style={{
                      paddingVertical: 10,
                      paddingHorizontal: 14,
                      backgroundColor:
                        percentage === preset ? "#4CAF50" : isDark ? "#333" : "#f0f0f0",
                      borderRadius: 16,
                      borderWidth: percentage === preset ? 0 : 1,
                      borderColor: isDark ? "#444" : "#ddd",
                      minWidth: 60,
                      alignItems: "center",
                      marginHorizontal: 4,
                    }}
                    onPress={() => setPercentage(preset)}>
                    <Text
                      style={{
                        color: percentage === preset ? "#fff" : isDark ? "#fff" : "#000",
                        fontSize: 14,
                        fontWeight: "500",
                      }}>
                      {preset}%
                    </Text>
                  </Pressable>
                ))}
              </View>

              {/* Manual input buttons */}
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: 15,
                }}>
                <Pressable
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: isDark ? "#333" : "#f0f0f0",
                    alignItems: "center",
                    justifyContent: "center",
                    borderWidth: 1,
                    borderColor: isDark ? "#444" : "#ddd",
                  }}
                  onPress={() => setPercentage(Math.max(1, percentage - 5))}>
                  <Ionicons name="remove" size={24} color={isDark ? "#fff" : "#000"} />
                </Pressable>

                <View
                  style={{
                    paddingHorizontal: 20,
                    paddingVertical: 10,
                    backgroundColor: isDark ? "#333" : "#f0f0f0",
                    borderRadius: 8,
                    marginHorizontal: 15,
                    minWidth: 80,
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: isDark ? "#444" : "#ddd",
                  }}>
                  <Text
                    style={{ color: isDark ? "#fff" : "#000", fontSize: 18, fontWeight: "bold" }}>
                    {percentage}%
                  </Text>
                </View>

                <Pressable
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: isDark ? "#333" : "#f0f0f0",
                    alignItems: "center",
                    justifyContent: "center",
                    borderWidth: 1,
                    borderColor: isDark ? "#444" : "#ddd",
                  }}
                  onPress={() => setPercentage(Math.min(200, percentage + 5))}>
                  <Ionicons name="add" size={24} color={isDark ? "#fff" : "#000"} />
                </Pressable>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Pressable
                style={[
                  styles.button,
                  { backgroundColor: "#4CAF50", opacity: processing ? 0.7 : 1 },
                ]}
                onPress={processImage}
                disabled={processing || !selectedImage}>
                {processing ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Ionicons
                      name="contrast-outline"
                      size={20}
                      color="#fff"
                      style={styles.buttonIcon}
                    />
                    <Text style={styles.buttonText}>Adjust Quality</Text>
                  </>
                )}
              </Pressable>

              {processedImage && (
                <Pressable
                  style={[styles.button, { backgroundColor: "#2196F3", opacity: saving ? 0.7 : 1 }]}
                  onPress={saveImage}
                  disabled={saving}>
                  {saving ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <Ionicons
                        name="save-outline"
                        size={20}
                        color="#fff"
                        style={styles.buttonIcon}
                      />
                      <Text style={styles.buttonText}>Save to Gallery</Text>
                    </>
                  )}
                </Pressable>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  controlsContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 10,
  },

  buttonContainer: {
    flexDirection: "column",
    justifyContent: "center",
    width: "100%",
    marginTop: 20,
    gap: 10,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
