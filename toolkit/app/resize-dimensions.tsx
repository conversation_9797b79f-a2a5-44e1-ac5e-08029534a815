import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import * as MediaLibrary from "expo-media-library";
import { Stack } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  useColorScheme,
  View,
} from "react-native";
import ImageSelector from "../components/ImageSelector";

export default function ResizeDimensionsScreen() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 });
  const [width, setWidth] = useState("");
  const [height, setHeight] = useState("");
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [saving, setSaving] = useState(false);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  useEffect(() => {
    if (selectedImage) {
      // Get original image dimensions
      Image.getSize(selectedImage, (width, height) => {
        setOriginalDimensions({ width, height });
        setWidth(width.toString());
        setHeight(height.toString());
      });
    }
  }, [selectedImage]);

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
    setProcessedImage(null);
  };

  const updateWidth = (text: string) => {
    setWidth(text);
    if (maintainAspectRatio && originalDimensions.width > 0 && text) {
      const aspectRatio = originalDimensions.height / originalDimensions.width;
      const newHeight = Math.round(parseInt(text) * aspectRatio);
      setHeight(newHeight.toString());
    }
  };

  const updateHeight = (text: string) => {
    setHeight(text);
    if (maintainAspectRatio && originalDimensions.height > 0 && text) {
      const aspectRatio = originalDimensions.width / originalDimensions.height;
      const newWidth = Math.round(parseInt(text) * aspectRatio);
      setWidth(newWidth.toString());
    }
  };

  const toggleAspectRatio = () => {
    setMaintainAspectRatio(!maintainAspectRatio);
  };

  const processImage = async () => {
    if (!selectedImage || !width || !height) return;

    setProcessing(true);
    try {
      const newWidth = parseInt(width);
      const newHeight = parseInt(height);

      if (isNaN(newWidth) || isNaN(newHeight) || newWidth <= 0 || newHeight <= 0) {
        Alert.alert("Invalid Dimensions", "Please enter valid width and height values.");
        setProcessing(false);
        return;
      }

      const result = await ImageManipulator.manipulateAsync(
        selectedImage,
        [{ resize: { width: newWidth, height: newHeight } }],
        { compress: 1, format: ImageManipulator.SaveFormat.JPEG }
      );

      setProcessedImage(result.uri);
    } catch (error) {
      console.error("Error processing image:", error);
      Alert.alert("Error", "Failed to process image. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const saveImage = async () => {
    if (!processedImage) return;

    setSaving(true);
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant permission to save images to your gallery."
        );
        setSaving(false);
        return;
      }

      // Save the image
      const asset = await MediaLibrary.createAssetAsync(processedImage);
      await MediaLibrary.createAlbumAsync("ImageToolkit", asset, false);

      Alert.alert("Success", "Image saved to gallery!");
    } catch (error) {
      console.error("Error saving image:", error);
      Alert.alert("Error", "Failed to save image. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "Resize by Dimensions",
          headerTitleAlign: "center",
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <ImageSelector
          onImageSelected={handleImageSelected}
          selectedImage={processedImage || selectedImage}
        />

        {selectedImage && (
          <View style={styles.controlsContainer}>
            <Text style={[styles.label, { color: isDark ? "#fff" : "#000" }]}>
              Original Size: {originalDimensions.width} × {originalDimensions.height}
            </Text>

            <View style={styles.dimensionsContainer}>
              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>Width</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={width}
                  onChangeText={updateWidth}
                  keyboardType="number-pad"
                  placeholder="Width"
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: isDark ? "#fff" : "#000" }]}>Height</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: isDark ? "#fff" : "#000",
                      backgroundColor: isDark ? "#333" : "#f0f0f0",
                      borderColor: isDark ? "#555" : "#ccc",
                    },
                  ]}
                  value={height}
                  onChangeText={updateHeight}
                  keyboardType="number-pad"
                  placeholder="Height"
                  placeholderTextColor={isDark ? "#aaa" : "#888"}
                />
              </View>
            </View>

            <Pressable style={styles.aspectRatioButton} onPress={toggleAspectRatio}>
              <Ionicons
                name={maintainAspectRatio ? "lock-closed" : "lock-open"}
                size={18}
                color={maintainAspectRatio ? "#2196F3" : isDark ? "#aaa" : "#888"}
              />
              <Text
                style={[
                  styles.aspectRatioText,
                  {
                    color: maintainAspectRatio ? "#2196F3" : isDark ? "#aaa" : "#888",
                  },
                ]}>
                Maintain Aspect Ratio
              </Text>
            </Pressable>

            <View style={styles.buttonContainer}>
              <Pressable
                style={[
                  styles.button,
                  { backgroundColor: "#2196F3", opacity: processing ? 0.7 : 1 },
                ]}
                onPress={processImage}
                disabled={processing || !selectedImage || !width || !height}>
                {processing ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Ionicons
                      name="expand-outline"
                      size={20}
                      color="#fff"
                      style={styles.buttonIcon}
                    />
                    <Text style={styles.buttonText}>Resize Image</Text>
                  </>
                )}
              </Pressable>

              {processedImage && (
                <Pressable
                  style={[styles.button, { backgroundColor: "#4CAF50", opacity: saving ? 0.7 : 1 }]}
                  onPress={saveImage}
                  disabled={saving}>
                  {saving ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <Ionicons
                        name="save-outline"
                        size={20}
                        color="#fff"
                        style={styles.buttonIcon}
                      />
                      <Text style={styles.buttonText}>Save to Gallery</Text>
                    </>
                  )}
                </Pressable>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: "center",
  },
  controlsContainer: {
    width: "100%",
    marginTop: 20,
    alignItems: "center",
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 15,
  },
  dimensionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 15,
  },
  inputContainer: {
    width: "48%",
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
  },
  aspectRatioButton: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  aspectRatioText: {
    marginLeft: 8,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: "column",
    justifyContent: "center",
    width: "100%",
    gap: 10,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    width: "100%",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
});
