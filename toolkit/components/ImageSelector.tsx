import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import * as ImagePicker from "expo-image-picker";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  Image,
  Modal,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { PinchGestureHandler, State } from "react-native-gesture-handler";
import { useTheme } from "../theme/ThemeContext";

interface ImageSelectorProps {
  onImageSelected: (uri: string) => void;
  selectedImage: string | null;
}

export default function ImageSelector({ onImageSelected, selectedImage }: ImageSelectorProps) {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [scale] = useState(new Animated.Value(1));
  const { colors, theme } = useTheme();
  const isDark = theme === "dark";

  const onPinchEvent = Animated.event([{ nativeEvent: { scale } }], { useNativeDriver: true });

  const onPinchStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
        bounciness: 1,
      }).start();
    }
  };

  const pickImageFromGallery = async () => {
    setLoading(true);
    try {
      // Request permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "We need access to your photo library to select images.",
          [{ text: "OK" }]
        );
        setLoading(false);
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsEditing: false,
        quality: 1,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert("Error", "Failed to pick image. Please try again.");
    } finally {
      setLoading(false);
      setModalVisible(false);
    }
  };

  const takePhoto = async () => {
    setLoading(true);
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== "granted") {
        Alert.alert("Permission Required", "We need access to your camera to take photos.", [
          { text: "OK" },
        ]);
        setLoading(false);
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert("Error", "Failed to take photo. Please try again.");
    } finally {
      setLoading(false);
      setModalVisible(false);
    }
  };

  const openImageOptions = () => {
    setModalVisible(true);
  };

  return (
    <View style={styles.container}>
      {selectedImage ? (
        <View style={styles.imageContainer}>
          <PinchGestureHandler
            onGestureEvent={onPinchEvent}
            onHandlerStateChange={onPinchStateChange}>
            <Animated.View style={{ transform: [{ scale }] }}>
              <Image
                source={{ uri: selectedImage }}
                style={[
                  styles.image,
                  {
                    borderColor: colors.border,
                    shadowColor: colors.shadow,
                  },
                ]}
              />
            </Animated.View>
          </PinchGestureHandler>
          <Text style={[styles.zoomHint, { color: colors.subtext }]}>Pinch to zoom</Text>
          <Pressable
            style={[
              styles.changeButton,
              {
                backgroundColor: colors.primary,
                shadowColor: colors.shadow,
              },
            ]}
            onPress={openImageOptions}>
            <Ionicons name="camera" size={16} color={colors.buttonText} style={styles.buttonIcon} />
            <Text style={[styles.buttonText, { color: colors.buttonText }]}>Change Image</Text>
          </Pressable>
        </View>
      ) : (
        <Pressable
          style={[
            styles.selectButton,
            {
              backgroundColor: colors.primary,
              shadowColor: colors.shadow,
            },
          ]}
          onPress={openImageOptions}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={colors.buttonText} />
          ) : (
            <>
              <Ionicons name="image" size={24} color={colors.buttonText} style={styles.icon} />
              <Text style={[styles.buttonText, { color: colors.buttonText }]}>Select an Image</Text>
            </>
          )}
        </Pressable>
      )}

      {/* Image Source Selection Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <Pressable style={styles.modalOverlay} onPress={() => setModalVisible(false)}>
          <BlurView intensity={30} style={StyleSheet.absoluteFill} />
          <Pressable
            style={[
              styles.modalContent,
              {
                backgroundColor: isDark ? "#2a2a2a" : "rgba(255, 255, 255, 0.95)",
                borderColor: isDark ? "#444" : "transparent",
                borderWidth: isDark ? 1 : 0,
              },
            ]}
            onPress={(e) => e.stopPropagation()}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Select Image Source</Text>

            <Pressable
              style={[
                styles.modalButton,
                {
                  backgroundColor: isDark ? "#333" : colors.card,
                  borderColor: isDark ? "#444" : colors.border,
                },
              ]}
              onPress={takePhoto}>
              <Ionicons name="camera" size={24} color={colors.primary} style={styles.modalIcon} />
              <Text style={[styles.modalButtonText, { color: colors.text }]}>Take Photo</Text>
            </Pressable>

            <Pressable
              style={[
                styles.modalButton,
                {
                  backgroundColor: isDark ? "#333" : colors.card,
                  borderColor: isDark ? "#444" : colors.border,
                },
              ]}
              onPress={pickImageFromGallery}>
              <Ionicons name="images" size={24} color={colors.primary} style={styles.modalIcon} />
              <Text style={[styles.modalButtonText, { color: colors.text }]}>
                Choose from Gallery
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.cancelButton,
                {
                  backgroundColor: isDark ? "#222" : colors.card,
                  borderColor: isDark ? "#444" : colors.border,
                },
              ]}
              onPress={() => setModalVisible(false)}>
              <Text style={[styles.cancelButtonText, { color: colors.error }]}>Cancel</Text>
            </Pressable>
          </Pressable>
        </Pressable>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    marginVertical: 20,
  },
  selectButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    width: "80%",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    elevation: 4,
  },
  icon: {
    marginRight: 10,
  },
  imageContainer: {
    width: "90%",
    alignItems: "center",
  },
  image: {
    width: 300,
    height: 300,
    borderRadius: 16,
    resizeMode: "contain",
    borderWidth: 1,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 5,
    backgroundColor: "transparent",
  },
  zoomHint: {
    marginTop: 8,
    fontSize: 12,
    fontStyle: "italic",
  },
  changeButton: {
    marginTop: 16,
    padding: 14,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontWeight: "600",
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: "85%",
    padding: 24,
    borderRadius: 20,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 20,
  },
  modalButton: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  modalIcon: {
    marginRight: 10,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  cancelButton: {
    width: "100%",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 8,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
});
