import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import React, { useEffect, useState } from "react";
import { Animated, Easing, Pressable, StyleSheet, Text, View } from "react-native";
import { useTheme } from "../theme/ThemeContext";

interface CustomSliderProps {
  minimumValue: number;
  maximumValue: number;
  step: number;
  value: number;
  onValueChange: (value: number) => void;
  minimumTrackTintColor?: string;
  maximumTrackTintColor?: string;
  thumbTintColor?: string;
  showLabel?: boolean;
  labelSuffix?: string;
  presets?: number[];
  style?: any;
}

const CustomSlider: React.FC<CustomSliderProps> = ({
  minimumValue,
  maximumValue,
  step,
  value,
  onValueChange,
  minimumTrackTintColor,
  maximumTrackTintColor,
  thumbTintColor,
  showLabel = true,
  labelSuffix = "",
  presets,
  style,
}) => {
  const { colors, theme } = useTheme();
  const isDark = theme === "dark";

  const [sliderWidth, setSliderWidth] = useState(0);
  const [thumbPosition] = useState(new Animated.Value(0));
  const [thumbScale] = useState(new Animated.Value(1));
  const [labelOpacity] = useState(new Animated.Value(0));

  // Use theme colors if not provided
  const minTrackColor = minimumTrackTintColor || colors.primary;
  const maxTrackColor = maximumTrackTintColor || (isDark ? "#555" : "#e0e0e0");
  const thumbColor = thumbTintColor || colors.primary;

  // Calculate the initial position of the thumb
  const calculateThumbPosition = (val: number) => {
    if (sliderWidth === 0) return 0;
    const percentage = (val - minimumValue) / (maximumValue - minimumValue);
    return percentage * sliderWidth;
  };

  // Calculate value from position
  const calculateValueFromPosition = (position: number) => {
    const percentage = Math.max(0, Math.min(1, position / sliderWidth));
    const newValue = minimumValue + percentage * (maximumValue - minimumValue);
    return Math.round(newValue / step) * step;
  };

  // Update the thumb position when the value changes
  useEffect(() => {
    if (sliderWidth > 0) {
      const position = calculateThumbPosition(value);
      thumbPosition.setValue(position);
    }
  }, [value, sliderWidth]);

  // Animate thumb on touch
  const animateThumbScale = (active: boolean) => {
    Animated.spring(thumbScale, {
      toValue: active ? 1.3 : 1,
      useNativeDriver: true,
      friction: 7,
    }).start();
  };

  // Animate label visibility
  const animateLabel = (visible: boolean) => {
    Animated.timing(labelOpacity, {
      toValue: visible ? 1 : 0,
      duration: 200,
      easing: Easing.ease,
      useNativeDriver: true,
    }).start();
  };

  // Create a pan responder for the thumb
  const thumbPanResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        // Handle the start of the gesture
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        animateThumbScale(true);
        animateLabel(true);
      },
      onPanResponderMove: (_, gestureState) => {
        if (sliderWidth === 0) return;

        // Store the initial position when the drag starts
        const initialPosition = calculateThumbPosition(value);

        // Calculate new position based on drag
        const newPosition = Math.max(0, Math.min(sliderWidth, initialPosition + gestureState.dx));

        // Calculate and update the value
        const newValue = calculateValueFromPosition(newPosition);
        onValueChange(newValue);
      },
      onPanResponderRelease: () => {
        // Handle the end of the gesture
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        animateThumbScale(false);

        // Hide label after a delay
        setTimeout(() => {
          animateLabel(false);
        }, 1000);
      },
    })
  ).current;

  // Handle slider track press
  const handleTrackPress = (event: any) => {
    if (sliderWidth === 0) return;

    // Get the position of the press relative to the slider
    const locationX = event.nativeEvent.locationX;

    // Calculate the new value based on the press position
    const newValue = calculateValueFromPosition(locationX);

    // Update the value
    onValueChange(newValue);

    // Provide haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Animate the thumb and show the label
    animateThumbScale(true);
    animateLabel(true);

    // Reset animations after a delay
    setTimeout(() => {
      animateThumbScale(false);
      setTimeout(() => {
        animateLabel(false);
      }, 1000);
    }, 100);
  };

  // Handle preset selection
  const handlePresetPress = (presetValue: number) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onValueChange(presetValue);
  };

  return (
    <View style={[styles.container, style]}>
      {/* Value label */}
      {showLabel && (
        <Animated.View
          style={[
            styles.labelContainer,
            {
              opacity: labelOpacity,
              backgroundColor: colors.card,
              borderColor: colors.border,
              transform: [
                { translateX: Animated.subtract(thumbPosition, 25) },
                { translateY: -45 },
              ],
            },
          ]}>
          <Text style={[styles.labelText, { color: colors.text }]}>
            {value}
            {labelSuffix}
          </Text>
          <View style={[styles.labelArrow, { borderTopColor: colors.card }]} />
        </Animated.View>
      )}

      {/* Main slider */}
      <View
        style={[styles.sliderContainer]}
        onLayout={(event) => {
          const { width } = event.nativeEvent.layout;
          setSliderWidth(width);
          const position = calculateThumbPosition(value);
          thumbPosition.setValue(position);
        }}>
        <Pressable style={styles.trackPressable} onPress={handleTrackPress}>
          <View style={[styles.track, { backgroundColor: maxTrackColor }]} />
          <Animated.View
            style={[
              styles.filledTrack,
              {
                backgroundColor: minTrackColor,
                width: thumbPosition,
              },
            ]}
          />
        </Pressable>
        <Animated.View
          {...thumbPanResponder.panHandlers}
          style={[
            styles.thumb,
            {
              backgroundColor: thumbColor,
              transform: [{ translateX: thumbPosition }, { scale: thumbScale }],
              shadowColor: colors.shadow,
            },
          ]}
        />
      </View>

      {/* Presets if provided */}
      {presets && presets.length > 0 && (
        <View style={styles.presetsContainer}>
          {presets.map((preset, index) => (
            <Pressable
              key={index}
              style={[
                styles.presetButton,
                {
                  backgroundColor: value === preset ? minTrackColor : colors.card,
                  borderColor: colors.border,
                },
              ]}
              onPress={() => handlePresetPress(preset)}>
              <Text
                style={[
                  styles.presetText,
                  {
                    color: value === preset ? colors.buttonText : colors.text,
                  },
                ]}>
                {preset}
                {labelSuffix}
              </Text>
            </Pressable>
          ))}
        </View>
      )}

      {/* Min/Max controls */}
      <View style={styles.controlsContainer}>
        <Pressable
          style={[
            styles.controlButton,
            { backgroundColor: colors.card, borderColor: colors.border },
          ]}
          onPress={() => {
            const newValue = Math.max(minimumValue, value - step);
            onValueChange(newValue);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}>
          <Ionicons name="remove" size={18} color={colors.text} />
        </Pressable>

        <Text style={[styles.valueText, { color: colors.text }]}>
          {value}
          {labelSuffix}
        </Text>

        <Pressable
          style={[
            styles.controlButton,
            { backgroundColor: colors.card, borderColor: colors.border },
          ]}
          onPress={() => {
            const newValue = Math.min(maximumValue, value + step);
            onValueChange(newValue);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}>
          <Ionicons name="add" size={18} color={colors.text} />
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginVertical: 10,
  },
  sliderContainer: {
    height: 40,
    justifyContent: "center",
    width: "100%",
  },
  trackPressable: {
    width: "100%",
    height: 40,
    justifyContent: "center",
    zIndex: 1,
  },
  track: {
    height: 8,
    borderRadius: 4,
    width: "100%",
  },
  filledTrack: {
    height: 8,
    borderRadius: 4,
    position: "absolute",
  },
  thumb: {
    width: 32,
    height: 32,
    borderRadius: 16,
    position: "absolute",
    top: 4,
    left: -16, // Center the thumb on the track
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.35,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.8)",
    zIndex: 2, // Ensure thumb is above track
  },
  labelContainer: {
    position: "absolute",
    padding: 10,
    borderRadius: 10,
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 10,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  labelText: {
    fontSize: 14,
    fontWeight: "600",
  },
  labelArrow: {
    position: "absolute",
    bottom: -10,
    width: 0,
    height: 0,
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopWidth: 10,
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
  },
  presetsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 15,
    flexWrap: "wrap",
  },
  presetButton: {
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderRadius: 16,
    marginBottom: 8,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  presetText: {
    fontSize: 12,
    fontWeight: "500",
  },
  controlsContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 15,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 2,
  },
  valueText: {
    fontSize: 16,
    fontWeight: "600",
    marginHorizontal: 15,
  },
});

export default CustomSlider;
