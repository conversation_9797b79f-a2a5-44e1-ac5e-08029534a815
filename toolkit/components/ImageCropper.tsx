import { Ionicons } from "@expo/vector-icons";
import * as ImageManipulator from "expo-image-manipulator";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Image,
  PanResponder,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";

interface ImageCropperProps {
  imageUri: string;
  onCropComplete: (croppedUri: string) => void;
  onCancel: () => void;
  aspectRatio?: number;
}

const ImageCropper: React.FC<ImageCropperProps> = ({
  imageUri,
  onCropComplete,
  onCancel,
  aspectRatio = 35 / 45, // Default passport photo aspect ratio
}) => {
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [cropRect, setCropRect] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [processing, setProcessing] = useState(false);

  // Initialize crop rectangle when image loads
  useEffect(() => {
    if (imageUri) {
      Image.getSize(imageUri, (width, height) => {
        setImageSize({ width, height });
      });
    }
  }, [imageUri]);

  // Update crop rectangle when container or image size changes
  useEffect(() => {
    if (
      containerSize.width > 0 &&
      containerSize.height > 0 &&
      imageSize.width > 0 &&
      imageSize.height > 0
    ) {
      // Calculate the scale to fit the image in the container
      const scale = Math.min(
        containerSize.width / imageSize.width,
        containerSize.height / imageSize.height
      );

      // Calculate the displayed image dimensions
      const displayWidth = imageSize.width * scale;
      const displayHeight = imageSize.height * scale;

      // Calculate initial crop rectangle size (50% of the smaller dimension)
      let cropWidth, cropHeight;

      if (aspectRatio) {
        // If aspect ratio is specified, use it to determine crop dimensions
        if (displayWidth / displayHeight > aspectRatio) {
          // Image is wider than the target aspect ratio
          cropHeight = displayHeight * 0.8;
          cropWidth = cropHeight * aspectRatio;
        } else {
          // Image is taller than the target aspect ratio
          cropWidth = displayWidth * 0.8;
          cropHeight = cropWidth / aspectRatio;
        }
      } else {
        // No aspect ratio, use square crop
        cropWidth = cropHeight = Math.min(displayWidth, displayHeight) * 0.5;
      }

      // Center the crop rectangle
      const x = (containerSize.width - cropWidth) / 2;
      const y = (containerSize.height - cropHeight) / 2;

      setCropRect({ x, y, width: cropWidth, height: cropHeight });
    }
  }, [containerSize, imageSize, aspectRatio]);

  // Create pan responders for each corner of the crop rectangle
  const createCornerPanResponder = (
    corner: "topLeft" | "topRight" | "bottomLeft" | "bottomRight"
  ) => {
    return PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        const { dx, dy } = gestureState;

        setCropRect((prev) => {
          let newRect = { ...prev };

          // Calculate new values based on which corner is being dragged
          switch (corner) {
            case "topLeft":
              newRect = {
                x: Math.max(0, Math.min(prev.x + dx, prev.x + prev.width - 50)),
                y: Math.max(0, Math.min(prev.y + dy, prev.y + prev.height - 50)),
                width: Math.max(50, prev.width - dx),
                height: Math.max(50, prev.height - dy),
              };
              break;
            case "topRight":
              newRect = {
                x: prev.x,
                y: Math.max(0, Math.min(prev.y + dy, prev.y + prev.height - 50)),
                width: Math.max(50, prev.width + dx),
                height: Math.max(50, prev.height - dy),
              };
              break;
            case "bottomLeft":
              newRect = {
                x: Math.max(0, Math.min(prev.x + dx, prev.x + prev.width - 50)),
                y: prev.y,
                width: Math.max(50, prev.width - dx),
                height: Math.max(50, prev.height + dy),
              };
              break;
            case "bottomRight":
              newRect = {
                x: prev.x,
                y: prev.y,
                width: Math.max(50, prev.width + dx),
                height: Math.max(50, prev.height + dy),
              };
              break;
          }

          // If aspect ratio is specified, maintain it
          if (aspectRatio) {
            if (corner === "topLeft" || corner === "bottomRight") {
              newRect.height = newRect.width / aspectRatio;
            } else {
              newRect.width = newRect.height * aspectRatio;
            }
          }

          // Ensure crop rectangle stays within image bounds
          newRect.x = Math.max(0, Math.min(newRect.x, containerSize.width - newRect.width));
          newRect.y = Math.max(0, Math.min(newRect.y, containerSize.height - newRect.height));
          newRect.width = Math.min(newRect.width, containerSize.width - newRect.x);
          newRect.height = Math.min(newRect.height, containerSize.height - newRect.y);

          return newRect;
        });
      },
      onPanResponderRelease: () => {
        // Handle release if needed
      },
    });
  };

  // Create pan responders for each corner
  const topLeftPanResponder = createCornerPanResponder("topLeft");
  const topRightPanResponder = createCornerPanResponder("topRight");
  const bottomLeftPanResponder = createCornerPanResponder("bottomLeft");
  const bottomRightPanResponder = createCornerPanResponder("bottomRight");

  // Create pan responder for moving the entire crop rectangle
  const cropRectPanResponder = PanResponder.create({
    onStartShouldSetPanResponder: (evt, gestureState) => {
      // Only handle if touch is inside crop rect but not on corners
      const { locationX, locationY } = evt.nativeEvent;
      return (
        locationX > cropRect.x + 20 &&
        locationX < cropRect.x + cropRect.width - 20 &&
        locationY > cropRect.y + 20 &&
        locationY < cropRect.y + cropRect.height - 20
      );
    },
    onPanResponderMove: (_, gestureState) => {
      const { dx, dy } = gestureState;

      setCropRect((prev) => {
        // Calculate new position
        let newX = prev.x + dx;
        let newY = prev.y + dy;

        // Ensure crop rectangle stays within image bounds
        newX = Math.max(0, Math.min(newX, containerSize.width - prev.width));
        newY = Math.max(0, Math.min(newY, containerSize.height - prev.height));

        return {
          ...prev,
          x: newX,
          y: newY,
        };
      });
    },
    onPanResponderRelease: () => {
      // Handle release if needed
    },
  });

  const handleCrop = async () => {
    if (!imageUri) return;

    setProcessing(true);

    try {
      // Calculate the scale to fit the image in the container
      const scale = Math.min(
        containerSize.width / imageSize.width,
        containerSize.height / imageSize.height
      );

      // Calculate the displayed image dimensions
      const displayWidth = imageSize.width * scale;
      const displayHeight = imageSize.height * scale;

      // Calculate offsets if the image doesn't fill the container
      const offsetX = (containerSize.width - displayWidth) / 2;
      const offsetY = (containerSize.height - displayHeight) / 2;

      // Calculate the crop coordinates relative to the original image
      // First, adjust for the offset to get coordinates relative to the displayed image
      const adjustedX = Math.max(0, cropRect.x - offsetX);
      const adjustedY = Math.max(0, cropRect.y - offsetY);

      // Then convert to original image coordinates
      const originX = Math.floor(adjustedX / scale);
      const originY = Math.floor(adjustedY / scale);
      const width = Math.floor(cropRect.width / scale);
      const height = Math.floor(cropRect.height / scale);

      // Ensure the crop dimensions are valid
      const finalOriginX = Math.max(0, Math.min(originX, imageSize.width - 1));
      const finalOriginY = Math.max(0, Math.min(originY, imageSize.height - 1));
      const finalWidth = Math.max(1, Math.min(width, imageSize.width - finalOriginX));
      const finalHeight = Math.max(1, Math.min(height, imageSize.height - finalOriginY));

      console.log("Crop parameters:", {
        originX: finalOriginX,
        originY: finalOriginY,
        width: finalWidth,
        height: finalHeight,
      });

      // Perform the crop
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          {
            crop: {
              originX: finalOriginX,
              originY: finalOriginY,
              width: finalWidth,
              height: finalHeight,
            },
          },
        ],
        { format: ImageManipulator.SaveFormat.JPEG, compress: 1 }
      );

      onCropComplete(result.uri);
    } catch (error) {
      console.error("Error cropping image:", error);
      // If there's an error, just return the original image
      onCropComplete(imageUri);
    } finally {
      setProcessing(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Crop Your Photo</Text>
      <Text style={styles.subtitle}>Drag the corners to adjust the crop area</Text>

      <View
        style={styles.imageContainer}
        onLayout={(event) => {
          const { width, height } = event.nativeEvent.layout;
          setContainerSize({ width, height });
        }}
        {...cropRectPanResponder.panHandlers}>
        {imageUri ? (
          <>
            <Image source={{ uri: imageUri }} style={styles.image} resizeMode="contain" />

            {/* Crop rectangle overlay */}
            <View
              style={[
                styles.cropRect,
                {
                  left: cropRect.x,
                  top: cropRect.y,
                  width: cropRect.width,
                  height: cropRect.height,
                },
              ]}>
              {/* Corner handles */}
              <View
                style={[styles.cornerHandle, styles.topLeftHandle]}
                {...topLeftPanResponder.panHandlers}
              />
              <View
                style={[styles.cornerHandle, styles.topRightHandle]}
                {...topRightPanResponder.panHandlers}
              />
              <View
                style={[styles.cornerHandle, styles.bottomLeftHandle]}
                {...bottomLeftPanResponder.panHandlers}
              />
              <View
                style={[styles.cornerHandle, styles.bottomRightHandle]}
                {...bottomRightPanResponder.panHandlers}
              />
            </View>

            {/* Semi-transparent overlay outside crop area */}
            <View style={[styles.overlay, { top: 0, left: 0, right: 0, height: cropRect.y }]} />
            <View
              style={[
                styles.overlay,
                {
                  top: cropRect.y,
                  left: 0,
                  width: cropRect.x,
                  height: cropRect.height,
                },
              ]}
            />
            <View
              style={[
                styles.overlay,
                {
                  top: cropRect.y,
                  left: cropRect.x + cropRect.width,
                  right: 0,
                  height: cropRect.height,
                },
              ]}
            />
            <View
              style={[
                styles.overlay,
                {
                  top: cropRect.y + cropRect.height,
                  left: 0,
                  right: 0,
                  bottom: 0,
                },
              ]}
            />
          </>
        ) : (
          <ActivityIndicator size="large" color="#4CAF50" />
        )}
      </View>

      <View style={styles.buttonContainer}>
        <Pressable style={styles.cancelButton} onPress={onCancel}>
          <Ionicons name="close-outline" size={20} color="#fff" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>Cancel</Text>
        </Pressable>

        <Pressable
          style={[styles.cropButton, { opacity: processing ? 0.7 : 1 }]}
          onPress={handleCrop}
          disabled={processing}>
          {processing ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons name="crop-outline" size={20} color="#fff" style={styles.buttonIcon} />
              <Text style={styles.buttonText}>Crop</Text>
            </>
          )}
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: "#fff",
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#aaa",
    textAlign: "center",
    marginBottom: 20,
  },
  imageContainer: {
    flex: 1,
    position: "relative",
    overflow: "hidden",
    backgroundColor: "#111",
    borderRadius: 8,
    marginBottom: 20,
  },
  image: {
    width: "100%",
    height: "100%",
  },
  cropRect: {
    position: "absolute",
    borderWidth: 2,
    borderColor: "#fff",
    borderStyle: "dashed",
  },
  cornerHandle: {
    position: "absolute",
    width: 30,
    height: 30,
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    borderRadius: 15,
  },
  topLeftHandle: {
    top: -15,
    left: -15,
  },
  topRightHandle: {
    top: -15,
    right: -15,
  },
  bottomLeftHandle: {
    bottom: -15,
    left: -15,
  },
  bottomRightHandle: {
    bottom: -15,
    right: -15,
  },
  overlay: {
    position: "absolute",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
  },
  cropButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#4CAF50",
    padding: 15,
    borderRadius: 10,
    marginLeft: 10,
  },
  cancelButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#f44336",
    padding: 15,
    borderRadius: 10,
    marginRight: 10,
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
});

export default ImageCropper;
